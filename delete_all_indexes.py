#!/usr/bin/env python3
"""
删除数据库中所有索引的脚本
用于测试应用启动时是否会自动创建索引
"""

import sqlite3
import os
import sys
from pathlib import Path


def get_database_path():
    """获取数据库文件路径"""
    # 从.env文件中读取数据库路径
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip().startswith('TORTOISE_ORM='):
                    # 解析TORTOISE_ORM配置中的file_path
                    if 'app_system.sqlite3' in line:
                        return "app_system.sqlite3"
    
    # 默认路径
    return "app_system.sqlite3"


def connect_to_database(db_path):
    """连接到数据库"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        print(f"✅ 成功连接到数据库: {db_path}")
        return conn
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None


def get_all_indexes(conn):
    """获取数据库中所有的索引"""
    cursor = conn.cursor()
    
    # 查询所有索引（排除主键索引和自动创建的索引）
    query = """
    SELECT name, tbl_name, sql 
    FROM sqlite_master 
    WHERE type = 'index' 
    AND name NOT LIKE 'sqlite_%'
    AND name NOT LIKE '%_pk'
    AND sql IS NOT NULL
    ORDER BY tbl_name, name
    """
    
    cursor.execute(query)
    indexes = cursor.fetchall()
    
    return indexes


def delete_index(conn, index_name):
    """删除指定的索引"""
    try:
        cursor = conn.cursor()
        cursor.execute(f"DROP INDEX IF EXISTS {index_name}")
        conn.commit()
        return True
    except Exception as e:
        print(f"❌ 删除索引 {index_name} 失败: {e}")
        return False


def main():
    """主函数"""
    print("🗄️ 开始删除数据库中的所有索引...")
    print("=" * 50)
    
    # 获取数据库路径
    db_path = get_database_path()
    print(f"📍 数据库路径: {db_path}")
    
    # 连接数据库
    conn = connect_to_database(db_path)
    if not conn:
        sys.exit(1)
    
    try:
        # 获取所有索引
        print("\n🔍 查询数据库中的所有索引...")
        indexes = get_all_indexes(conn)
        
        if not indexes:
            print("ℹ️ 数据库中没有找到需要删除的索引")
            return
        
        print(f"📊 找到 {len(indexes)} 个索引:")
        print("-" * 50)
        
        # 显示所有索引
        for index_name, table_name, sql in indexes:
            print(f"📋 表: {table_name:<25} 索引: {index_name}")
        
        print("-" * 50)
        
        # 确认删除
        response = input(f"\n⚠️ 确定要删除这 {len(indexes)} 个索引吗？(y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ 操作已取消")
            return
        
        # 删除索引
        print(f"\n🗑️ 开始删除索引...")
        deleted_count = 0
        failed_count = 0
        
        for index_name, table_name, sql in indexes:
            print(f"🔄 删除索引: {index_name} (表: {table_name})")
            if delete_index(conn, index_name):
                print(f"✅ 成功删除索引: {index_name}")
                deleted_count += 1
            else:
                failed_count += 1
        
        print("\n" + "=" * 50)
        print(f"📈 删除结果统计:")
        print(f"✅ 成功删除: {deleted_count} 个索引")
        print(f"❌ 删除失败: {failed_count} 个索引")
        print(f"📊 总计处理: {len(indexes)} 个索引")
        
        if deleted_count > 0:
            print(f"\n🎉 索引删除完成！现在可以启动应用测试是否会自动创建索引。")
        
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        sys.exit(1)
    
    finally:
        conn.close()
        print("🔒 数据库连接已关闭")


if __name__ == "__main__":
    main()
