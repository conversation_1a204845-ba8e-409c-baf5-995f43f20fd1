import requests
import json

# API基础URL
BASE_URL = "http://127.0.0.1:9999/api/v1"

# 登录并获取token
def login(username, password):
    url = f"{BASE_URL}/auth/login"
    payload = {
        "username": username,
        "password": password
    }
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == "0000":
            return data.get("data", {}).get("token")
    raise Exception(f"登录失败: {response.text}")

# 创建菜单
def create_menu(token, menu_data):
    url = f"{BASE_URL}/system-manage/menus"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    response = requests.post(url, headers=headers, json=menu_data)
    if response.status_code == 200:
        data = response.json()
        if data.get("code") == "0000":
            return data.get("data", {}).get("created_id")
        print(f"创建菜单失败: {data.get('msg', '未知错误')}")
    else:
        print(f"创建菜单失败: {response.text}")
    return None

def main():
    # 用户凭证
    username = input("请输入用户名: ")
    password = input("请输入密码: ")
    
    try:
        # 登录获取token
        token = login(username, password)
        print("登录成功，获取到token")
        
        # 创建STRM顶级菜单
        strm_menu_data = {
            "menuType": "1",  # 目录
            "menuName": "STRM管理",
            "routeName": "strm",
            "routePath": "/strm",
            "icon": "mdi:file-upload-outline",
            "iconType": "1",  # iconify图标
            "i18nKey": "route.strm",
            "parentId": 0,
            "order": 3,
            "statusType": "1",  # 启用
            "hideInMenu": False,
            "keepAlive": False,
            "constant": False,
            "query": [],
            "byMenuButtons": []
        }
        
        strm_menu_id = create_menu(token, strm_menu_data)
        if not strm_menu_id:
            print("创建STRM顶级菜单失败，程序退出")
            return
        
        print(f"STRM顶级菜单创建成功，ID: {strm_menu_id}")
        
        # 创建文件上传子菜单
        upload_menu_data = {
            "menuType": "2",  # 菜单
            "menuName": "文件上传",
            "routeName": "strm_upload",
            "routePath": "/strm/upload",
            "component": "layout.base$view.strm_upload",
            "icon": "mdi:upload",
            "iconType": "1",  # iconify图标
            "i18nKey": "route.strm_upload",
            "parentId": strm_menu_id,
            "order": 1,
            "statusType": "1",  # 启用
            "hideInMenu": False,
            "keepAlive": False,
            "constant": False,
            "query": [],
            "byMenuButtons": []
        }
        
        upload_menu_id = create_menu(token, upload_menu_data)
        if upload_menu_id:
            print(f"文件上传菜单创建成功，ID: {upload_menu_id}")
        else:
            print("文件上传菜单创建失败")
        
        # 创建任务管理子菜单
        tasks_menu_data = {
            "menuType": "2",  # 菜单
            "menuName": "任务管理",
            "routeName": "strm_tasks",
            "routePath": "/strm/tasks",
            "component": "layout.base$view.strm_tasks",
            "icon": "mdi:playlist-check",
            "iconType": "1",  # iconify图标
            "i18nKey": "route.strm_tasks",
            "parentId": strm_menu_id,
            "order": 2,
            "statusType": "1",  # 启用
            "hideInMenu": False,
            "keepAlive": False,
            "constant": False,
            "query": [],
            "byMenuButtons": []
        }
        
        tasks_menu_id = create_menu(token, tasks_menu_data)
        if tasks_menu_id:
            print(f"任务管理菜单创建成功，ID: {tasks_menu_id}")
        else:
            print("任务管理菜单创建失败")
        
        print("所有菜单创建完成，请刷新页面查看效果")
        
    except Exception as e:
        print(f"发生错误: {e}")

if __name__ == "__main__":
    main() 