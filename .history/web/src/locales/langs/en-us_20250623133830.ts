const local: App.I18n.Schema = {
  system: {
    title: 'SoybeanAdmin',
    updateTitle: 'System Version Update Notification',
    updateContent: 'A new version of the system has been detected. Do you want to refresh the page immediately?',
    updateConfirm: 'Refresh immediately',
    updateCancel: 'Later'
  },
  common: {
    action: 'Action',
    add: 'Add',
    addSuccess: 'Add Success',
    backToHome: 'Back to home',
    batchApprove: 'Batch Approve',
    batchReject: 'Batch Reject',
    batchDelete: 'Batch Delete',
    cancel: 'Cancel',
    close: 'Close',
    check: 'Check',
    expandColumn: 'Expand Column',
    columnSetting: 'Column Setting',
    config: 'Config',
    confirm: 'Confirm',
    delete: 'Delete',
    deleteSuccess: 'Delete Success',
    confirmDelete: 'Are you sure you want to delete?',
    approve: 'Approve',
    approveSuccess: 'Approve Success',
    confirmApprove: 'Are you sure you want to approve?',
    reject: 'Reject',
    rejectSuccess: 'Approve Reject',
    confirmReject: 'Are you sure you want to reject?',
    edit: 'Edit',
    view: 'View',
    warning: 'Warning',
    error: 'Error',
    index: 'Index',
    keywordSearch: 'Please enter keyword',
    logout: 'Logout',
    logoutConfirm: 'Are you sure you want to log out?',
    lookForward: 'Coming soon',
    modify: 'Modify',
    modifySuccess: 'Modify Success',
    noData: 'No Data',
    operate: 'Operate',
    pleaseCheckValue: 'Please check whether the value is valid',
    refresh: 'Refresh',
    refreshAPI: 'Refresh API',
    reset: 'Reset',
    search: 'Search',
    switch: 'Switch',
    tip: 'Tip',
    trigger: 'Trigger',
    update: 'Update',
    updateSuccess: 'Update Success',
    userCenter: 'User Center',
    yesOrNo: {
      yes: 'Yes',
      no: 'No'
    }
  },
  request: {
    logout: 'Logout user after request failed',
    logoutMsg: 'User status is invalid, please log in again',
    logoutWithModal: 'Pop up modal after request failed and then log out user',
    logoutWithModalMsg: 'User status is invalid, please log in again',
    refreshToken: 'The requested token has expired, refresh the token',
    tokenExpired: 'The requested token has expired'
  },
  theme: {
    themeSchema: {
      title: 'Theme Schema',
      light: 'Light',
      dark: 'Dark',
      auto: 'Follow System'
    },
    grayscale: 'Grayscale',
    colourWeakness: 'Colour Weakness',
    layoutMode: {
      title: 'Layout Mode',
      vertical: 'Vertical Menu Mode',
      horizontal: 'Horizontal Menu Mode',
      'vertical-mix': 'Vertical Mix Menu Mode',
      'horizontal-mix': 'Horizontal Mix menu Mode',
      reverseHorizontalMix: 'Reverse first level menus and child level menus position'
    },
    recommendColor: 'Apply Recommended Color Algorithm',
    recommendColorDesc: 'The recommended color algorithm refers to',
    themeColor: {
      title: 'Theme Color',
      primary: 'Primary',
      info: 'Info',
      success: 'Success',
      warning: 'Warning',
      error: 'Error',
      followPrimary: 'Follow Primary'
    },
    scrollMode: {
      title: 'Scroll Mode',
      wrapper: 'Wrapper',
      content: 'Content'
    },
    page: {
      animate: 'Page Animate',
      mode: {
        title: 'Page Animate Mode',
        fade: 'Fade',
        'fade-slide': 'Slide',
        'fade-bottom': 'Fade Zoom',
        'fade-scale': 'Fade Scale',
        'zoom-fade': 'Zoom Fade',
        'zoom-out': 'Zoom Out',
        none: 'None'
      }
    },
    fixedHeaderAndTab: 'Fixed Header And Tab',
    header: {
      height: 'Header Height',
      breadcrumb: {
        visible: 'Breadcrumb Visible',
        showIcon: 'Breadcrumb Icon Visible'
      }
    },
    tab: {
      visible: 'Tab Visible',
      cache: 'Tag Bar Info Cache',
      height: 'Tab Height',
      mode: {
        title: 'Tab Mode',
        chrome: 'Chrome',
        button: 'Button'
      }
    },
    sider: {
      inverted: 'Dark Sider',
      width: 'Sider Width',
      collapsedWidth: 'Sider Collapsed Width',
      mixWidth: 'Mix Sider Width',
      mixCollapsedWidth: 'Mix Sider Collapse Width',
      mixChildMenuWidth: 'Mix Child Menu Width'
    },
    footer: {
      visible: 'Footer Visible',
      fixed: 'Fixed Footer',
      height: 'Footer Height',
      right: 'Right Footer'
    },
    watermark: {
      visible: 'Watermark Full Screen Visible',
      text: 'Watermark Text'
    },
    themeDrawerTitle: 'Theme Configuration',
    pageFunTitle: 'Page Function',
    resetCacheStrategy: {
      title: 'Reset Cache Strategy',
      close: 'Close Page',
      refresh: 'Refresh Page'
    },
    configOperation: {
      copyConfig: 'Copy Config',
      copySuccessMsg: 'Copy Success, Please replace the variable "themeSettings" in "src/theme/settings.ts"',
      resetConfig: 'Reset Config',
      resetSuccessMsg: 'Reset Success'
    }
  },
  route: {
    login: 'Login',
    403: '403',
    404: '404',
    500: '500',
    'iframe-page': 'Iframe Page',
    home: 'Home',
    document: 'Document',
    document_project: 'Project Document',
    'document_project-link': 'Project Document(Link)',
    document_vue: 'Vue Document',
    document_vite: 'Vite Document',
    document_unocss: 'UnoCSS Document',
    document_naive: 'Naive UI Document',
    document_antd: 'Ant Design Vue Document',
    document_alova: 'Alova Document',
    'user-center': 'User Center',
    about: 'About',
    function: 'Function',
    alova: 'Alova Example',
    alova_request: 'Alova Request',
    alova_user: 'User List',
    alova_scenes: 'Business Scenes',
    function_tab: 'Tab',
    'function_multi-tab': 'Multi Tab',
    'function_hide-child': 'Hide Child',
    'function_hide-child_one': 'Hide Child',
    'function_hide-child_two': 'Menu Two',
    'function_hide-child_three': 'Menu Three',
    function_request: 'Request',
    'function_toggle-auth': 'Toggle Auth',
    'function_super-page': 'Super Admin',
    manage: 'System Manage',
    manage_log: 'Log Management',
    manage_api: 'API Management',
    manage_user: 'User Management',
    'manage_user-detail': 'User Detail',
    manage_role: 'Role Management',
    manage_menu: 'Menu Management',
    'manage_settings': 'System Settings',
    'multi-menu': 'Multi Menu',
    'multi-menu_first': 'Menu One',
    'multi-menu_first_child': 'Menu One Child',
    'multi-menu_second': 'Menu Two',
    'multi-menu_second_child': 'Menu Two Child',
    'multi-menu_second_child_home': 'Menu Two Child Home',
    exception: 'Exception',
    exception_403: '403',
    exception_404: '404',
    exception_500: '500',
    plugin: 'Plugin',
    plugin_copy: 'Copy',
    plugin_charts: 'Charts',
    plugin_charts_echarts: 'ECharts',
    plugin_charts_antv: 'AntV',
    plugin_charts_vchart: 'VChart',
    plugin_editor: 'Editor',
    plugin_editor_quill: 'Rich Text Editor',
    plugin_editor_markdown: 'Markdown Editor',
    plugin_icon: 'Icon',
    plugin_map: 'Map',
    plugin_print: 'Print',
    plugin_swiper: 'Swiper',
    plugin_video: 'Video',
    plugin_barcode: 'Barcode',
    plugin_pinyin: 'Pinyin',
    plugin_excel: 'Excel',
    plugin_pdf: 'PDF Preview',
    plugin_gantt: 'Gantt',
    plugin_gantt_dhtmlx: 'dhtmlxGantt',
    plugin_gantt_vtable: 'VTableGantt',
    plugin_typeit: 'TypeIt',
    plugin_tables: 'Tables',
    plugin_tables_vtable: 'VTable',
    strm: 'STRM Manage',
    strm_upload: 'File Upload',
    strm_generate: 'STRM Generate',
    strm_history: 'Upload History',
    strm_settings: 'System Settings'
  },
  page: {
    login: {
      common: {
        loginOrRegister: 'Login / Register',
        userNamePlaceholder: 'Please enter user name',
        phonePlaceholder: 'Please enter phone number',
        codePlaceholder: 'Please enter verification code',
        passwordPlaceholder: 'Please enter password',
        confirmPasswordPlaceholder: 'Please enter password again',
        codeLogin: 'Verification code login',
        confirm: 'Confirm',
        back: 'Back',
        validateSuccess: 'Verification passed',
        loginSuccess: 'Login successfully',
        welcomeBack: 'Welcome back, {nickName} !'
      },
      pwdLogin: {
        title: 'Password Login',
        rememberMe: 'Remember me',
        forgetPassword: 'Forget password?',
        register: 'Register',
        otherAccountLogin: 'Other Account Login',
        otherLoginMode: 'Other Login Mode',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        user: 'User'
      },
      codeLogin: {
        title: 'Verification Code Login',
        getCode: 'Get verification code',
        reGetCode: 'Reacquire after {time}s',
        sendCodeSuccess: 'Verification code sent successfully',
        imageCodePlaceholder: 'Please enter image verification code'
      },
      register: {
        title: 'Register',
        agreement: 'I have read and agree to',
        protocol: '《User Agreement》',
        policy: '《Privacy Policy》'
      },
      resetPwd: {
        title: 'Reset Password'
      },
      bindWeChat: {
        title: 'Bind WeChat'
      }
    },
    about: {
      title: 'About',
      introduction: `SoybeanAdmin is an elegant and powerful admin template, based on the latest front-end technology stack, including Vue3, Vite5, TypeScript, Pinia and UnoCSS. It has built-in rich theme configuration and components, strict code specifications, and an automated file routing system. In addition, it also uses the online mock data solution based on ApiFox. SoybeanAdmin provides you with a one-stop admin solution, no additional configuration, and out of the box. It is also a best practice for learning cutting-edge technologies quickly.`,
      projectInfo: {
        title: 'Project Info',
        version: 'Version',
        latestBuildTime: 'Latest Build Time',
        githubLink: 'Github Link',
        previewLink: 'Preview Link'
      },
      prdDep: 'Production Dependency',
      devDep: 'Development Dependency'
    },
    home: {
      branchDesc:
        'For the convenience of everyone in developing and updating the merge, we have streamlined the code of the main branch, only retaining the homepage menu, and the rest of the content has been moved to the example branch for maintenance. The preview address displays the content of the example branch.',
      greeting: 'Good morning, {userName}, today is another day full of vitality!',
      weatherDesc: 'Today is cloudy to clear, 20℃ - 25℃!',
      projectCount: 'Project Count',
      todo: 'Todo',
      message: 'Message',
      downloadCount: 'Download Count',
      registerCount: 'Register Count',
      schedule: 'Work and rest Schedule',
      study: 'Study',
      work: 'Work',
      rest: 'Rest',
      entertainment: 'Entertainment',
      visitCount: 'Visit Count',
      turnover: 'Turnover',
      dealCount: 'Deal Count',
      projectNews: {
        title: 'Project News',
        moreNews: 'More News',
        desc1: 'Soybean created the open source project soybean-admin on May 28, 2021!',
        desc2: 'Yanbowe submitted a bug to soybean-admin, the multi-tab bar will not adapt.',
        desc3: 'Soybean is ready to do sufficient preparation for the release of soybean-admin!',
        desc4: 'Soybean is busy writing project documentation for soybean-admin!',
        desc5: 'Soybean just wrote some of the workbench pages casually, and it was enough to see!'
      },
      creativity: 'Creativity'
    },
    function: {
      tab: {
        tabOperate: {
          title: 'Tab Operation',
          addTab: 'Add Tab',
          addTabDesc: 'To about page',
          closeTab: 'Close Tab',
          closeCurrentTab: 'Close Current Tab',
          closeAboutTab: 'Close "About" Tab',
          addMultiTab: 'Add Multi Tab',
          addMultiTabDesc1: 'To MultiTab page',
          addMultiTabDesc2: 'To MultiTab page(with query params)'
        },
        tabTitle: {
          title: 'Tab Title',
          changeTitle: 'Change Title',
          change: 'Change',
          resetTitle: 'Reset Title',
          reset: 'Reset'
        }
      },
      multiTab: {
        routeParam: 'Route Param',
        backTab: 'Back function_tab'
      },
      toggleAuth: {
        toggleAccount: 'Toggle Account',
        authHook: 'Auth Hook Function `hasAuth`',
        superAdminVisible: 'Super Admin Visible',
        adminVisible: 'Admin Visible',
        adminOrUserVisible: 'Admin and User Visible'
      },
      request: {
        repeatedErrorOccurOnce: 'Repeated Request Error Occurs Once',
        repeatedError: 'Repeated Request Error',
        repeatedErrorMsg1: 'Custom Request Error 1',
        repeatedErrorMsg2: 'Custom Request Error 2'
      }
    },
    alova: {
      scenes: {
        captchaSend: 'Captcha Send',
        autoRequest: 'Auto Request',
        visibilityRequestTips: 'Automatically request when switching browser window',
        pollingRequestTips: 'It will request every 3 seconds',
        networkRequestTips: 'Automatically request after network reconnecting',
        refreshTime: 'Refresh Time',
        startRequest: 'Start Request',
        stopRequest: 'Stop Request',
        requestCrossComponent: 'Request Cross Component',
        triggerAllRequest: 'Manually Trigger All Automated Requests'
      }
    },
    manage: {
      common: {
        statusType: {
          _: 'Status',
          enable: 'Enable',
          disable: 'Disable'
        }
      },
      role: {
        title: 'Role List',
        roleName: 'Role Name',
        roleCode: 'Role Code',
        rolestatusType: 'Role statusType',
        roleDesc: 'Role Description',
        menuAuth: 'Menu Auth',
        buttonAuth: 'Button Auth',
        apiAuth: 'Api Auth',
        form: {
          roleName: 'Please enter role name',
          roleCode: 'Please enter role code',
          rolestatusType: 'Please select role statusType',
          roleDesc: 'Please enter role description'
        },
        addRole: 'Add Role',
        editRole: 'Edit Role'
      },
      user: {
        title: 'User List',
        userName: 'User Name',
        password: 'Password',
        userGender: 'Gender',
        nickName: 'Nick Name',
        userPhone: 'Phone Number',
        userEmail: 'Email',
        userStatusType: 'User statusType',
        userRole: 'User Role',
        form: {
          userName: 'Please enter user name',
          password: 'Please enter password',
          userGender: 'Please select gender',
          nickName: 'Please enter nick name',
          userPhone: 'Please enter phone number',
          userEmail: 'Please enter email',
          userStatusType: 'Please select user statusType',
          userRole: 'Please select user role'
        },
        addUser: 'Add User',
        editUser: 'Edit User',
        gender: {
          male: 'Male',
          female: 'Female',
          unknow: 'Unknow'
        }
      },
      menu: {
        home: 'Home',
        title: 'Menu List',
        id: 'ID',
        parentId: 'Parent ID',
        menuType: 'Menu Type',
        menuName: 'Menu Name',
        routeName: 'Route Name',
        routePath: 'Route Path',
        pathParam: 'Path Param',
        layout: 'Layout Component',
        page: 'Page Component',
        i18nKey: 'I18n Key',
        icon: 'Icon',
        localIcon: 'Local Icon',
        iconTypeTitle: 'Icon Type',
        order: 'Order',
        constant: 'Constant',
        keepAlive: 'Keep Alive',
        href: 'Href',
        hideInMenu: 'Hide In Menu',
        activeMenu: 'Active Menu',
        multiTab: 'Multi Tab',
        fixedIndexInTab: 'Fixed Index In Tab',
        query: 'Query Params',
        button: 'Button',
        buttonCode: 'Button Code',
        buttonDesc: 'Button Desc',
        menuStatusType: 'Menu statusType',
        form: {
          home: 'Please select home',
          menuType: 'Please select menu type',
          menuName: 'Please enter menu name',
          routeName: 'Please enter route name',
          routePath: 'Please enter route path',
          pathParam: 'Please enter path param',
          page: 'Please select page component',
          layout: 'Please select layout component',
          i18nKey: 'Please enter i18n key',
          icon: 'Please enter iconify name',
          localIcon: 'Please enter local icon name',
          order: 'Please enter order',
          keepAlive: 'Please select whether to cache route',
          href: 'Please enter href',
          hideInMenu: 'Please select whether to hide menu',
          activeMenu: 'Please select route name of the highlighted menu',
          multiTab: 'Please select whether to support multiple tabs',
          fixedInTab: 'Please select whether to fix in the tab',
          fixedIndexInTab: 'Please enter the index fixed in the tab',
          queryKey: 'Please enter route parameter Key',
          queryValue: 'Please enter route parameter Value',
          button: 'Please select whether it is a button',
          buttonCode: 'Please enter button code',
          buttonDesc: 'Please enter button description',
          menuStatusType: 'Please select menu statusType'
        },
        addMenu: 'Add Menu',
        editMenu: 'Edit Menu',
        addChildMenu: 'Add Child Menu',
        type: {
          directory: 'Directory',
          menu: 'Menu'
        },
        iconType: {
          iconify: 'Iconify Icon',
          local: 'Local Icon'
        }
      },
      log: {
        title: '',
        logType: '',
        byUser: '',
        logDetailType: '',
        createTime: '',
        requestDomain: '',
        requestPath: '',
        responseCode: '',
        xRequestId: '',
        requestParams: '',
        responseData: '',
        userAgent: '',
        processTime: '',
        ipAddress: '',
        form: {
          logType: '',
          byUser: '',
          logDetailType: '',
          requestPath: '',
          createTime: '',
          responseCode: ''
        },
        viewLog: '',
        logDetailTypes: {
          SystemStart: '',
          SystemStop: '',
          UserLoginSuccess: '',
          UserAuthRefreshTokenSuccess: '',
          UserLoginGetUserInfo: '',
          UserLoginUserNameVaild: '',
          UserLoginErrorPassword: '',
          UserLoginForbid: '',
          ApiGetList: '',
          ApiGetTree: '',
          ApiRefresh: '',
          ApiGetOne: '',
          ApiCreateOne: '',
          ApiUpdateOne: '',
          ApiDeleteOne: '',
          ApiBatchDelete: '',
          MenuGetList: '',
          MenuGetTree: '',
          MenuGetPages: '',
          MenuGetButtonsTree: '',
          MenuGetOne: '',
          MenuCreateOne: '',
          MenuUpdateOne: '',
          MenuDeleteOne: '',
          MenuBatchDeleteOne: '',
          RoleGetList: '',
          RoleGetMenus: '',
          RoleUpdateMenus: '',
          RoleGetButtons: '',
          RoleUpdateButtons: '',
          RoleGetApis: '',
          RoleUpdateApis: '',
          RoleGetOne: '',
          RoleCreateOne: '',
          RoleUpdateOne: '',
          RoleDeleteOne: '',
          RoleBatchDeleteOne: '',
          UserGetList: '',
          UserGetOne: '',
          UserCreateOne: '',
          UserUpdateOne: '',
          UserDeleteOne: '',
          UserBatchDeleteOne: ''
        },
        logTypes: {
          ApiLog: '',
          UserLog: '',
          AdminLog: '',
          SystemLog: ''
        }
      },
      api: {
        title: '',
        path: '',
        method: '',
        summary: '',
        tags: '',
        statusType: '',
        form: {
          path: '',
          method: '',
          summary: '',
          tags: '',
          statusType: ''
        },
        addApi: '',
        editApi: '',
        methods: {
          GET: '',
          POST: '',
          PUT: '',
          PATCH: '',
          DELETE: ''
        }
      }
    }
  },
  form: {
    required: 'Cannot be empty',
    userName: {
      required: 'Please enter user name',
      invalid: 'User name format is incorrect'
    },
    phone: {
      required: 'Please enter phone number',
      invalid: 'Phone number format is incorrect'
    },
    pwd: {
      required: 'Please enter password',
      invalid: '6-18 characters, including letters, numbers, and underscores'
    },
    confirmPwd: {
      required: 'Please enter password again',
      invalid: 'The two passwords are inconsistent'
    },
    code: {
      required: 'Please enter verification code',
      invalid: 'Verification code format is incorrect'
    },
    email: {
      required: 'Please enter email',
      invalid: 'Email format is incorrect'
    }
  },
  dropdown: {
    closeCurrent: 'Close Current',
    closeOther: 'Close Other',
    closeLeft: 'Close Left',
    closeRight: 'Close Right',
    closeAll: 'Close All'
  },
  icon: {
    themeConfig: 'Theme Configuration',
    themeSchema: 'Theme Schema',
    lang: 'Switch Language',
    fullscreen: 'Fullscreen',
    fullscreenExit: 'Exit Fullscreen',
    reload: 'Reload Page',
    collapse: 'Collapse Menu',
    expand: 'Expand Menu',
    pin: 'Pin',
    unpin: 'Unpin'
  },
  datatable: {
    itemCount: 'Total {total} items'
  },
  strm: {
    upload: 'File Upload',
    generate: 'STRM Generate',
    history: 'Upload History',
    settings: {
      title: 'System Settings',
      defaultServer: 'Default Server',
      defaultDownloadServer: 'Default Download Server',
      defaultMediaServer: 'Default Media Server',
      enablePathReplacement: 'Enable Path Replacement',
      replacementPath: 'Replacement Path',
      downloadThreads: 'Default Download Threads',
      outputDirectory: 'Default Output Directory',
      saveSettings: 'Save Settings',
      defaultServerPlaceholder: 'Please select default server',
      defaultDownloadServerPlaceholder: 'Please select default download server',
      defaultMediaServerPlaceholder: 'Please select default media server',
      replacementPathPlaceholder: 'Please enter replacement path',
      downloadThreadsPlaceholder: 'Please enter default download threads',
      outputDirectoryPlaceholder: 'Please enter default output directory path',
      defaultServerHelp: 'Set global default server, will be selected by default when generating STRM',
      defaultDownloadServerHelp: 'Set default download server for file downloads',
      defaultMediaServerHelp: 'Set default media server for media file playback',
      enablePathReplacementHelp: 'Enable path replacement according to path mapping rules',
      replacementPathHelp: 'Set target path for path replacement',
      downloadThreadsHelp: 'Set default parallel download threads',
      outputDirectoryHelp: 'Set default output directory for STRM file generation',
      fileTypesSettings: 'File Type Settings',
      videoFileTypes: 'Video File Types',
      audioFileTypes: 'Audio File Types',
      imageFileTypes: 'Image File Types',
      subtitleFileTypes: 'Subtitle File Types',
      metadataFileTypes: 'Metadata File Types',
      videoFileTypesPlaceholder: 'Please enter video file extensions, separated by commas, e.g.: mp4,mkv,avi',
      audioFileTypesPlaceholder: 'Please enter audio file extensions, separated by commas, e.g.: mp3,flac,wav',
      imageFileTypesPlaceholder: 'Please enter image file extensions, separated by commas, e.g.: jpg,png,gif',
      subtitleFileTypesPlaceholder: 'Please enter subtitle file extensions, separated by commas, e.g.: srt,ass,vtt',
      metadataFileTypesPlaceholder: 'Please enter metadata file extensions, separated by commas, e.g.: nfo,xml,json',
      fileTypesHelp: 'Set file type extensions, separated by commas',
      addExtensionPlaceholder: 'Add extension (e.g. .mp4)',
      invalidExtension: 'Invalid extension format, should start with a dot followed by 1-10 letters or numbers',
      duplicateExtension: 'This extension already exists',
      saveSuccess: 'Save successfully',
      saveFail: 'Save failed',
      basicSettings: 'Basic Settings',
      serverManagement: 'Server Management',
      serverList: 'Server List',
      addServer: 'Add Server',
      editServer: 'Edit Server',
      deleteServer: 'Delete Server',
      testConnection: 'Test Connection',
      serverInfo: 'Server Info',
      serverName: 'Server Name',
      serverType: 'Server Type',
      serverUrl: 'Server URL',
      isDefault: 'Set as Default',
      description: 'Description',
      authRequired: 'Auth Required',
      username: 'Username',
      password: 'Password',
      confirmDelete: 'Are you sure to delete this server?',
      deleteSuccess: 'Delete successfully',
      deleteError: 'Delete failed',
      testSuccess: 'Connection successful',
      testError: 'Connection failed',
      downloadServer: 'Download Server',
      mediaServer: 'Media Server',
      serverNamePlaceholder: 'Please enter server name',
      serverUrlPlaceholder: 'Please enter server URL',
      baseUrlPlaceholder: 'Please enter server URL address, starting with http:// or https://',
      descriptionPlaceholder: 'Please enter server description',
      usernamePlaceholder: 'Please enter username',
      passwordPlaceholder: 'Please enter password',
      addSuccess: 'Add Successful',
      addError: 'Add Failed',
      updateSuccess: 'Update Successful',
      updateError: 'Update Failed',
      cancel: 'Cancel',
      confirm: 'Confirm',
      serverStatusActive: 'Online',
      serverStatusInactive: 'Offline',
      status: 'Status',
      serverStatusUnknown: 'Not Tested',
      serverStatusWarning: 'Warning',
      cd2host: 'Download Server (CD2HOST)',
      xiaoyahost: 'Media Server (XIAOYAHOST)',
      http: 'HTTP',
      https: 'HTTPS',
      local: 'Local Network',
      ftp: 'FTP',
      webdav: 'WebDAV',
      baseUrl: 'Server URL',
      test: 'Test',
      passwordHelp: 'Leave empty to keep unchanged',
      save: 'Save',
      noServers: 'No servers available, please add a new server',
      invalidUrl: 'Please enter a valid server address',
      unnamedServer: 'Unnamed Server',
      invalidServerData: 'Invalid server data format',
      getServerListFailed: 'Failed to get server list'
    }
  }
};

export default local;
