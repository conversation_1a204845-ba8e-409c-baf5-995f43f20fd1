import { request } from '../request';

/**
 * 上传115目录树文件
 *
 * @param file 要上传的文件
 */
export function uploadDirectoryTree(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<StrmAPI.UploadResult>({
    url: '/strm/upload',
    method: 'post',
    data: formData
  });
}

/**
 * 通过URL上传115目录树文件
 *
 * @param url 文件的URL地址
 * @param onProgress 下载进度回调函数 (percent: number) => void
 * @param timeoutMs 超时时间（毫秒），默认为60秒
 */
export function uploadDirectoryTreeFromUrl(url: string, onProgress?: (percent: number) => void, timeoutMs: number = 60000) {
  return request<StrmAPI.UploadResult>({
    url: '/strm/upload-url',
    method: 'post',
    data: { url },
    // 为URL上传设置更长的超时时间，因为需要从远程服务器下载文件
    timeout: timeoutMs,
    onDownloadProgress: event => {
      if (onProgress && event.total) {
        // 只有当total存在（服务器返回Content-Length）时才能计算精确进度
        const percent = Math.round((event.loaded * 100) / event.total);
        onProgress(percent);
      } else if (onProgress && event.loaded > 0) {
        // 当无法获取total时，提供不精确的进度指示（基于已下载的字节数）
        // 这种情况下最多只显示到95%，最后5%在上传成功后设置
        const estimatedPercent = Math.min(95, Math.round(event.loaded / 10240)); // 假设平均文件大小为1MB
        onProgress(estimatedPercent);
      }
    }
  });
}

/**
 * 解析已上传的115目录树文件
 *
 * @param data 包含记录ID的对象
 */
export function parseDirectoryTree(data: { record_id: string | number; file_path?: string }) {
  return request<StrmAPI.ParseResult>({
    url: '/strm/parse',
    method: 'post',
    data
  });
}

/** 获取上传历史 */
export function getUploadHistory(params: Api.Page.PageParams) {
  return request<Api.Page.PageResult<StrmAPI.UploadRecord>>({
    url: '/strm/history',
    method: 'get',
    params
  });
}

/**
 * 删除上传记录
 *
 * @param recordId 记录ID
 */
export function deleteUploadRecord(recordId: string | number) {
  return request<{ message: string }>({
    url: `/strm/history/${recordId}`,
    method: 'delete'
  });
}

/**
 * 获取文件下载链接
 *
 * @param recordId 记录ID
 * @returns 完整的下载URL
 */
export function getDownloadUrl(recordId: string | number): string {
  // 获取基础URL，确保与其他API请求一致
  const baseURL = import.meta.env.VITE_SERVICE_BASE_URL || '';
  return `${baseURL}/strm/download/${recordId}`;
}

/**
 * 获取文件解析结果，支持按文件类型过滤和分页
 *
 * @param recordId 记录ID
 * @param options 可选参数对象 {fileType, page, pageSize}
 * @returns 过滤和分页后的解析结果
 */
export function getParseResult(
  recordId: string | number,
  options: { fileType?: string; page?: number; pageSize?: number; allFiles?: boolean } = {}
) {
  const { fileType = 'all', page = 1, pageSize = 10, allFiles = false } = options;

  return request<StrmAPI.ParseResult>({
    url: `/strm/result/${recordId}`,
    method: 'get',
    params: { file_type: fileType, page, page_size: pageSize, all_files: allFiles }
  });
}

/**
 * 获取目录内容（采用懒加载方式）
 *
 * @param recordId 记录ID
 * @param options 可选参数对象 {directoryPath, fileType}
 * @returns 包含目录下文件和子目录的结果
 */
export function getDirectoryContent(
  recordId: string | number,
  options: { directoryPath?: string; fileType?: string } = {}
) {
  const { directoryPath = '/', fileType = 'all' } = options;

  return request<{
    directory_path: string;
    files: StrmAPI.ParsedFile[];
    subdirectories: string[];
    stats: {
      file_count: number;
      subdirectory_count: number;
    }
  }>({
    url: `/strm/directory/${recordId}`,
    method: 'get',
    params: { directory_path: directoryPath, file_type: fileType }
  });
}

/**
 * 搜索文件
 *
 * @param recordId 记录ID
 * @param searchText 搜索文本
 * @param options 可选参数，包括文件类型过滤和是否忽略大小写
 * @returns 搜索结果
 */
export function searchFiles(
  recordId: string | number,
  searchText: string,
  options: { fileType?: string; ignoreCase?: boolean } = {}
) {
  const { fileType = 'all', ignoreCase = true } = options;

  return request<{
    search_text: string;
    ignore_case: boolean;
    file_type: string;
    total_matches: number;
    matches: StrmAPI.ParsedFile[];
  }>({
    url: `/strm/search/${recordId}`,
    method: 'get',
    params: { search_text: searchText, file_type: fileType, ignore_case: ignoreCase }
  });
}

/**
 * 获取可用的媒体服务器列表
 * 
 * @returns 可用的媒体服务器列表
 */
export function getMediaServers() {
  return request<StrmAPI.MediaServer[]>({
    url: '/strm/servers',
    method: 'get'
  });
}

/**
 * 创建STRM生成任务
 * 
 * @param data 任务创建参数
 * @returns 生成任务信息
 */
export function generateStrm(data: StrmAPI.StrmTaskCreate) {
  return request<StrmAPI.StrmGenerateResult>({
    url: '/strm/generate',
    method: 'post',
    data
  });
}

/**
 * 获取任务状态
 * 
 * @param taskId 任务ID 
 * @returns 任务详情
 */
export function getTaskStatus(taskId: string | number) {
  return request<StrmAPI.StrmTaskDetail>({
    url: `/strm/task/${taskId}`,
    method: 'get'
  });
}

/**
 * 获取用户任务列表
 * 
 * @param params 分页参数
 * @returns 任务列表
 */
export function getTasks(params: Api.Page.PageParams) {
  return request<StrmAPI.StrmTaskResponse>({
    url: '/strm/tasks',
    method: 'get',
    params
  });
}

/**
 * 取消任务
 * 
 * @param taskId 任务ID
 */
export function cancelTask(taskId: string | number) {
  return request<{ success: boolean; message: string }>({
    url: `/strm/task/${taskId}/cancel`,
    method: 'post'
  });
}

/**
 * 删除任务
 * 
 * @param taskId 任务ID
 */
export function deleteTask(taskId: string | number) {
  return request<{ success: boolean; message: string }>({
    url: `/strm/task/${taskId}`,
    method: 'delete'
  });
}

/**
 * 获取STRM文件下载链接
 * 
 * @param taskId 任务ID
 * @returns 完整的下载URL
 */
export function getStrmDownloadUrl(taskId: string | number): string {
  // 获取基础URL，确保与其他API请求一致
  const baseURL = import.meta.env.VITE_SERVICE_BASE_URL || '';
  return `${baseURL}/strm/download-strm/${taskId}`;
}
