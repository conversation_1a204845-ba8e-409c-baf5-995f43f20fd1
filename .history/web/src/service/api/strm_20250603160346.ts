import { defHttp } from '../request';

/**
 * 创建STRM任务
 * @param data 任务数据
 */
export function createStrmTask(data: StrmAPI.StrmTaskCreate) {
  return defHttp.post<ApiCommon.Response<StrmAPI.StrmTaskResponse>>(
    {
      url: '/strm/task',
      data
    }
  );
}

/**
 * 获取STRM任务列表
 */
export function getStrmTasks() {
  return defHttp.get<ApiCommon.Response<StrmAPI.StrmTaskResponse[]>>(
    {
      url: '/strm/task'
    }
  );
}

/**
 * 获取STRM任务详情
 * @param taskId 任务ID
 */
export function getStrmTask(taskId: number) {
  return defHttp.get<ApiCommon.Response<StrmAPI.StrmTaskResponse>>(
    {
      url: `/strm/task/${taskId}`
    }
  );
}

/**
 * 更新STRM任务
 * @param taskId 任务ID
 * @param data 更新数据
 */
export function updateStrmTask(taskId: number, data: StrmAPI.StrmTaskUpdate) {
  return defHttp.put<ApiCommon.Response<StrmAPI.StrmTaskResponse>>(
    {
      url: `/strm/task/${taskId}`,
      data
    }
  );
}

/**
 * 上传115目录树文件
 * @param taskId 任务ID
 * @param file 文件对象
 */
export function uploadTreeFile(taskId: number, file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return defHttp.post<ApiCommon.Response<StrmAPI.UploadResult>>(
    {
      url: `/strm/task/upload/${taskId}`,
      data: formData,
      headers: { 'Content-Type': 'multipart/form-data' }
    }
  );
}

/**
 * 处理STRM生成任务
 * @param taskId 任务ID
 * @param files 文件列表，可选
 */
export function processStrmTask(taskId: number, files?: StrmAPI.ParsedFile[]) {
  return defHttp.post<ApiCommon.Response<StrmAPI.ProcessResult>>(
    {
      url: `/strm/task/process/${taskId}`,
      data: { files }
    }
  );
}
