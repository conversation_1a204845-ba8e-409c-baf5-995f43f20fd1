import { request } from '../request';

// Extend the global StrmAPI namespace for type safety
declare global {
  namespace StrmAPI {
    interface ParsedFile {
      file_name: string;
      path: string;
      is_directory?: boolean;
      file_type?: string;
      mime_type?: string;
      extension?: string;
      directory?: string;
    }
  }
}

/**
 * 上传115目录树文件
 *
 * @param file 要上传的文件
 */
export function uploadDirectoryTree(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return request<StrmAPI.UploadResult>({
    url: '/strm/upload',
    method: 'post',
    data: formData
  });
}

/**
 * 通过URL上传115目录树文件
 *
 * @param url 文件的URL地址
 * @param onProgress 下载进度回调函数 (percent: number) => void
 * @param timeoutMs 超时时间（毫秒），默认为60秒
 */
export function uploadDirectoryTreeFromUrl(url: string, onProgress?: (percent: number) => void, timeoutMs: number = 60000) {
  return request<StrmAPI.UploadResult>({
    url: '/strm/upload-url',
    method: 'post',
    data: { url },
    // 为URL上传设置更长的超时时间，因为需要从远程服务器下载文件
    timeout: timeoutMs,
    onDownloadProgress: event => {
      if (onProgress && event.total) {
        // 只有当total存在（服务器返回Content-Length）时才能计算精确进度
        const percent = Math.round((event.loaded * 100) / event.total);
        onProgress(percent);
      } else if (onProgress && event.loaded > 0) {
        // 当无法获取total时，提供不精确的进度指示（基于已下载的字节数）
        // 这种情况下最多只显示到95%，最后5%在上传成功后设置
        const estimatedPercent = Math.min(95, Math.round(event.loaded / 10240)); // 假设平均文件大小为1MB
        onProgress(estimatedPercent);
      }
    }
  });
}

/**
 * 解析已上传的115目录树文件
 *
 * @param data 包含记录ID的对象
 */
export function parseDirectoryTree(data: { record_id: string | number; file_path?: string }) {
  return request<StrmAPI.ParseResult>({
    url: '/strm/parse',
    method: 'post',
    data
  });
}

/**
 * 获取媒体服务器列表
 * @param serverType 可选的服务器类型过滤
 */
export function getMediaServers(serverType?: string) {
  return request({
    url: '/strm/servers',
    method: 'get',
    params: serverType ? { server_type: serverType } : {}
  });
}

/**
 * 创建STRM生成任务
 * @param data 任务数据
 */
export function createStrmTask(data: Record<string, any>) {
  return request({
    url: '/strm/generate',
    method: 'post',
    data
  });
}

/**
 * 生成STRM文件
 * @param data 生成所需的数据
 */
export function generateStrm(data: Record<string, any>) {
  // 使用更短的超时，因为这个请求应该很快返回，若长时间无响应则可能是服务器问题
  const timeoutMs = 10000; // 10秒超时

  return request({
    url: '/strm/generate',
    method: 'post',
    data,
    timeout: timeoutMs,
    retry: 0, // 不自动重试，让前端控制重试逻辑
    // 不使用AbortController，改为简单超时配置
    onError: (error) => {
      console.error('STRM生成任务创建失败:', error);
      // 错误将由调用者处理
    }
  });
}

/**
 * 获取任务列表
 * @param params 查询参数
 */
export function getTaskList(params?: Record<string, any>) {
  return request({
    url: '/strm/tasks',
    method: 'get',
    params
  });
}

/**
 * 获取任务状态
 * @param taskId 任务ID
 */
export function getTaskStatus(taskId: number) {
  return request({
    url: `/strm/task/${taskId}`,
    method: 'get'
  });
}

/**
 * 取消任务
 * @param taskId 任务ID
 */
export function cancelTask(taskId: number) {
  return request({
    url: `/strm/task/${taskId}/cancel`,
    method: 'post'
  });
}

/**
 * 删除任务
 * @param taskId 任务ID
 */
export function deleteTask(taskId: number) {
  return request({
    url: `/strm/task/${taskId}`,
    method: 'delete'
  });
}

/**
 * 获取文件上传历史
 * @param params 查询参数
 */
export function getUploadHistory(params?: Record<string, any>) {
  return request({
    url: '/strm/history',
    method: 'get',
    params
  });
}

/**
 * 获取文件解析结果，支持按文件类型过滤和分页
 *
 * @param recordId 记录ID
 * @param options 可选参数对象 {fileType, page, pageSize}
 * @returns 过滤和分页后的解析结果
 */
export function getParseResult(
  recordId: string | number,
  options: { fileType?: string; page?: number; pageSize?: number; allFiles?: boolean } = {}
) {
  const { fileType = 'all', page = 1, pageSize = 10, allFiles = false } = options;

  return request<StrmAPI.ParseResult>({
    url: `/strm/result/${recordId}`,
    method: 'get',
    params: { file_type: fileType, page, page_size: pageSize, all_files: allFiles }
  });
}

/**
 * 获取目录内容（采用懒加载方式）
 *
 * @param recordId 记录ID
 * @param options 可选参数对象 {directoryPath, fileType}
 * @returns 包含目录下文件和子目录的结果
 */
export function getDirectoryContent(
  recordId: string | number,
  options: { directoryPath?: string; fileType?: string } = {}
) {
  // 规范化路径，确保以斜杠开始和结束
  let normalizedPath = options.directoryPath || '/';

  // 检查这个路径是否是一个文件路径 (有文件扩展名)
  const hasFileExtension = (path: string) => {
    // 移除潜在的尾部斜杠
    const cleanPath = path.endsWith('/') ? path.slice(0, -1) : path;
    const fileName = cleanPath.split('/').pop() || '';
    const parts = fileName.split('.');
    return parts.length > 1 && parts[parts.length - 1].length > 0;
  };

  // 如果路径看起来是一个文件路径 (有扩展名)，尝试获取其所在目录
  if (hasFileExtension(normalizedPath)) {
    normalizedPath = normalizedPath.split('/').slice(0, -1).join('/') || '/';
  }

  // 确保路径以斜杠结束
  if (!normalizedPath.endsWith('/')) {
    normalizedPath += '/';
  }

  const { fileType = 'all' } = options;

  return request<{
    path: string;
    total: number;
    page: number;
    page_size: number;
    items: Array<{
      file_name: string;
      path: string;
      is_directory?: boolean;
      file_type?: string;
      mime_type?: string;
      extension?: string;
      directory?: string;
    }>;
    file_type: string;
    updated_by_version_check: boolean;
  }>({
    url: `/strm/directory/${recordId}`,
    method: 'get',
    params: { directory_path: normalizedPath, file_type: fileType }
  });
}

/**
 * 搜索文件
 *
 * @param recordId 记录ID
 * @param searchText 搜索文本
 * @param options 可选参数，包括文件类型过滤和是否忽略大小写
 * @returns 搜索结果
 */
export function searchFiles(
  recordId: string | number,
  searchText: string,
  options: { fileType?: string; ignoreCase?: boolean } = {}
) {
  const { fileType = 'all', ignoreCase = true } = options;

  return request<{
    search_text: string;
    ignore_case: boolean;
    file_type: string;
    total_matches: number;
    matches: StrmAPI.ParsedFile[];
  }>({
    url: `/strm/search/${recordId}`,
    method: 'get',
    params: { search_text: searchText, file_type: fileType, ignore_case: ignoreCase }
  });
}

/**
 * 获取STRM文件下载链接
 *
 * @param taskId 任务ID
 * @returns 完整的下载URL
 */
export function getStrmDownloadUrl(taskId: string | number): string {
  // 获取基础URL，确保与其他API请求一致
  const baseURL = import.meta.env.VITE_SERVICE_BASE_URL || '';
  return `${baseURL}/strm/download-strm/${taskId}`;
}

/**
 * 获取系统设置
 */
export function getSystemSettings() {
  return request({
    url: '/system-manage/settings',
    method: 'get'
  });
}

/**
 * 更新系统设置
 * @param data 设置数据
 */
export function updateSystemSettings(data: Record<string, any>) {
  return request({
    url: '/system-manage/settings',
    method: 'post',
    data
  });
}

/**
 * 删除上传记录
 * @param recordId 记录ID
 */
export function deleteUploadRecord(recordId: number) {
  return request({
    url: `/strm/history/${recordId}`,
    method: 'delete'
  });
}

/**
 * 获取文件下载URL
 *
 * @param recordId 记录ID
 * @returns 下载URL
 */
export function getDownloadUrl(recordId: number): string {
  // 获取基础URL，确保与其他API请求一致
  const baseURL = import.meta.env.VITE_SERVICE_BASE_URL || '';
  return `${baseURL}/strm/download/${recordId}`;
}

/**
 * 创建媒体服务器
 * @param data 服务器数据
 */
export function createMediaServer(data: Record<string, any>) {
  return request({
    url: '/strm/server',
    method: 'post',
    data
  });
}

/**
 * 更新媒体服务器
 * @param serverId 服务器ID
 * @param data 服务器数据
 */
export function updateMediaServer(serverId: number, data: Record<string, any>) {
  return request({
    url: `/strm/server/${serverId}`,
    method: 'put',
    data
  });
}

/**
 * 删除媒体服务器
 * @param serverId 服务器ID
 */
export function deleteMediaServer(serverId: number) {
  return request({
    url: `/strm/server/${serverId}`,
    method: 'delete'
  });
}

/**
 * 获取任务日志
 * @param taskId 任务ID
 * @param params 查询参数
 */
export function getTaskLogs(taskId: number, params?: {
  page?: number;
  page_size?: number;
  level?: string;
  search?: string;
}) {
  return request({
    url: `/strm/task/${taskId}/logs`,
    method: 'get',
    params
  });
}

/**
 * 测试媒体服务器连接
 * @param serverId 服务器ID（可以为0，表示测试临时服务器）
 * @param tempServer 临时服务器对象（可选，当serverId为0时使用）
 */
export function testServerConnection(serverId: number, tempServer?: Record<string, any>) {
  // 如果是测试新服务器（未保存的服务器）
  if (serverId === 0 && tempServer) {
    // 使用新的API端点测试未保存的服务器
    return request({
      url: `/strm/server/test`,
      method: 'post',
      data: tempServer
    });
  }

  // 测试已有服务器
  return request({
    url: `/strm/server/${serverId}/test`,
    method: 'post'
  });
}

/**
 * WebSocket连接管理器
 * 处理WebSocket连接、认证和消息处理
 */
export class TaskWebSocketManager {
  private ws: WebSocket | null = null;
  private taskId: number;
  private baseUrl: string;
  private eventHandlers: Map<string, Function[]> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5; // 增加最大重试次数
  private reconnectTimeout: number | null = null;
  private isConnected = false;
  private pingInterval: number | null = null; // 添加ping间隔计时器
  private healthCheckTimer: number | null = null; // 添加健康检查计时器
  private lastMessageTime: number = 0; // 记录最后收到消息的时间

  /**
   * 创建WebSocket连接管理器
   * @param taskId 任务ID
   */
  constructor(taskId: number) {
    this.taskId = taskId;

    // 构建WebSocket URL (ws:// 或 wss://)
    const baseUrl = import.meta.env.VITE_SERVICE_BASE_URL || '';

    // 根据当前协议决定使用ws还是wss
    const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';

    // 从HTTP URL转换为WebSocket URL
    this.baseUrl = baseUrl.replace(/^https?:\/\//, `${protocol}://`);

    // 如果baseUrl中包含了端口号或路径，则保留它们
    if (this.baseUrl === '') {
      this.baseUrl = `${protocol}://${window.location.host}`;
    }

    // 初始化最后消息时间
    this.lastMessageTime = Date.now();
  }

  /**
   * 连接到WebSocket服务器
   * @returns 连接是否成功
   */
  connect(): boolean {
    // 确保没有现有连接
    this.disconnect();

    try {
      // 获取认证令牌
      const token = this.getAuthToken();
      if (!token) {
        console.error('无法创建WebSocket连接：缺少有效的认证令牌');
        return false;
      }

      // 创建WebSocket连接
      // 注意：检查baseUrl是否已包含/api前缀，避免路径重复
      const apiPrefix = this.baseUrl.includes('/api') ? '' : '/api';

      // 添加token作为URL查询参数进行认证
      const tokenParam = `token=${encodeURIComponent(token)}`;
      const url = `${this.baseUrl}${apiPrefix}/strm/ws/task/${this.taskId}/events?${tokenParam}`;

      console.log('尝试连接WebSocket:', url.replace(token, 'TOKEN_HIDDEN'));
      this.ws = new WebSocket(url);

      // 设置WebSocket认证头（在open之前）
      // 注意：这种方式并不是标准的WebSocket，因为浏览器API不支持自定义请求头
      // 在实际部署中，可能需要使用服务器代理或其他方式处理认证
      // 此代码假设服务器端有处理子协议的逻辑，通过子协议传递token
      this.ws.onopen = (event) => {
        console.log('WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.lastMessageTime = Date.now(); // 重置最后消息时间

        // 保留发送认证信息的步骤作为备用认证机制
        this.ws?.send(JSON.stringify({
          type: 'authenticate',
          token: token
        }));

        // 触发连接事件
        this.triggerEvent('open', event);

        // 设置定期ping以保持连接活跃
        this.setupPingInterval();

        // 设置健康检查定时器
        this.setupHealthCheck();
      };

      // 处理消息
      this.ws.onmessage = (event) => {
        // 更新最后收到消息的时间
        this.lastMessageTime = Date.now();

        try {
          const message = JSON.parse(event.data);
          const eventType = message.event || 'message';

          // 触发对应类型的事件
          this.triggerEvent(eventType, message.data);

          // 同时触发message事件
          if (eventType !== 'message') {
            this.triggerEvent('message', message);
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
          this.triggerEvent('error', {
            type: 'parse_error',
            message: '解析WebSocket消息失败',
            error: error
          });
        }
      };

      // 处理错误
      this.ws.onerror = (event) => {
        console.error('WebSocket错误:', event);
        this.triggerEvent('error', event);
      };

      // 处理关闭
      this.ws.onclose = (event) => {
        this.isConnected = false;
        console.log('WebSocket连接已关闭:', event.code, event.reason);
        this.triggerEvent('close', event);

        // 清理ping和健康检查定时器
        this.clearTimers();

        // 检查是否需要重新连接
        if (event.code !== 1000 && event.code !== 1001) {
          this.attemptReconnect();
        }
      };

      return true;
    } catch (error) {
      console.error('创建WebSocket连接失败:', error);
      return false;
    }
  }

  /**
   * 设置定期ping以保持连接活跃
   */
  private setupPingInterval() {
    // 清除现有定时器
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    // 每15秒发送一次ping
    this.pingInterval = window.setInterval(() => {
      this.sendPing();
    }, 15000);
  }

  /**
   * 设置健康检查定时器
   */
  private setupHealthCheck() {
    // 清除现有定时器
    if (this.healthCheckTimer) {
      clearTimeout(this.healthCheckTimer);
    }

    // 每30秒检查一次连接健康状况
    this.healthCheckTimer = window.setTimeout(() => {
      this.checkConnectionHealth();
    }, 30000);
  }

  /**
   * 检查连接健康状况
   */
  private checkConnectionHealth() {
    // 如果超过40秒未收到消息，则认为连接可能已断开
    const now = Date.now();
    const elapsed = now - this.lastMessageTime;

    if (elapsed > 40000 && this.isConnected) {
      console.warn(`WebSocket健康检查：${elapsed}ms未收到消息，连接可能已断开`);

      // 触发超时事件
      this.triggerEvent('timeout', { elapsed });

      // 尝试重新连接
      this.isConnected = false;
      if (this.ws) {
        try {
          this.ws.close();
        } catch (e) {
          // 忽略关闭错误
        }
        this.ws = null;
      }

      this.attemptReconnect();
      return;
    }

    // 重新设置健康检查定时器
    if (this.isConnected) {
      this.healthCheckTimer = window.setTimeout(() => {
        this.checkConnectionHealth();
      }, 30000);
    }
  }

  /**
   * 清理所有定时器
   */
  private clearTimers() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }

    if (this.healthCheckTimer) {
      clearTimeout(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    // 清理所有定时器
    this.clearTimers();

    if (this.ws) {
      // 关闭WebSocket连接
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        try {
          this.ws.close();
        } catch (e) {
          console.error('关闭WebSocket连接时出错:', e);
        }
      }

      this.ws = null;
      this.isConnected = false;
    }
  }

  /**
   * 获取当前连接状态
   * @returns 是否已连接
   */
  isConnectedToServer(): boolean {
    return this.isConnected && this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 发送消息到服务器
   * @param data 要发送的数据
   */
  send(data: any): boolean {
    if (!this.isConnectedToServer()) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      this.ws!.send(typeof data === 'string' ? data : JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('发送WebSocket消息失败:', error);
      // 如果发送失败，可能连接已断开，尝试重新连接
      if (this.isConnected) {
        this.isConnected = false;
        this.attemptReconnect();
      }
      return false;
    }
  }

  /**
   * 发送ping消息以保持连接活跃
   */
  sendPing() {
    if (this.isConnectedToServer()) {
      this.send({ type: 'ping', timestamp: Date.now() });
    }
  }

  /**
   * 添加事件监听器
   * @param eventType 事件类型
   * @param handler 事件处理函数
   */
  addEventListener(eventType: string, handler: Function) {
    // 确保事件类型存在
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }

    // 添加处理函数
    const handlers = this.eventHandlers.get(eventType)!;
    handlers.push(handler);
  }

  /**
   * 移除事件监听器
   * @param eventType 事件类型
   * @param handler 事件处理函数，如果未提供则移除所有该类型的处理函数
   */
  removeEventListener(eventType: string, handler?: Function) {
    if (!this.eventHandlers.has(eventType)) {
      return;
    }

    if (!handler) {
      // 移除所有处理函数
      this.eventHandlers.delete(eventType);
    } else {
      // 移除特定处理函数
      const handlers = this.eventHandlers.get(eventType)!;
      const index = handlers.indexOf(handler);
      if (index !== -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param eventType 事件类型
   * @param data 事件数据
   */
  private triggerEvent(eventType: string, data: any) {
    if (!this.eventHandlers.has(eventType)) {
      return;
    }

    // 调用所有处理函数
    const handlers = this.eventHandlers.get(eventType)!;
    handlers.forEach(handler => {
      try {
        handler(data);
      } catch (error) {
        console.error(`执行WebSocket ${eventType}事件处理函数失败:`, error);
      }
    });
  }

  /**
   * 尝试重新连接
   */
  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('WebSocket重连失败：达到最大重试次数');
      this.triggerEvent('reconnectFailed', { attempts: this.reconnectAttempts });
      return;
    }

    // 清理任何现有的重连计时器
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectAttempts++;
    // 使用指数退避策略计算延迟，但最大10秒
    const delay = Math.min(1000 * Math.pow(1.5, this.reconnectAttempts - 1), 10000);

    console.log(`WebSocket连接断开，${delay}ms后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    this.triggerEvent('reconnecting', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.maxReconnectAttempts,
      delay
    });

    this.reconnectTimeout = window.setTimeout(() => {
      this.reconnectTimeout = null;
      // 尝试重新连接
      if (!this.isConnected) {
        const success = this.connect();
        if (!success && this.reconnectAttempts < this.maxReconnectAttempts) {
          // 如果连接失败且未达到最大重试次数，则继续尝试
          this.attemptReconnect();
        }
      }
    }, delay);
  }

  /**
   * 获取认证令牌
   * @returns 认证令牌或null
   */
  private getAuthToken(): string | null {
    // 尝试多种方式获取token
    let token = null;

    try {
      // 1. 尝试使用localStg工具获取(应用正常使用的方式)
      // @ts-ignore - 尝试访问可能存在的全局变量
      if (window.localStg && window.localStg.get) {
        token = window.localStg.get('token');
      }

      // 2. 从localStorage直接获取
      if (!token && window.localStorage) {
        token = localStorage.getItem('token');

        // 3. 尝试获取带前缀的token (如果应用配置了存储前缀)
        if (!token) {
          const prefix = import.meta.env.VITE_STORAGE_PREFIX || '';
          const fullKey = `${prefix}token`;
          token = window.localStorage.getItem(fullKey);
        }
      }

      // 4. 尝试从sessionStorage获取(以防应用使用会话存储)
      if (!token && window.sessionStorage) {
        token = sessionStorage.getItem('token');
      }
    } catch (e) {
      console.error('获取认证令牌时发生错误:', e);
    }

    return token;
  }
}

/**
 * 创建WebSocket任务状态连接
 * @param taskId 任务ID
 * @returns WebSocket连接管理器或null（如果无法创建连接）
 */
export function createTaskStatusWebSocket(taskId: number): TaskWebSocketManager | null {
  try {
    const manager = new TaskWebSocketManager(taskId);
    const success = manager.connect();

    if (success) {
      console.log('WebSocket任务状态连接已创建');

      // 设置30秒ping间隔以保持连接活跃
      setInterval(() => {
        manager.sendPing();
      }, 30000);

      return manager;
    }

    return null;
  } catch (error) {
    console.error('创建WebSocket任务状态连接失败:', error);
    return null;
  }
}

/**
 * 创建WebSocket测试连接
 * @returns WebSocket连接管理器或null（如果无法创建连接）
 */
export function createTestWebSocket(): TaskWebSocketManager | null {
  try {
    // 构建WebSocket URL
    const baseUrl = import.meta.env.VITE_SERVICE_BASE_URL || '';
    const protocol = window.location.protocol === 'https:' ? 'wss' : 'ws';
    const wsBaseUrl = baseUrl.replace(/^https?:\/\//, `${protocol}://`);
    const wsUrl = wsBaseUrl || `${protocol}://${window.location.host}`;

    // 获取认证令牌
    let token = localStorage.getItem('token');
    if (!token) {
      // 尝试从其他可能的存储位置获取token
      const prefix = import.meta.env.VITE_STORAGE_PREFIX || '';
      const fullKey = `${prefix}token`;
      token = localStorage.getItem(fullKey);
    }

    if (!token) {
      console.error('无法创建测试WebSocket连接：缺少有效的认证令牌');
      return null;
    }

    // 创建WebSocket连接
    // 添加token作为URL查询参数进行认证
    const tokenParam = `token=${encodeURIComponent(token)}`;
    const ws = new WebSocket(`${wsUrl}/api/strm/ws/test?${tokenParam}`);

    // 在连接打开时发送认证信息作为备用
    ws.onopen = () => {
      ws.send(JSON.stringify({
        type: 'authenticate',
        token: token
      }));
    };

    // 记录消息
    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log('WebSocket测试消息:', message);
      } catch (error) {
        console.error('解析WebSocket测试消息失败:', error);
      }
    };

    // 处理错误
    ws.onerror = (event) => {
      console.error('WebSocket测试错误:', event);
    };

    // 处理关闭
    ws.onclose = (event) => {
      console.log('WebSocket测试连接已关闭:', event.code, event.reason);
    };

    // 创建通用管理器包装
    const manager = {
      ws,
      isConnected: false,
      disconnect() {
        if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
          ws.close();
        }
      }
    };

    return manager as any;
  } catch (error) {
    console.error('创建WebSocket测试连接失败:', error);
    return null;
  }
}
