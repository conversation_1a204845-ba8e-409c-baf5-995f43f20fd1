import { createFlatRequest } from '@sa/axios';
import { request } from '../request';
import { getAuthorization } from '../request/shared';
import { getServiceBaseURL } from '@/utils/service';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

// 为STRM API创建一个特殊的请求实例，适用于直接返回数据对象的API
const strmRequest = createFlatRequest({
  baseURL
}, {
  async onRequest(config) {
    const Authorization = getAuthorization();
    Object.assign(config.headers, { Authorization });
    return config;
  },
  // STRM API直接返回数据对象，不包含标准的code字段
  isBackendSuccess() {
    return true;
  },
  // 直接返回原始响应数据
  transformBackendResponse(response) {
    return response.data;
  }
});

/**
 * 上传115目录树文件
 *
 * @param file 要上传的文件
 */
export function uploadDirectoryTree(file: File) {
  const formData = new FormData();
  formData.append('file', file);

  return strmRequest<StrmAPI.UploadResult>({
    url: '/api/v1/strm/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: formData
  });
}
