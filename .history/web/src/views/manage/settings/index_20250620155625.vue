<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { getMediaServers, getSystemSettings, updateSystemSettings } from '@/service/api/strm';
import { $t } from '@/locales';
import ServerManagement from '@/components/custom/server-management.vue';

defineOptions({ name: 'ManageSystemSettings' });

const message = useMessage();

// 页面状态
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('basicSettings'); // 当前激活的选项卡: basicSettings 或 serverManagement

// 数据模型
const settings = ref({
  enablePathReplacement: false,
  replacementPath: '',
  downloadThreads: 0,
  outputDirectory: '',
  defaultMediaServerId: null as number | null,
  defaultDownloadServerId: null as number | null,
  videoFileTypes: '',
  audioFileTypes: '',
  imageFileTypes: '',
  subtitleFileTypes: '',
  metadataFileTypes: ''
});

// 服务器列表
const mediaServers = ref<Array<{ label: string; value: number }>>([]);
const downloadServers = ref<Array<{ label: string; value: number }>>([]);

// 获取系统设置
async function fetchSettings() {
  try {
    loading.value = true;
    const res = await getSystemSettings();
    if (res.data) {
      // 设置系统参数
      settings.value = {
        enablePathReplacement: res.data.enablePathReplacement ?? false,
        replacementPath: res.data.replacementPath ?? '',
        downloadThreads: res.data.downloadThreads ?? 0,
        outputDirectory: res.data.outputDirectory ?? '',
        defaultMediaServerId: res.data.default_media_server_id ?? null,
        defaultDownloadServerId: res.data.default_download_server_id ?? null,
        videoFileTypes: res.data.video_file_types ?? '',
        audioFileTypes: res.data.audio_file_types ?? '',
        imageFileTypes: res.data.image_file_types ?? '',
        subtitleFileTypes: res.data.subtitle_file_types ?? '',
        metadataFileTypes: res.data.metadata_file_types ?? ''
      };
    }
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.saveFail'));
  } finally {
    loading.value = false;
  }
}

// 获取服务器列表
async function fetchServers() {
  try {
    // 获取媒体服务器（xiaoyahost类型）
    const mediaRes = await getMediaServers('xiaoyahost');
    if (mediaRes.data && Array.isArray(mediaRes.data)) {
      mediaServers.value = mediaRes.data.map(server => ({
        label: server.name,
        value: server.id
      }));
    }

    // 获取下载服务器（cd2host类型）
    const downloadRes = await getMediaServers('cd2host');
    if (downloadRes.data && Array.isArray(downloadRes.data)) {
      downloadServers.value = downloadRes.data.map(server => ({
        label: server.name,
        value: server.id
      }));
    }
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.saveFail'));
  }
}

// 保存设置
async function saveSettings() {
  try {
    submitting.value = true;

    // 保存系统设置
    await updateSystemSettings({
      enable_path_replacement: settings.value.enablePathReplacement,
      replacement_path: settings.value.replacementPath,
      download_threads: settings.value.downloadThreads,
      output_directory: settings.value.outputDirectory,
      default_media_server_id: settings.value.defaultMediaServerId,
      default_download_server_id: settings.value.defaultDownloadServerId,
      video_file_types: settings.value.videoFileTypes,
      audio_file_types: settings.value.audioFileTypes,
      image_file_types: settings.value.imageFileTypes,
      subtitle_file_types: settings.value.subtitleFileTypes,
      metadata_file_types: settings.value.metadataFileTypes
    });
    message.success($t('strm.settings.saveSuccess'));
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.saveFail'));
  } finally {
    submitting.value = false;
  }
}

// 服务器变更时刷新列表
function handleServersUpdate() {
  fetchServers();
}

// 初始化
onMounted(async () => {
  await Promise.all([fetchSettings(), fetchServers()]);
});
</script>

<template>
  <n-card :title="$t('strm.settings.title')" :bordered="false">
    <n-tabs v-model:value="activeTab" type="line" animated>
      <!-- 基本设置选项卡 -->
      <n-tab-pane name="basicSettings" :tab="$t('strm.settings.basicSettings')">
        <n-spin :show="loading">
          <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging"
            :disabled="submitting" class="pt-20px">

            <!-- 默认下载服务器 -->
            <n-form-item :label="$t('strm.settings.defaultDownloadServer')">
              <n-select v-model:value="settings.defaultDownloadServerId" :options="downloadServers"
                :placeholder="$t('strm.settings.defaultDownloadServerPlaceholder')" clearable />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultDownloadServerHelp')
                  }}</span>
              </template>
            </n-form-item>

            <!-- 默认媒体服务器 -->
            <n-form-item :label="$t('strm.settings.defaultMediaServer')">
              <n-select v-model:value="settings.defaultMediaServerId" :options="mediaServers"
                :placeholder="$t('strm.settings.defaultMediaServerPlaceholder')" clearable />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultMediaServerHelp')
                  }}</span>
              </template>
            </n-form-item>

            <!-- 路径替换开关 -->
            <n-form-item :label="$t('strm.settings.enablePathReplacement')">
              <n-switch v-model:value="settings.enablePathReplacement" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.enablePathReplacementHelp')
                  }}</span>
              </template>
            </n-form-item>

            <!-- 路径替换值，只在开启时显示 -->
            <n-form-item :label="$t('strm.settings.replacementPath')" v-show="settings.enablePathReplacement">
              <n-input v-model:value="settings.replacementPath"
                :placeholder="$t('strm.settings.replacementPathPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.replacementPathHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 下载线程数 -->
            <n-form-item :label="$t('strm.settings.downloadThreads')">
              <n-input-number v-model:value="settings.downloadThreads" :min="1" :max="20" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.downloadThreadsHelp')
                  }}</span>
              </template>
            </n-form-item>

            <!-- 输出目录 -->
            <n-form-item :label="$t('strm.settings.outputDirectory')">
              <n-input v-model:value="settings.outputDirectory"
                :placeholder="$t('strm.settings.outputDirectoryPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.outputDirectoryHelp')
                  }}</span>
              </template>
            </n-form-item>

            <!-- 文件类型分隔线 -->
            <n-divider>{{ $t('strm.settings.fileTypesSettings') }}</n-divider>

            <!-- 视频文件类型 -->
            <n-form-item :label="$t('strm.settings.videoFileTypes')">
              <n-input v-model:value="settings.videoFileTypes"
                :placeholder="$t('strm.settings.videoFileTypesPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 音频文件类型 -->
            <n-form-item :label="$t('strm.settings.audioFileTypes')">
              <n-input v-model:value="settings.audioFileTypes"
                :placeholder="$t('strm.settings.audioFileTypesPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 图片文件类型 -->
            <n-form-item :label="$t('strm.settings.imageFileTypes')">
              <n-input v-model:value="settings.imageFileTypes"
                :placeholder="$t('strm.settings.imageFileTypesPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 字幕文件类型 -->
            <n-form-item :label="$t('strm.settings.subtitleFileTypes')">
              <n-input v-model:value="settings.subtitleFileTypes"
                :placeholder="$t('strm.settings.subtitleFileTypesPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 元数据文件类型 -->
            <n-form-item :label="$t('strm.settings.metadataFileTypes')">
              <n-input v-model:value="settings.metadataFileTypes"
                :placeholder="$t('strm.settings.metadataFileTypesPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 保存按钮 -->
            <n-form-item>
              <n-button type="primary" :loading="submitting" @click="saveSettings">{{
                $t('strm.settings.save') }}</n-button>
            </n-form-item>
          </n-form>
        </n-spin>
      </n-tab-pane>

      <!-- 服务器管理选项卡 -->
      <n-tab-pane name="serverManagement" :tab="$t('strm.settings.serverManagement')">
        <server-management @update:servers="handleServersUpdate" />
      </n-tab-pane>
    </n-tabs>
  </n-card>
</template>

<style scoped>
.pt-20px {
  padding-top: 20px;
}
</style>
