<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { getMediaServers, getSystemSettings, updateSystemSettings } from '@/service/api/strm';
import { $t } from '@/locales';
import ServerManagement from '@/components/custom/server-management.vue';

defineOptions({ name: 'ManageSystemSettings' });

const message = useMessage();

// 页面状态
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('basicSettings'); // 当前激活的选项卡: basicSettings 或 serverManagement
// 添加服务器管理标签页是否已加载数据的标记
const serverTabLoaded = ref(false);

// 记录错误信息
const errorInfo = ref({
  fileType: '', // 出错的文件类型 video, audio, image, subtitle, metadata
  extension: '', // 出错的扩展名，例如 .txt
  conflictType: '' // 冲突的文件类型
});

// 数据模型
const settings = ref({
  enablePathReplacement: false,
  replacementPath: '',
  downloadThreads: 0,
  outputDirectory: '',
  defaultMediaServerId: null as number | null,
  defaultDownloadServerId: null as number | null,
  videoFileTypes: '',
  audioFileTypes: '',
  imageFileTypes: '',
  subtitleFileTypes: '',
  metadataFileTypes: ''
});

// 服务器列表
const mediaServers = ref<Array<{ label: string; value: number }>>([]);
const downloadServers = ref<Array<{ label: string; value: number }>>([]);
// 所有服务器的原始数据，用于传递给服务器管理组件
const allServers = ref<Array<any>>([]);
// 标记服务器列表是否已加载
const serversLoaded = ref(false);

// 文件类型标签数据
const videoFileTypeTags = computed({
  get: () => settings.value.videoFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.videoFileTypes = val.join(','); }
});

const audioFileTypeTags = computed({
  get: () => settings.value.audioFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.audioFileTypes = val.join(','); }
});

const imageFileTypeTags = computed({
  get: () => settings.value.imageFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.imageFileTypes = val.join(','); }
});

const subtitleFileTypeTags = computed({
  get: () => settings.value.subtitleFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.subtitleFileTypes = val.join(','); }
});

const metadataFileTypeTags = computed({
  get: () => settings.value.metadataFileTypes.split(',').filter(item => item.trim()),
  set: (val) => { settings.value.metadataFileTypes = val.join(','); }
});

// 格式化文件扩展名（确保以.开头）
function formatExtension(ext: string): string {
  const trimmed = ext.trim();
  if (!trimmed.startsWith('.') && trimmed !== '') {
    return `.${trimmed}`;
  }
  return trimmed;
}

// 验证文件扩展名是否有效
function validateExtension(ext: string): boolean {
  // 允许空字符串
  if (!ext) return true;
  // 扩展名应该以点开头，后面跟1-10个字母或数字
  const regex = /^\.[a-zA-Z0-9]{1,10}$/;
  return regex.test(ext);
}

// 标签添加前的验证
function beforeAddTag(tag: string, tagType: string): boolean | string {
  const formatted = formatExtension(tag);

  // 验证格式
  if (!validateExtension(formatted)) {
    message.warning('无效的文件扩展名格式');
    return false;
  }

  // 检查当前类型中是否有重复
  const existingTags = getTagsRefByType(tagType).value;
  const hasDuplicate = existingTags.some(t => t.toLowerCase() === formatted.toLowerCase());
  if (hasDuplicate) {
    message.warning('该扩展名已存在');
    return false;
  }

  // 不再检查其他类型中是否有重复，由后端进行验证

  return formatted;
}

// 根据类型获取对应的标签引用
function getTagsRefByType(type: string): typeof videoFileTypeTags {
  switch (type) {
    case 'video': return videoFileTypeTags;
    case 'audio': return audioFileTypeTags;
    case 'image': return imageFileTypeTags;
    case 'subtitle': return subtitleFileTypeTags;
    case 'metadata': return metadataFileTypeTags;
    default: return videoFileTypeTags;
  }
}

// 获取不同文件类型的标签样式
function getTagType(tagType: string): 'success' | 'info' | 'warning' | 'error' | 'default' | 'primary' {
  switch (tagType) {
    case 'video': return 'success';
    case 'audio': return 'info';
    case 'image': return 'warning';
    case 'subtitle': return 'error';
    case 'metadata': return 'primary';
    default: return 'default';
  }
}

// 获取系统设置
async function fetchSettings() {
  try {
    loading.value = true;

    const res = await getSystemSettings();

    if (res.data) {
      // 设置系统参数
      settings.value = {
        enablePathReplacement: res.data.enable_path_replacement ?? false,
        replacementPath: res.data.replacement_path ?? '',
        downloadThreads: res.data.download_threads ?? 0,
        outputDirectory: res.data.output_directory ?? '',
        defaultMediaServerId: res.data.default_media_server_id ?? null,
        defaultDownloadServerId: res.data.default_download_server_id ?? null,
        videoFileTypes: res.data.video_file_types ?? '',
        audioFileTypes: res.data.audio_file_types ?? '',
        imageFileTypes: res.data.image_file_types ?? '',
        subtitleFileTypes: res.data.subtitle_file_types ?? '',
        metadataFileTypes: res.data.metadata_file_types ?? ''
      };

      // 如果需要显示默认服务器设置，则需要加载服务器列表
      if (settings.value.defaultMediaServerId !== null || settings.value.defaultDownloadServerId !== null) {
        await loadServersIfNeeded();
      }
    }
  } catch (err: any) {
    message.error(err?.message || '获取系统设置失败');
  } finally {
    loading.value = false;
  }
}

// 获取服务器列表（仅在需要时调用）
async function fetchServers() {
  if (serversLoaded.value) {
    return; // 如果已加载过，则不重复加载
  }

  try {
    // 获取所有服务器
    const allRes = await getMediaServers();

    if (allRes.data && Array.isArray(allRes.data)) {
      // 保存原始服务器数据
      allServers.value = allRes.data;

      // 过滤出媒体服务器（xiaoyahost类型）
      mediaServers.value = allRes.data
        .filter(server => server.server_type === 'xiaoyahost')
        .map(server => ({
          label: server.name,
          value: server.id
        }));

      // 过滤出下载服务器（cd2host类型）
      downloadServers.value = allRes.data
        .filter(server => server.server_type === 'cd2host')
        .map(server => ({
          label: server.name,
          value: server.id
        }));

      serversLoaded.value = true; // 标记为已加载
    }
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.saveFail'));
  }
}

// 仅在需要时加载服务器列表
async function loadServersIfNeeded() {
  if (!serversLoaded.value) {
    await fetchServers();
  }
}

// 保存设置
async function saveSettings() {
  // 重置错误信息
  errorInfo.value = { fileType: '', extension: '', conflictType: '' };

  submitting.value = true;

  try {
    const requestData = {
      enable_path_replacement: settings.value.enablePathReplacement,
      replacement_path: settings.value.replacementPath,
      download_threads: settings.value.downloadThreads,
      output_directory: settings.value.outputDirectory,
      default_media_server_id: settings.value.defaultMediaServerId,
      default_download_server_id: settings.value.defaultDownloadServerId,
      video_file_types: settings.value.videoFileTypes,
      audio_file_types: settings.value.audioFileTypes,
      image_file_types: settings.value.imageFileTypes,
      subtitle_file_types: settings.value.subtitleFileTypes,
      metadata_file_types: settings.value.metadataFileTypes
    };

    // 使用项目统一的API请求方法
    const response = await updateSystemSettings(requestData);

    // 检查响应是否有错误
    if (response.error) {
      // 如果有错误，提取错误信息并处理，然后返回
      const errorMsg = response.error.response?.data?.msg || '';

      if (errorMsg.includes('文件类型设置有误') || errorMsg.includes('扩展名')) {
        handleFileTypeError(errorMsg);
      }

      // 重要：无论什么错误，保存失败后重新加载服务器数据以保持UI与服务器数据一致
      await fetchSettings();

      return; // 有错误时直接返回，不继续执行
    }

    // 如果没有错误，显示成功消息
    message.success($t('strm.settings.saveSuccess'));

    // 重新加载设置数据
    await fetchSettings();
  } catch (err: any) {
    // 这个catch块不会显示错误消息，因为所有HTTP错误都会被响应对象的error属性捕获
    // 或者被全局拦截器处理

    // 保存失败后也重新加载数据，确保UI状态与服务器一致
    await fetchSettings();
  } finally {
    submitting.value = false;
  }
}

// 处理文件类型错误
function handleFileTypeError(errorMsg: string): void {
  // 解析出错的文件类型和扩展名
  let extensionMatch = errorMsg.match(/扩展名\s+(\.\w+)\s+在\s+(\w+)\s+类型中重复/);
  if (extensionMatch && extensionMatch.length >= 3) {
    const extension = extensionMatch[1]; // 例如 .txt
    const fileTypeText = extensionMatch[2]; // 例如 元数据

    // 将中文文件类型名称转换为英文标识符
    let fileType = '';
    if (fileTypeText === '视频') fileType = 'video';
    else if (fileTypeText === '音频') fileType = 'audio';
    else if (fileTypeText === '图片') fileType = 'image';
    else if (fileTypeText === '字幕') fileType = 'subtitle';
    else if (fileTypeText === '元数据') fileType = 'metadata';

    // 保存错误信息
    errorInfo.value = { fileType, extension, conflictType: '' };
  } else {
    // 尝试匹配不同类型的错误：同一个文件后缀不能属于不同的文件类型
    extensionMatch = errorMsg.match(/扩展名\s+(\.\w+)\s+在\s+(\w+)\s+和\s+(\w+)\s+类型中均存在/);
    if (extensionMatch && extensionMatch.length >= 4) {
      const extension = extensionMatch[1]; // 例如 .svg
      const fileTypeText1 = extensionMatch[2]; // 例如 图片
      const fileTypeText2 = extensionMatch[3]; // 例如 字幕

      // 确定主要冲突类型和次要冲突类型
      let fileType = '';
      let conflictType = '';

      // 转换第一个文件类型
      if (fileTypeText1 === '视频') fileType = 'video';
      else if (fileTypeText1 === '音频') fileType = 'audio';
      else if (fileTypeText1 === '图片') fileType = 'image';
      else if (fileTypeText1 === '字幕') fileType = 'subtitle';
      else if (fileTypeText1 === '元数据') fileType = 'metadata';

      // 转换第二个文件类型
      if (fileTypeText2 === '视频') conflictType = 'video';
      else if (fileTypeText2 === '音频') conflictType = 'audio';
      else if (fileTypeText2 === '图片') conflictType = 'image';
      else if (fileTypeText2 === '字幕') conflictType = 'subtitle';
      else if (fileTypeText2 === '元数据') conflictType = 'metadata';

      // 保存错误信息
      errorInfo.value = { fileType, extension, conflictType };
    }
  }

  // 确保在基本设置标签页
  activeTab.value = 'basicSettings';

  // 等待DOM更新后滚动到出错的文件类型区域
  setTimeout(() => {
    const fileTypeLabel = document.querySelector(`[data-filetype="${errorInfo.value.fileType}"]`);
    if (fileTypeLabel) {
      fileTypeLabel.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, 100);
}

// 检查标签是否有错误
function isTagInError(tagType: string, tagValue: string): boolean {
  return (errorInfo.value.fileType === tagType && errorInfo.value.extension === tagValue) ||
    (errorInfo.value.conflictType === tagType && errorInfo.value.extension === tagValue);
}

// 切换标签页
function handleTabChange(name: string) {
  activeTab.value = name;
  // 如果切换到服务器管理选项卡，检查服务器列表是否已加载
  if (name === 'serverManagement' && (!serversLoaded.value || !allServers.value || allServers.value.length === 0)) {
    // 重置加载标记
    serversLoaded.value = false;
    serverTabLoaded.value = false;

    // 加载服务器列表
    fetchServers().then(() => {
      // 标记服务器管理标签页已加载数据
      serverTabLoaded.value = true;
    });
  }
}

// 服务器变更时刷新列表
async function handleServersUpdate() {
  // 记录当前活动的标签页
  const currentTab = activeTab.value;

  // 重置标记，重新加载服务器列表
  serversLoaded.value = false;
  // 重置服务器管理标签页加载状态
  serverTabLoaded.value = false;

  // 强制刷新所有服务器数据
  await fetchServers();

  // 重新标记为已加载
  serverTabLoaded.value = true;

  // 如果用户在基本设置页面，也需要刷新下拉选择框的选项
  if (currentTab === 'basicSettings') {
    // 重新加载设置，确保服务器下拉列表更新
    await fetchSettings();
  }

  // 显示通知
  message.success('服务器列表已更新');
}

// 初始化
onMounted(async () => {
  // 先加载基本设置
  await fetchSettings();

  // 如果当前选项卡是服务器管理，则加载服务器列表
  if (activeTab.value === 'serverManagement') {
    // 重置加载标记
    serversLoaded.value = false;
    serverTabLoaded.value = false;

    // 加载服务器列表
    await fetchServers();
    // 标记服务器管理标签页已加载数据
    serverTabLoaded.value = true;
  }
});
</script>

<template>
  <n-card :title="$t('strm.settings.title')" :bordered="false">
    <n-tabs v-model:value="activeTab" type="line" animated @update:value="handleTabChange">
      <!-- 基本设置选项卡 -->
      <n-tab-pane name="basicSettings" :tab="$t('strm.settings.basicSettings')">
        <n-spin :show="loading">
          <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging"
            :disabled="submitting" class="pt-20px">

            <!-- 默认下载服务器 -->
            <n-form-item :label="$t('strm.settings.defaultDownloadServer')">
              <n-select v-model:value="settings.defaultDownloadServerId" :options="downloadServers"
                :placeholder="$t('strm.settings.defaultDownloadServerPlaceholder')" clearable
                @focus="loadServersIfNeeded" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultDownloadServerHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 默认媒体服务器 -->
            <n-form-item :label="$t('strm.settings.defaultMediaServer')">
              <n-select v-model:value="settings.defaultMediaServerId" :options="mediaServers"
                :placeholder="$t('strm.settings.defaultMediaServerPlaceholder')" clearable
                @focus="loadServersIfNeeded" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultMediaServerHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 路径替换开关 -->
            <n-form-item :label="$t('strm.settings.enablePathReplacement')">
              <n-switch v-model:value="settings.enablePathReplacement" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.enablePathReplacementHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 路径替换值，只在开启时显示 -->
            <n-form-item :label="$t('strm.settings.replacementPath')" v-show="settings.enablePathReplacement">
              <n-input v-model:value="settings.replacementPath"
                :placeholder="$t('strm.settings.replacementPathPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.replacementPathHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 下载线程数 -->
            <n-form-item :label="$t('strm.settings.downloadThreads')">
              <n-input-number v-model:value="settings.downloadThreads" :min="1" :max="20" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.downloadThreadsHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 输出目录 -->
            <n-form-item :label="$t('strm.settings.outputDirectory')">
              <n-input v-model:value="settings.outputDirectory"
                :placeholder="$t('strm.settings.outputDirectoryPlaceholder')" />
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.outputDirectoryHelp')
                }}</span>
              </template>
            </n-form-item>

            <!-- 文件类型分隔线 -->
            <n-divider>{{ $t('strm.settings.fileTypesSettings') }}</n-divider>

            <!-- 视频文件类型 -->
            <n-form-item :label="$t('strm.settings.videoFileTypes')" data-filetype="video">
              <n-dynamic-tags v-model:value="videoFileTypeTags" :type="getTagType('video')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'video')">
                <template #tag="{ tag, handleClose }">
                  <n-tag :type="getTagType('video')" closable @close="handleClose"
                    :style="isTagInError('video', tag) ? { backgroundColor: '#fff2f0', borderColor: '#ff4d4f', color: '#ff4d4f' } : {}">
                    {{ tag }}
                  </n-tag>
                </template>
              </n-dynamic-tags>
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 音频文件类型 -->
            <n-form-item :label="$t('strm.settings.audioFileTypes')" data-filetype="audio">
              <n-dynamic-tags v-model:value="audioFileTypeTags" :type="getTagType('audio')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'audio')">
                <template #tag="{ tag, handleClose }">
                  <n-tag :type="getTagType('audio')" closable @close="handleClose"
                    :style="isTagInError('audio', tag) ? { backgroundColor: '#fff2f0', borderColor: '#ff4d4f', color: '#ff4d4f' } : {}">
                    {{ tag }}
                  </n-tag>
                </template>
              </n-dynamic-tags>
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 图片文件类型 -->
            <n-form-item :label="$t('strm.settings.imageFileTypes')" data-filetype="image">
              <n-dynamic-tags v-model:value="imageFileTypeTags" :type="getTagType('image')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'image')">
                <template #tag="{ tag, handleClose }">
                  <n-tag :type="getTagType('image')" closable @close="handleClose"
                    :style="isTagInError('image', tag) ? { backgroundColor: '#fff2f0', borderColor: '#ff4d4f', color: '#ff4d4f' } : {}">
                    {{ tag }}
                  </n-tag>
                </template>
              </n-dynamic-tags>
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 字幕文件类型 -->
            <n-form-item :label="$t('strm.settings.subtitleFileTypes')" data-filetype="subtitle">
              <n-dynamic-tags v-model:value="subtitleFileTypeTags" :type="getTagType('subtitle')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'subtitle')">
                <template #tag="{ tag, handleClose }">
                  <n-tag :type="getTagType('subtitle')" closable @close="handleClose"
                    :style="isTagInError('subtitle', tag) ? { backgroundColor: '#fff2f0', borderColor: '#ff4d4f', color: '#ff4d4f' } : {}">
                    {{ tag }}
                  </n-tag>
                </template>
              </n-dynamic-tags>
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 元数据文件类型 -->
            <n-form-item :label="$t('strm.settings.metadataFileTypes')" data-filetype="metadata">
              <n-dynamic-tags v-model:value="metadataFileTypeTags" :type="getTagType('metadata')" :input-props="{
                placeholder: $t('strm.settings.addExtensionPlaceholder')
              }" :add-button-props="{
                dashed: true,
                round: true
              }" :max="20" :before-add-tag="(tag: string) => beforeAddTag(tag, 'metadata')">
                <template #tag="{ tag, handleClose }">
                  <n-tag :type="getTagType('metadata')" closable @close="handleClose"
                    :style="isTagInError('metadata', tag) ? { backgroundColor: '#fff2f0', borderColor: '#ff4d4f', color: '#ff4d4f' } : {}">
                    {{ tag }}
                  </n-tag>
                </template>
              </n-dynamic-tags>
              <template #help>
                <span class="text-xs text-gray-400">{{ $t('strm.settings.fileTypesHelp') }}</span>
              </template>
            </n-form-item>

            <!-- 保存按钮 -->
            <n-form-item>
              <n-button type="primary" :loading="submitting" @click="saveSettings">{{
                $t('strm.settings.save') }}</n-button>
            </n-form-item>
          </n-form>
        </n-spin>
      </n-tab-pane>

      <!-- 服务器管理选项卡 -->
      <n-tab-pane name="serverManagement" :tab="$t('strm.settings.serverManagement')">
        <server-management :auto-load="false" :external-servers="allServers" @update:servers="handleServersUpdate" />
      </n-tab-pane>
    </n-tabs>
  </n-card>
</template>

<style scoped>
.pt-20px {
  padding-top: 20px;
}
</style>
