<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { getSystemSettings, updateSystemSettings, getMediaServers } from '@/service/api/strm';
import { $t } from '@/locales';

defineOptions({ name: 'ManageSystemSettings' });

const message = useMessage();

// 页面状态
const loading = ref(false);
const submitting = ref(false);

// 数据模型
const settings = ref({
    defaultServerId: null as number | null,
    enablePathReplacement: true,
    outputDirectory: ''
});

// 服务器列表
const servers = ref<Array<{ id: number; name: string; serverType: string }>>([]);

// 获取系统设置
async function fetchSettings() {
    try {
        loading.value = true;
        const res = await getSystemSettings();
        if (res.data) {
            settings.value = {
                defaultServerId: res.data.defaultServerId ?? null,
                enablePathReplacement: res.data.enablePathReplacement ?? true,
                outputDirectory: res.data.outputDirectory ?? ''
            };
        }
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        loading.value = false;
    }
}

// 获取服务器列表
async function fetchServers() {
    try {
        const res = await getMediaServers();
        if (res.data && Array.isArray(res.data)) {
            servers.value = res.data.map(server => ({
                id: server.id,
                name: server.name,
                serverType: server.serverType
            }));
        }
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    }
}

// 保存设置
async function saveSettings() {
    try {
        submitting.value = true;
        await updateSystemSettings({
            default_server_id: settings.value.defaultServerId,
            enable_path_replacement: settings.value.enablePathReplacement,
            output_directory: settings.value.outputDirectory
        });
        message.success($t('strm.settings.saveSuccess'));
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        submitting.value = false;
    }
}

// 初始化
onMounted(async () => {
    await Promise.all([fetchSettings(), fetchServers()]);
});
</script>

<template>
    <n-card :title="$t('strm.settings.title')" :bordered="false">
        <n-spin :show="loading">
            <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging"
                :disabled="submitting" class="pt-20px">
                <n-form-item :label="$t('strm.settings.defaultServer')">
                    <n-select v-model:value="settings.defaultServerId"
                        :options="servers.map(server => ({ label: server.name, value: server.id }))"
                        :placeholder="$t('strm.settings.defaultServerPlaceholder')" clearable />
                    <template #help>
                        <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultServerHelp') }}</span>
                    </template>
                </n-form-item>

                <n-form-item :label="$t('strm.settings.enablePathReplacement')">
                    <n-switch v-model:value="settings.enablePathReplacement" />
                    <template #help>
                        <span class="text-xs text-gray-400">{{ $t('strm.settings.enablePathReplacementHelp') }}</span>
                    </template>
                </n-form-item>

                <n-form-item :label="$t('strm.settings.outputDirectory')">
                    <n-input v-model:value="settings.outputDirectory"
                        :placeholder="$t('strm.settings.outputDirectoryPlaceholder')" />
                    <template #help>
                        <span class="text-xs text-gray-400">{{ $t('strm.settings.outputDirectoryHelp') }}</span>
                    </template>
                </n-form-item>

                <n-divider />

                <div class="flex justify-center">
                    <n-button type="primary" :loading="submitting" @click="saveSettings">
                        {{ $t('strm.settings.saveSettings') }}
                    </n-button>
                </div>
            </n-form>
        </n-spin>
    </n-card>
</template>

<style scoped>
.pt-20px {
    padding-top: 20px;
}
</style>