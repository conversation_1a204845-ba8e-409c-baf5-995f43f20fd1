<script setup lang="ts">
import { onMounted, ref, h } from 'vue';
import { useMessage, NTag, NButton, NPopconfirm } from 'naive-ui';
import { getSystemSettings, updateSystemSettings, getMediaServers, deleteMediaServer, createMediaServer, updateMediaServer, testServerConnection } from '@/service/api/strm';
import { $t } from '@/locales';

defineOptions({ name: 'ManageSystemSettings' });

const message = useMessage();

// 页面状态
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('basicSettings'); // 当前激活的选项卡: basicSettings 或 serverManagement

// 数据模型
const settings = ref({
    defaultServerId: null as number | null,
    enablePathReplacement: true,
    outputDirectory: ''
});

// 服务器列表
const servers = ref<Array<{ id: number; name: string; serverType: string; baseUrl: string; isDefault: boolean; description?: string; authRequired: boolean }>>([]);
const loadingServers = ref(false);

// 获取系统设置
async function fetchSettings() {
    try {
        loading.value = true;
        const res = await getSystemSettings();
        if (res.data) {
            settings.value = {
                defaultServerId: res.data.defaultServerId ?? null,
                enablePathReplacement: res.data.enablePathReplacement ?? true,
                outputDirectory: res.data.outputDirectory ?? ''
            };
        }
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        loading.value = false;
    }
}

// 获取服务器列表
async function fetchServers() {
    try {
        loadingServers.value = true;
        const res = await getMediaServers();
        if (res.data && Array.isArray(res.data)) {
            servers.value = res.data.map(server => ({
                id: server.id,
                name: server.name,
                serverType: server.server_type,
                baseUrl: server.base_url,
                isDefault: server.is_default,
                description: server.description || '',
                authRequired: server.auth_required
            }));
        }
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        loadingServers.value = false;
    }
}

// 保存设置
async function saveSettings() {
    try {
        submitting.value = true;
        await updateSystemSettings({
            default_server_id: settings.value.defaultServerId,
            enable_path_replacement: settings.value.enablePathReplacement,
            output_directory: settings.value.outputDirectory
        });
        message.success($t('strm.settings.saveSuccess'));
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        submitting.value = false;
    }
}

// 服务器操作抽屉
const showServerDrawer = ref(false);
const serverDrawerType = ref<'add' | 'edit'>('add');
const currentServerId = ref<number | null>(null);
const serverForm = ref({
    name: '',
    serverType: 'media', // media | download
    baseUrl: '',
    description: '',
    authRequired: false,
    username: '',
    password: ''
});

// 打开添加服务器抽屉
function openAddServerDrawer() {
    serverDrawerType.value = 'add';
    currentServerId.value = null;
    serverForm.value = {
        name: '',
        serverType: 'media',
        baseUrl: '',
        isDefault: false,
        description: '',
        authRequired: false,
        username: '',
        password: ''
    };
    showServerDrawer.value = true;
}

// 打开编辑服务器抽屉
function openEditServerDrawer(server: any) {
    serverDrawerType.value = 'edit';
    currentServerId.value = server.id;
    serverForm.value = {
        name: server.name,
        serverType: server.serverType,
        baseUrl: server.baseUrl,
        description: server.description || '',
        authRequired: server.authRequired,
        username: '', // 后端不会返回敏感信息
        password: ''
    };
    showServerDrawer.value = true;
}

// 提交服务器表单
async function submitServerForm() {
    try {
        submitting.value = true;
        const data = {
            name: serverForm.value.name,
            server_type: serverForm.value.serverType,
            base_url: serverForm.value.baseUrl,
            description: serverForm.value.description,
            auth_required: serverForm.value.authRequired
        };

        if (serverForm.value.authRequired) {
            Object.assign(data, {
                username: serverForm.value.username,
                password: serverForm.value.password
            });
        }

        if (serverDrawerType.value === 'add') {
            await createMediaServer(data);
            message.success($t('strm.settings.addSuccess'));
        } else {
            await updateMediaServer(currentServerId.value!, data);
            message.success($t('strm.settings.updateSuccess'));
        }

        showServerDrawer.value = false;
        fetchServers();
    } catch (error: any) {
        message.error(error.message || (serverDrawerType.value === 'add' ? $t('strm.settings.addError') : $t('strm.settings.updateError')));
    } finally {
        submitting.value = false;
    }
}

// 删除服务器
async function handleDeleteServer(serverId: number) {
    try {
        await deleteMediaServer(serverId);
        message.success($t('strm.settings.deleteSuccess'));
        fetchServers();
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.deleteError'));
    }
}

// 测试服务器连接
async function handleTestConnection(serverId: number) {
    try {
        submitting.value = true;
        await testServerConnection(serverId);
        message.success($t('strm.settings.testSuccess'));
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.testError'));
    } finally {
        submitting.value = false;
    }
}

// 获取服务器类型显示文本
function getServerTypeText(serverType: string) {
    return serverType === 'media'
        ? $t('strm.settings.mediaServer')
        : $t('strm.settings.downloadServer');
}

// 初始化
onMounted(async () => {
    await Promise.all([fetchSettings(), fetchServers()]);
});
</script>

<template>
    <n-card :title="$t('strm.settings.title')" :bordered="false">
        <n-tabs v-model:value="activeTab" type="line" animated>
            <!-- 基本设置选项卡 -->
            <n-tab-pane name="basicSettings" :tab="$t('strm.settings.basicSettings')">
                <n-spin :show="loading">
                    <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging"
                        :disabled="submitting" class="pt-20px">
                        <n-form-item :label="$t('strm.settings.defaultServer')">
                            <n-select v-model:value="settings.defaultServerId"
                                :options="servers.map(server => ({ label: server.name, value: server.id }))"
                                :placeholder="$t('strm.settings.defaultServerPlaceholder')" clearable />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultServerHelp') }}</span>
                            </template>
                        </n-form-item>

                        <n-form-item :label="$t('strm.settings.enablePathReplacement')">
                            <n-switch v-model:value="settings.enablePathReplacement" />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.enablePathReplacementHelp')
                                    }}</span>
                            </template>
                        </n-form-item>

                        <n-form-item :label="$t('strm.settings.outputDirectory')">
                            <n-input v-model:value="settings.outputDirectory"
                                :placeholder="$t('strm.settings.outputDirectoryPlaceholder')" />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.outputDirectoryHelp') }}</span>
                            </template>
                        </n-form-item>

                        <n-divider />

                        <div class="flex justify-center">
                            <n-button type="primary" :loading="submitting" @click="saveSettings">
                                {{ $t('strm.settings.saveSettings') }}
                            </n-button>
                        </div>
                    </n-form>
                </n-spin>
            </n-tab-pane>

            <!-- 服务器管理选项卡 -->
            <n-tab-pane name="serverManagement" :tab="$t('strm.settings.serverManagement')">
                <div class="mb-4 flex justify-between items-center">
                    <h3 class="text-lg font-medium">{{ $t('strm.settings.serverList') }}</h3>
                    <n-button type="primary" @click="openAddServerDrawer">
                        {{ $t('strm.settings.addServer') }}
                    </n-button>
                </div>

                <n-data-table :loading="loadingServers" :columns="[
                    {
                        title: $t('strm.settings.serverName'),
                        key: 'name',
                        ellipsis: {
                            tooltip: true
                        }
                    },
                    {
                        title: $t('strm.settings.serverType'),
                        key: 'serverType',
                        render: (row) => getServerTypeText(row.serverType)
                    },
                    {
                        title: $t('strm.settings.serverUrl'),
                        key: 'baseUrl',
                        ellipsis: {
                            tooltip: true
                        }
                    },
                    {
                        title: $t('strm.settings.isDefault'),
                        key: 'isDefault',
                        render: (row) => row.isDefault ?
                            h(NTag, { type: 'success' }, { default: () => '✓' }) :
                            ''
                    },
                    {
                        title: $t('strm.settings.description'),
                        key: 'description',
                        ellipsis: {
                            tooltip: true
                        }
                    },
                    {
                        title: $t('common.operate'),
                        key: 'actions',
                        fixed: 'right',
                        width: 180,
                        render: (row) => {
                            return h('div', { class: 'flex space-x-2' }, [
                                h(NButton, {
                                    size: 'small',
                                    onClick: () => handleTestConnection(row.id)
                                }, { default: () => $t('strm.settings.testConnection') }),
                                h(NButton, {
                                    size: 'small',
                                    onClick: () => openEditServerDrawer(row)
                                }, { default: () => $t('common.edit') }),
                                h(NPopconfirm, {
                                    onPositiveClick: () => handleDeleteServer(row.id)
                                }, {
                                    trigger: () => h(NButton, {
                                        size: 'small',
                                        type: 'error'
                                    }, { default: () => $t('common.delete') }),
                                    default: () => $t('strm.settings.confirmDelete')
                                })
                            ]);
                        }
                    }
                ]" :data="servers" :row-key="(row) => row.id" :bordered="false" />
            </n-tab-pane>
        </n-tabs>
    </n-card>

    <!-- 服务器操作抽屉 -->
    <n-drawer v-model:show="showServerDrawer" :width="502" :closable="true">
        <n-drawer-content
            :title="serverDrawerType === 'add' ? $t('strm.settings.addServer') : $t('strm.settings.editServer')">
            <n-form ref="formRef" label-placement="left" label-width="auto" require-mark-placement="right-hanging"
                :model="serverForm">
                <n-form-item :label="$t('strm.settings.serverName')" path="name">
                    <n-input v-model:value="serverForm.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
                </n-form-item>

                <n-form-item :label="$t('strm.settings.serverType')" path="serverType">
                    <n-radio-group v-model:value="serverForm.serverType">
                        <n-space>
                            <n-radio value="media">{{ $t('strm.settings.mediaServer') }}</n-radio>
                            <n-radio value="download">{{ $t('strm.settings.downloadServer') }}</n-radio>
                        </n-space>
                    </n-radio-group>
                </n-form-item>

                <n-form-item :label="$t('strm.settings.serverUrl')" path="baseUrl">
                    <n-input v-model:value="serverForm.baseUrl"
                        :placeholder="$t('strm.settings.serverUrlPlaceholder')" />
                </n-form-item>

                <n-form-item :label="$t('strm.settings.isDefault')">
                    <n-switch v-model:value="serverForm.isDefault" />
                </n-form-item>

                <n-form-item :label="$t('strm.settings.description')" path="description">
                    <n-input v-model:value="serverForm.description" type="textarea"
                        :placeholder="$t('strm.settings.descriptionPlaceholder')" />
                </n-form-item>

                <n-form-item :label="$t('strm.settings.authRequired')">
                    <n-switch v-model:value="serverForm.authRequired" />
                </n-form-item>

                <template v-if="serverForm.authRequired">
                    <n-form-item :label="$t('strm.settings.username')" path="username">
                        <n-input v-model:value="serverForm.username"
                            :placeholder="$t('strm.settings.usernamePlaceholder')" />
                    </n-form-item>

                    <n-form-item :label="$t('strm.settings.password')" path="password">
                        <n-input v-model:value="serverForm.password" type="password"
                            :placeholder="$t('strm.settings.passwordPlaceholder')" show-password-on="click" />
                    </n-form-item>
                </template>

                <div class="flex justify-end space-x-2 mt-4">
                    <n-button @click="showServerDrawer = false">
                        {{ $t('strm.settings.cancel') }}
                    </n-button>
                    <n-button type="primary" :loading="submitting" @click="submitServerForm">
                        {{ $t('strm.settings.confirm') }}
                    </n-button>
                </div>
            </n-form>
        </n-drawer-content>
    </n-drawer>
</template>

<style scoped>
.pt-20px {
    padding-top: 20px;
}
</style>
