<template>
    <div class="h-full">
        <n-card title="STRM文件生成" :bordered="false" class="h-full rounded-8px shadow-sm">
            <!-- 文件选择区域 -->
            <n-steps :current="currentStep" :status="stepStatus" vertical>
                <n-step title="选择文件" description="选择已解析的文件用于生成STRM">
                    <n-form v-if="currentStep === 0" label-placement="left" label-width="120px">
                        <n-form-item label="文件选择">
                            <n-select v-model:value="selectedFileId" :options="fileOptions"
                                placeholder="请选择已解析的115目录树文件" :loading="filesLoading" filterable clearable
                                @update:value="handleFileSelected" />
                        </n-form-item>

                        <n-space v-if="fileStats" class="mb-16px">
                            <n-statistic label="总文件数" :value="fileStats.total" />
                            <n-statistic label="视频文件" :value="fileStats.video" />
                            <n-statistic label="音频文件" :value="fileStats.audio" />
                            <n-statistic label="图片文件" :value="fileStats.image" />
                            <n-statistic label="字幕文件" :value="fileStats.subtitle" />
                        </n-space>

                        <div v-if="fileStats && fileStats.video === 0" class="mb-16px">
                            <n-alert type="warning">
                                <template #icon>
                                    <icon-mdi-alert-circle />
                                </template>
                                当前文件中没有视频文件，无法生成STRM文件
                            </n-alert>
                        </div>

                        <n-button type="primary" :disabled="!selectedFileId || !fileStats || fileStats.video === 0"
                            @click="handleNextStep">
                            下一步
                        </n-button>
                    </n-form>
                </n-step>

                <n-step title="配置服务器" description="选择媒体服务器并配置生成选项">
                    <n-form v-if="currentStep === 1" ref="formRef" :model="formModel" label-placement="left"
                        label-width="120px">
                        <n-form-item label="媒体服务器" path="serverId">
                            <n-select v-model:value="formModel.serverId" :options="serverOptions" placeholder="请选择媒体服务器"
                                :loading="serversLoading" filterable clearable />
                        </n-form-item>
                        <n-form-item label="下载服务器" path="downloadServerId">
                            <n-select v-model:value="formModel.downloadServerId" :options="downloadServerOptions"
                                placeholder="请选择下载服务器" :loading="serversLoading" filterable clearable />
                        </n-form-item>
                        <n-form-item label="任务名称" path="name">
                            <n-input v-model:value="formModel.name" placeholder="自定义任务名称（可选）" />
                        </n-form-item>

                        <n-form-item label="下载资源文件" path="downloadResources">
                            <n-switch v-model:value="formModel.downloadResources" />
                        </n-form-item>

                        <n-space>
                            <n-button @click="currentStep = 0">
                                上一步
                            </n-button>
                            <n-button type="primary" :loading="generating"
                                :disabled="!formModel.serverId || !formModel.downloadServerId"
                                @click="handleGenerateStrm">
                                开始生成
                            </n-button>
                        </n-space>
                    </n-form>
                </n-step>

                <n-step title="生成进度" description="STRM文件生成进度和结果">
                    <div v-if="currentStep === 2">
                        <n-space vertical>
                            <div class="flex justify-between">
                                <div>
                                    <div class="text-16px font-medium">{{ taskInfo.name }}</div>
                                    <div class="text-12px text-secondary-text">{{ getStatusText(taskInfo.status) }}
                                    </div>
                                </div>
                                <n-button :disabled="taskInfo.status !== 'running' && taskInfo.status !== 'pending'"
                                    @click="handleCancelTask">
                                    取消任务
                                </n-button>
                            </div>

                            <n-progress type="line" :percentage="taskInfo.progress || 0"
                                :processing="taskInfo.status === 'running'"
                                :status="getProgressStatus(taskInfo.status)" />

                            <n-descriptions label-placement="left" :column="1" size="small" class="mt-16px">
                                <n-descriptions-item label="总文件数">{{ taskInfo.total_files }}</n-descriptions-item>
                                <n-descriptions-item label="资源文件数">{{ taskInfo.resource_files_count || 0
                                    }}</n-descriptions-item>
                                <n-descriptions-item label="已处理文件">{{ taskInfo.processed_files }}</n-descriptions-item>
                                <n-descriptions-item label="成功生成">{{ taskInfo.success_files }}</n-descriptions-item>
                                <n-descriptions-item label="失败文件">{{ taskInfo.failed_files }}</n-descriptions-item>
                                <n-descriptions-item label="开始时间">{{ taskInfo.start_time ?
                                    formatDateTime(taskInfo.start_time) : '未开始'
                                    }}</n-descriptions-item>
                                <n-descriptions-item label="完成时间">{{ taskInfo.end_time ?
                                    formatDateTime(taskInfo.end_time) : '未完成'
                                    }}</n-descriptions-item>
                            </n-descriptions>

                            <!-- 下载按钮 -->
                            <n-space justify="center" class="mt-16px">
                                <n-button type="primary" @click="goToTasksPage">
                                    <template #icon>
                                        <icon-mdi-format-list-bulleted />
                                    </template>
                                    查看任务
                                </n-button>
                            </n-space>
                        </n-space>
                    </div>
                </n-step>
            </n-steps>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import type { FormInst, SelectOption, StepsProps } from 'naive-ui';
import {
    getUploadHistory,
    getParseResult,
    getMediaServers,
    generateStrm,
    getTaskStatus,
    cancelTask,
    getSystemSettings,
    getTaskStatusEventSource
} from '@/service/api/strm';

defineOptions({
    name: 'StrmGenerate'
});

// 路由
const router = useRouter();
const message = useMessage();

// 步骤控制
const currentStep = ref(0);
const stepStatus = ref<StepsProps['status']>('process');

// 文件选择相关
const filesLoading = ref(false);
const fileOptions = ref<SelectOption[]>([]);
const selectedFileId = ref<number | null>(null);
const fileStats = ref<StrmAPI.ParseStats | null>(null);

// 系统设置相关
const systemSettings = ref<Record<string, any> | null>(null);

// 服务器选择相关
const serversLoading = ref(false);
const serverOptions = ref<SelectOption[]>([]);
const downloadServerOptions = ref<SelectOption[]>([]);
const formRef = ref<FormInst | null>(null);
const formModel = ref({
    serverId: null as number | null,
    downloadServerId: null as number | null,
    name: '',
    downloadResources: true // 默认启用下载资源文件
});

// 生成任务相关
const generating = ref(false);
const taskPolling = ref(false);
const taskInfo = ref<StrmAPI.StrmTaskDetail>({
    id: 0,
    name: '',
    status: 'pending',
    total_files: 0,
    processed_files: 0,
    success_files: 0,
    failed_files: 0,
    progress: 0,
    output_dir: '',
    files: [],
    file_count: 0,
    resource_files_count: 0
});

// 状态文本映射
const STATUS_TEXT = {
    pending: '等待开始',
    running: '正在生成',
    completed: '生成完成',
    failed: '生成失败',
    canceled: '已取消'
};

// 获取已上传且已解析的文件列表
const fetchFiles = async () => {
    filesLoading.value = true;
    try {
        const res = await getUploadHistory({
            page: 1,
            page_size: 20
        });

        const records = res.data.records || [];
        // 筛选已解析的文件
        const parsedRecords = records.filter(record => record.status === 'parsed');

        // 转换为选择框选项
        fileOptions.value = parsedRecords.map(record => ({
            label: record.filename,
            value: record.id,
            disabled: false
        }));

        if (fileOptions.value.length === 0) {
            message.warning('没有找到已解析的文件，请先上传并解析文件');
        }
    } catch (error: any) {
        message.error(error.message || '获取文件列表失败');
    } finally {
        filesLoading.value = false;
    }
};

// 获取系统设置
const fetchSystemSettings = async () => {
    try {
        const res = await getSystemSettings();
        systemSettings.value = res.data || null;
    } catch (error: any) {
        message.error(error.message || '获取系统设置失败');
    }
};

// 获取媒体服务器列表
const fetchServers = async () => {
    serversLoading.value = true;
    try {
        const res = await getMediaServers();
        const servers: StrmAPI.MediaServer[] = res.data || [];

        // 转换为Select选项
        const allServerOptions = servers.map(server => ({
            label: `${server.name} (${server.server_type})`,
            value: server.id,
            disabled: false,
            server
        }));

        // 分离媒体服务器和下载服务器选项
        serverOptions.value = allServerOptions;
        downloadServerOptions.value = allServerOptions;

        // 从系统设置中获取默认值
        if (systemSettings.value) {
            // 设置默认媒体服务器
            if (systemSettings.value.default_media_server_id) {
                formModel.value.serverId = systemSettings.value.default_media_server_id;
            } else if (systemSettings.value.default_server_id) {
                // 兼容旧版本
                formModel.value.serverId = systemSettings.value.default_server_id;
            }

            // 设置默认下载服务器
            if (systemSettings.value.default_download_server_id) {
                formModel.value.downloadServerId = systemSettings.value.default_download_server_id;
            } else if (systemSettings.value.default_server_id) {
                // 兼容旧版本，如果没有专门的下载服务器设置，使用通用默认服务器
                formModel.value.downloadServerId = systemSettings.value.default_server_id;
            }
        } else {
            // 如果没有系统设置，尝试使用标记为默认的服务器
            const defaultServer = servers.find(s => s.is_default);
            if (defaultServer) {
                formModel.value.serverId = defaultServer.id;
                formModel.value.downloadServerId = defaultServer.id;
            }
        }

        if (serverOptions.value.length === 0) {
            message.warning('没有找到可用的媒体服务器，请先添加媒体服务器');
        }
    } catch (error: any) {
        message.error(error.message || '获取媒体服务器失败');
    } finally {
        serversLoading.value = false;
    }
};

// 处理文件选择
const handleFileSelected = async (value: number | null) => {
    if (!value) {
        fileStats.value = null;
        return;
    }

    try {
        // 获取文件解析结果
        const res = await getParseResult(value);
        fileStats.value = res.data.stats;
    } catch (error: any) {
        message.error(error.message || '获取文件解析结果失败');
        selectedFileId.value = null;
    }
};

// 下一步
const handleNextStep = () => {
    if (currentStep.value < 2) {
        currentStep.value++;
    }
};

// 获取任务状态
const pollTaskStatus = async () => {
    if (!taskInfo.value.id || taskPolling.value) return;

    taskPolling.value = true;
    try {
        const res = await getTaskStatus(taskInfo.value.id);

        if (!res.data) {
            message.error('获取任务状态失败，将在5秒后重试');
            setTimeout(() => {
                taskPolling.value = false;
                pollTaskStatus();
            }, 5000);
            return;
        }

        // 更新任务信息
        taskInfo.value = {
            ...taskInfo.value,
            ...res.data
        };

        // 计算进度
        if (taskInfo.value.total_files > 0) {
            taskInfo.value.progress = Math.min(
                100,
                Math.round((taskInfo.value.processed_files / taskInfo.value.total_files) * 100)
            );
        }

        // 如果任务还在运行，继续轮询
        if (taskInfo.value.status === 'running' || taskInfo.value.status === 'pending') {
            // 使用递减轮询间隔，初次轮询更频繁，随后减慢
            const pollInterval = taskInfo.value.status === 'pending' ? 1000 : 2000;
            setTimeout(() => {
                taskPolling.value = false;
                pollTaskStatus();
            }, pollInterval);
        } else {
            // 任务完成，停止轮询
            taskPolling.value = false;

            if (taskInfo.value.status === 'completed') {
                message.success('STRM文件生成完成');
                stepStatus.value = 'finish';
            } else if (taskInfo.value.status === 'failed') {
                message.error(taskInfo.value.error_message || 'STRM文件生成失败');
                stepStatus.value = 'error';
            } else if (taskInfo.value.status === 'canceled') {
                message.warning('STRM文件生成已取消');
                stepStatus.value = 'error';
            }
        }
    } catch (error: any) {
        console.error('获取任务状态错误:', error);
        message.error(error.message || '获取任务状态失败');

        // 错误后仍然继续轮询，但使用更长的间隔
        setTimeout(() => {
            taskPolling.value = false;
            pollTaskStatus();
        }, 5000);
    }
};

// 处理生成STRM文件
const handleGenerateStrm = async () => {
    if (!selectedFileId.value) {
        message.warning('请选择一个文件');
        return;
    }

    if (!formModel.value.serverId) {
        message.warning('请选择媒体服务器');
        return;
    }

    if (!formModel.value.downloadServerId) {
        message.warning('请选择下载服务器');
        return;
    }

    generating.value = true;
    try {
        const res = await generateStrm({
            record_id: selectedFileId.value,
            server_id: formModel.value.serverId,
            download_server_id: formModel.value.downloadServerId,
            name: formModel.value.name || undefined,
            download_resources: formModel.value.downloadResources
        });

        if (!res.data) {
            message.error('服务器响应异常，请稍后重试');
            generating.value = false;
            return;
        }

        if (!res.data.task_id) {
            // 处理任务创建失败但API返回成功的情况
            const errorMsg = res.data.error || '创建任务失败，未获取到任务ID';
            message.error(errorMsg);
            generating.value = false;
            return;
        }

        // 更新任务信息
        taskInfo.value = {
            id: res.data.task_id,
            name: res.data.name || '正在初始化...',
            status: res.data.status || 'pending',
            total_files: 0, // 初始值设为0，稍后通过轮询获取实际值
            processed_files: 0,
            success_files: 0,
            failed_files: 0,
            progress: 0,
            output_dir: '',
            files: [],
            file_count: 0,
            resource_files_count: 0
        };

        // 前进到下一步
        currentStep.value = 2;

        // 立即开始轮询任务状态
        pollTaskStatus();

        message.success(res.data.message || '任务创建成功，开始生成STRM文件');
    } catch (error: any) {
        console.error('STRM生成错误:', error);
        message.error(error.message || 'STRM文件生成失败，请稍后重试');
    } finally {
        generating.value = false;
    }
};

// 取消任务
const handleCancelTask = async () => {
    if (!taskInfo.value.id) return;

    try {
        await cancelTask(taskInfo.value.id);
        message.success('任务已取消');

        // 更新任务状态
        taskInfo.value.status = 'canceled';
        stepStatus.value = 'error';
    } catch (error: any) {
        message.error(error.message || '取消任务失败');
    }
};

// 跳转到任务管理页面
const goToTasksPage = () => {
    router.push('/strm/tasks');
};

// 重置函数（目前未使用，保留以备将来使用）
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const handleReset = () => {
    currentStep.value = 0;
    stepStatus.value = 'process';
    selectedFileId.value = null;
    fileStats.value = null;
    formModel.value = {
        serverId: null,
        downloadServerId: null,
        name: '',
        downloadResources: true
    };
    taskInfo.value = {
        id: 0,
        name: '',
        status: 'pending',
        total_files: 0,
        processed_files: 0,
        success_files: 0,
        failed_files: 0,
        progress: 0,
        output_dir: '',
        files: [],
        file_count: 0,
        resource_files_count: 0
    };
};

// 获取状态文本
const getStatusText = (status: string) => {
    return STATUS_TEXT[status as keyof typeof STATUS_TEXT] || status;
};

// 获取进度条状态
const getProgressStatus = (status: string) => {
    if (status === 'completed') return 'success';
    if (status === 'failed' || status === 'canceled') return 'error';
    return 'info';
};

// 格式化日期时间，将ISO格式转换为更易读的格式
const formatDateTime = (dateTimeString: string) => {
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
        return dateTimeString;
    }
};

// 不再需要前端计算资源文件数，直接使用后端返回的数据

// 组件挂载时获取数据
onMounted(() => {
    fetchFiles();
    fetchSystemSettings().then(() => {
        fetchServers();
    });
});
</script>

<style lang="scss" scoped>
.n-statistic {
    margin-right: 20px;
}
</style>
