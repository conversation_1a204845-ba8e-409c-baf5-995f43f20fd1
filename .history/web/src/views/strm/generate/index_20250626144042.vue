<template>
    <div class="h-full p-16px">
        <n-card title="STRM文件生成" :bordered="false" class="h-full rounded-8px shadow-sm">
            <!-- 步骤指示器：从垂直变为水平 -->
            <n-steps :current="currentStep" :status="stepStatus" class="mb-24px">
                <n-step title="选择文件" description="选择已解析的文件" />
                <n-step title="配置服务器" description="选择服务器和生成选项" />
                <n-step title="生成进度" description="文件生成进度和结果" />
            </n-steps>

            <!-- 步骤内容区 -->
            <div class="step-content-area">
                <!-- 步骤1：文件选择 -->
                <div v-if="currentStep === 0" class="step-content">
                    <n-card title="选择文件" class="mb-16px">
                        <n-form label-placement="left" label-width="120px">
                            <n-form-item label="文件选择">
                                <n-select v-model:value="selectedFileId" :options="fileOptions"
                                    placeholder="请选择已解析的115目录树文件" :loading="filesLoading" filterable clearable
                                    @update:value="handleFileSelected" />
                            </n-form-item>
                        </n-form>
                    </n-card>

                    <!-- 文件统计信息卡片 -->
                    <n-card v-if="fileStats" title="文件统计" class="mb-16px">
                        <n-grid :cols="7" :x-gap="12" :y-gap="8">
                            <n-grid-item>
                                <n-statistic label="总文件数" :value="fileStats.total" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="视频文件" :value="fileStats.video" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="音频文件" :value="fileStats.audio" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="图片文件" :value="fileStats.image" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="字幕文件" :value="fileStats.subtitle" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="元数据" :value="fileStats.metadata || 0" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="其他" :value="fileStats.other || 0" />
                            </n-grid-item>
                        </n-grid>

                        <div v-if="fileStats && fileStats.video === 0" class="mt-16px">
                            <n-alert type="warning">
                                <template #icon>
                                    <icon-mdi-alert-circle />
                                </template>
                                当前文件中没有视频文件，无法生成STRM文件
                            </n-alert>
                        </div>
                    </n-card>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end">
                        <n-button type="primary" :disabled="!selectedFileId || !fileStats || fileStats.video === 0"
                            @click="handleNextStep">
                            <template #icon>
                                <icon-mdi-arrow-right />
                            </template>
                            下一步：配置服务器
                        </n-button>
                    </div>
                </div>

                <!-- 步骤2：配置服务器 -->
                <div v-if="currentStep === 1" class="step-content">
                    <n-card title="服务器设置" class="mb-16px">
                        <n-form ref="formRef" :model="formModel" label-placement="left" label-width="120px">
                            <n-grid :cols="2" :x-gap="16">
                                <n-grid-item>
                                    <n-form-item label="媒体服务器" path="serverId">
                                        <n-select v-model:value="formModel.serverId" :options="serverOptions"
                                            placeholder="请选择媒体服务器" :loading="serversLoading" filterable clearable />
                                    </n-form-item>
                                </n-grid-item>
                                <n-grid-item>
                                    <n-form-item label="下载服务器" path="downloadServerId">
                                        <n-select v-model:value="formModel.downloadServerId"
                                            :options="downloadServerOptions" placeholder="请选择下载服务器"
                                            :loading="serversLoading" filterable clearable />
                                    </n-form-item>
                                </n-grid-item>
                            </n-grid>

                            <n-form-item label="任务名称" path="name">
                                <n-input v-model:value="formModel.name" placeholder="自定义任务名称（可选）" />
                            </n-form-item>

                            <n-form-item label="下载资源文件" path="downloadResources">
                                <n-switch v-model:value="formModel.downloadResources" />
                                <span class="ml-8px text-secondary-text">
                                    启用后将下载图片、字幕等资源文件
                                </span>
                            </n-form-item>
                        </n-form>
                    </n-card>

                    <!-- 操作按钮 -->
                    <div class="flex justify-between">
                        <n-button @click="currentStep = 0">
                            <template #icon>
                                <icon-mdi-arrow-left />
                            </template>
                            上一步
                        </n-button>
                        <n-button type="primary" :loading="generating"
                            :disabled="!formModel.serverId || !formModel.downloadServerId" @click="handleGenerateStrm">
                            <template #icon>
                                <icon-mdi-play />
                            </template>
                            开始生成
                        </n-button>
                    </div>
                </div>

                <!-- 步骤3：生成进度 -->
                <div v-if="currentStep === 2" class="step-content">
                    <n-card title="任务进度" class="mb-16px">
                        <div class="flex justify-between mb-16px">
                            <div>
                                <div class="text-18px font-medium">{{ taskInfo.name }}</div>
                                <div class="text-14px text-secondary-text mt-4px">
                                    <n-tag :type="getStatusTagType(taskInfo.status)">
                                        {{ getStatusText(taskInfo.status) }}
                                    </n-tag>
                                </div>
                            </div>
                            <n-button :disabled="taskInfo.status !== 'running' && taskInfo.status !== 'pending'"
                                @click="handleCancelTask" type="warning">
                                <template #icon>
                                    <icon-mdi-cancel />
                                </template>
                                取消任务
                            </n-button>
                        </div>

                        <!-- 进度条 -->
                        <n-progress type="line" :percentage="taskInfo.progress || 0"
                            :processing="taskInfo.status === 'running'" :status="getProgressStatus(taskInfo.status)"
                            :show-indicator="true" class="mb-16px" />

                        <!-- 任务详情 -->
                        <n-grid :cols="3" :x-gap="16">
                            <n-grid-item>
                                <n-statistic label="总文件数" :value="taskInfo.total_files || 0" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="已处理文件" :value="taskInfo.processed_files || 0" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="资源文件数" :value="taskInfo.resource_files_count || 0" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="成功生成" :value="taskInfo.success_files || 0" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="失败文件" :value="taskInfo.failed_files || 0" />
                            </n-grid-item>
                            <n-grid-item v-if="taskInfo.elapsed_time">
                                <n-statistic label="耗时" :value="taskInfo.elapsed_time || '-'" />
                            </n-grid-item>
                        </n-grid>
                    </n-card>

                    <!-- 任务执行信息 -->
                    <n-card title="执行信息" class="mb-16px">
                        <n-descriptions label-placement="left" :column="1" size="small">
                            <n-descriptions-item label="开始时间">{{ taskInfo.start_time ?
                                formatDateTime(taskInfo.start_time) : '未开始'
                                }}</n-descriptions-item>
                            <n-descriptions-item label="完成时间">{{ taskInfo.end_time ?
                                formatDateTime(taskInfo.end_time) : '未完成'
                                }}</n-descriptions-item>
                            <n-descriptions-item v-if="taskInfo.output_dir" label="输出目录">
                                {{ taskInfo.output_dir }}
                            </n-descriptions-item>
                            <n-descriptions-item v-if="taskInfo.error" label="错误信息" class="text-error">
                                {{ taskInfo.error }}
                            </n-descriptions-item>
                        </n-descriptions>
                    </n-card>

                    <!-- 完成操作区 -->
                    <div class="flex justify-center gap-16px">
                        <n-button type="primary" @click="goToTasksPage">
                            <template #icon>
                                <icon-mdi-format-list-bulleted />
                            </template>
                            查看所有任务
                        </n-button>
                        <n-button v-if="taskInfo.status === 'completed'" type="success"
                            :href="getStrmDownloadUrl(taskInfo.id)" tag="a">
                            <template #icon>
                                <icon-mdi-download />
                            </template>
                            下载STRM文件包
                        </n-button>
                        <n-button v-if="['completed', 'failed', 'canceled'].includes(taskInfo.status)"
                            @click="handleReset">
                            <template #icon>
                                <icon-mdi-refresh />
                            </template>
                            创建新任务
                        </n-button>
                    </div>
                </div>
            </div>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import type { FormInst, SelectOption, StepsProps } from 'naive-ui';
import {
    getUploadHistory,
    getParseResult,
    getMediaServers,
    generateStrm,
    getTaskStatus,
    cancelTask,
    getSystemSettings,
    getTaskStatusEventSource,
    createTaskStatusWebSocket,
    TaskWebSocketManager,
    getStrmDownloadUrl as getStrmDownloadUrlApi
} from '@/service/api/strm';

defineOptions({
    name: 'StrmGenerate'
});

// 路由
const router = useRouter();
const message = useMessage();

// 步骤控制
const currentStep = ref(0);
const stepStatus = ref<StepsProps['status']>('process');

// 文件选择相关
const filesLoading = ref(false);
const fileOptions = ref<SelectOption[]>([]);
const selectedFileId = ref<number | null>(null);
const fileStats = ref<StrmAPI.ParseStats | null>(null);

// 系统设置相关
const systemSettings = ref<Record<string, any> | null>(null);

// 服务器选择相关
const serversLoading = ref(false);
const serverOptions = ref<SelectOption[]>([]);
const downloadServerOptions = ref<SelectOption[]>([]);
const formRef = ref<FormInst | null>(null);
const formModel = ref({
    serverId: null as number | null,
    downloadServerId: null as number | null,
    name: '',
    downloadResources: true // 默认启用下载资源文件
});

// 生成任务相关
const generating = ref(false);
const taskPolling = ref(false);
const taskInfo = ref<StrmAPI.StrmTaskDetail>({
    id: 0,
    name: '',
    status: 'pending',
    total_files: 0,
    processed_files: 0,
    success_files: 0,
    failed_files: 0,
    progress: 0,
    output_dir: '',
    files: [],
    file_count: 0,
    resource_files_count: 0
});

// WebSocket连接引用
const wsManager = ref<TaskWebSocketManager | null>(null);
// 是否已连接
const connected = ref(false);

// 状态文本映射
const STATUS_TEXT = {
    pending: '等待开始',
    running: '正在生成',
    completed: '生成完成',
    failed: '生成失败',
    canceled: '已取消'
};

// 获取已上传且已解析的文件列表
const fetchFiles = async () => {
    filesLoading.value = true;
    try {
        const res = await getUploadHistory({
            page: 1,
            page_size: 20
        });

        const records = res.data.records || [];
        // 筛选已解析的文件
        const parsedRecords = records.filter(record => record.status === 'parsed');

        // 转换为选择框选项
        fileOptions.value = parsedRecords.map(record => ({
            label: record.filename,
            value: record.id,
            disabled: false
        }));

        if (fileOptions.value.length === 0) {
            message.warning('没有找到已解析的文件，请先上传并解析文件');
        }
    } catch (error: any) {
        message.error(error.message || '获取文件列表失败');
    } finally {
        filesLoading.value = false;
    }
};

// 获取系统设置
const fetchSystemSettings = async () => {
    try {
        const res = await getSystemSettings();
        systemSettings.value = res.data || null;
    } catch (error: any) {
        message.error(error.message || '获取系统设置失败');
    }
};

// 获取媒体服务器列表
const fetchServers = async () => {
    serversLoading.value = true;
    try {
        const res = await getMediaServers();
        const servers: StrmAPI.MediaServer[] = res.data || [];

        // 转换为Select选项，并根据服务器类型进行过滤
        const allServerOptions = servers.map(server => ({
            label: `${server.name} (${server.server_type})`,
            value: server.id,
            disabled: false,
            server
        }));

        // 根据服务器类型分离媒体服务器和下载服务器选项
        // 媒体服务器：类型为 "xiaoyahost"
        serverOptions.value = allServerOptions.filter(option =>
            option.server.server_type === 'xiaoyahost');

        // 下载服务器：类型为 "cd2host"
        downloadServerOptions.value = allServerOptions.filter(option =>
            option.server.server_type === 'cd2host');

        // 更新选项标签，去除冗余的类型标记
        serverOptions.value = serverOptions.value.map(option => ({
            ...option,
            label: option.server.name
        }));

        downloadServerOptions.value = downloadServerOptions.value.map(option => ({
            ...option,
            label: option.server.name
        }));

        // 从系统设置中获取默认值，确保默认服务器被选中
        if (systemSettings.value) {
            // 设置默认媒体服务器
            if (systemSettings.value.default_media_server_id) {
                formModel.value.serverId = systemSettings.value.default_media_server_id;
                console.log('已设置默认媒体服务器:', systemSettings.value.default_media_server_id);
            } else if (systemSettings.value.default_server_id) {
                // 兼容旧版本
                formModel.value.serverId = systemSettings.value.default_server_id;
                console.log('已设置旧版默认服务器作为媒体服务器:', systemSettings.value.default_server_id);
            }

            // 设置默认下载服务器
            if (systemSettings.value.default_download_server_id) {
                formModel.value.downloadServerId = systemSettings.value.default_download_server_id;
                console.log('已设置默认下载服务器:', systemSettings.value.default_download_server_id);
            } else if (systemSettings.value.default_server_id) {
                // 兼容旧版本，如果没有专门的下载服务器设置，使用通用默认服务器
                formModel.value.downloadServerId = systemSettings.value.default_server_id;
                console.log('已设置旧版默认服务器作为下载服务器:', systemSettings.value.default_server_id);
            }
        } else {
            // 如果没有系统设置，尝试使用标记为默认的服务器
            const defaultMediaServer = servers.find(s => s.is_default && (s.server_type === 'media' || s.server_type === 'both'));
            const defaultDownloadServer = servers.find(s => s.is_default && (s.server_type === 'download' || s.server_type === 'both'));

            if (defaultMediaServer) {
                formModel.value.serverId = defaultMediaServer.id;
                console.log('已设置标记为默认的媒体服务器:', defaultMediaServer.id);
            }

            if (defaultDownloadServer) {
                formModel.value.downloadServerId = defaultDownloadServer.id;
                console.log('已设置标记为默认的下载服务器:', defaultDownloadServer.id);
            }
        }

        // 添加日志记录，帮助调试
        console.log('获取到的所有服务器:', servers);
        console.log('过滤后的媒体服务器选项:', serverOptions.value);
        console.log('过滤后的下载服务器选项:', downloadServerOptions.value);

        if (serverOptions.value.length === 0) {
            console.warn('没有找到xiaoyahost类型的媒体服务器');
            message.warning('没有找到可用的媒体服务器，请先添加媒体服务器(类型为xiaoyahost)');
        }

        if (downloadServerOptions.value.length === 0) {
            console.warn('没有找到cd2host类型的下载服务器');
            message.warning('没有找到可用的下载服务器，请先添加下载服务器(类型为cd2host)');
        }
    } catch (error: any) {
        message.error(error.message || '获取媒体服务器失败');
    } finally {
        serversLoading.value = false;
    }
};

// 处理文件选择
const handleFileSelected = async (value: number | null) => {
    if (!value) {
        fileStats.value = null;
        return;
    }

    try {
        // 获取文件解析结果
        const res = await getParseResult(value);
        fileStats.value = res.data.stats;
    } catch (error: any) {
        message.error(error.message || '获取文件解析结果失败');
        selectedFileId.value = null;
    }
};

// 下一步
const handleNextStep = () => {
    if (currentStep.value < 2) {
        // 如果进入第二步（配置服务器），确保默认服务器已选中
        if (currentStep.value === 0) {
            // 确保已加载服务器选项
            if (!serverOptions.value.length || !downloadServerOptions.value.length) {
                fetchServers();
            }

            // 再次确认默认值已设置
            if (systemSettings.value) {
                // 设置默认媒体服务器 - 即使之前清除了也会重新设置
                if (systemSettings.value.default_media_server_id) {
                    formModel.value.serverId = systemSettings.value.default_media_server_id;
                    console.log('进入第二步：设置默认媒体服务器:', systemSettings.value.default_media_server_id);
                } else if (systemSettings.value.default_server_id) {
                    // 兼容旧版本
                    formModel.value.serverId = systemSettings.value.default_server_id;
                    console.log('进入第二步：设置旧版默认服务器作为媒体服务器:', systemSettings.value.default_server_id);
                }

                // 设置默认下载服务器 - 即使之前清除了也会重新设置
                if (systemSettings.value.default_download_server_id) {
                    formModel.value.downloadServerId = systemSettings.value.default_download_server_id;
                    console.log('进入第二步：设置默认下载服务器:', systemSettings.value.default_download_server_id);
                } else if (systemSettings.value.default_server_id) {
                    // 兼容旧版本
                    formModel.value.downloadServerId = systemSettings.value.default_server_id;
                    console.log('进入第二步：设置旧版默认服务器作为下载服务器:', systemSettings.value.default_server_id);
                }
            }
        }

        currentStep.value++;
    }
};

// 获取任务状态
const pollTaskStatus = async () => {
    if (!taskInfo.value.id || taskPolling.value) return;

    taskPolling.value = true;
    try {
        const res = await getTaskStatus(taskInfo.value.id);

        if (!res.data) {
            message.error('获取任务状态失败，将在5秒后重试');
            setTimeout(() => {
                taskPolling.value = false;
                pollTaskStatus();
            }, 5000);
            return;
        }

        // 更新任务信息
        taskInfo.value = {
            ...taskInfo.value,
            ...res.data
        };

        // 计算进度
        if (taskInfo.value.total_files > 0) {
            taskInfo.value.progress = Math.min(
                100,
                Math.round((taskInfo.value.processed_files / taskInfo.value.total_files) * 100)
            );
        }

        // 如果任务还在运行，继续轮询
        if (taskInfo.value.status === 'running' || taskInfo.value.status === 'pending') {
            // 使用递减轮询间隔，初次轮询更频繁，随后减慢
            const pollInterval = taskInfo.value.status === 'pending' ? 1000 : 2000;
            setTimeout(() => {
                taskPolling.value = false;
                pollTaskStatus();
            }, pollInterval);
        } else {
            // 任务完成，停止轮询
            taskPolling.value = false;

            if (taskInfo.value.status === 'completed') {
                message.success('STRM文件生成完成');
                stepStatus.value = 'finish';
            } else if (taskInfo.value.status === 'failed') {
                message.error(taskInfo.value.error || 'STRM文件生成失败');
                stepStatus.value = 'error';
            } else if (taskInfo.value.status === 'canceled') {
                message.warning('STRM文件生成已取消');
                stepStatus.value = 'error';
            }
        }
    } catch (error: any) {
        console.error('获取任务状态错误:', error);
        message.error(error.message || '获取任务状态失败');

        // 错误后仍然继续轮询，但使用更长的间隔
        setTimeout(() => {
            taskPolling.value = false;
            pollTaskStatus();
        }, 5000);
    }
};

// 设置任务状态事件监听
const setupTaskStatusEvents = () => {
    closeWsConnection(); // 确保先关闭之前的连接

    if (!taskInfo.value.id) {
        message.warning('无法获取任务ID，将使用轮询方式获取状态');
        fallbackToPolling();
        return;
    }

    try {
        // 创建WebSocket连接
        const newWsManager = createTaskStatusWebSocket(taskInfo.value.id);

        // 检查是否成功创建了WebSocket连接
        if (!newWsManager) {
            message.error('无法创建实时连接：未找到有效的认证令牌，请尝试重新登录后再试');
            console.warn('WebSocket连接创建失败提示: 无法创建与服务器的实时连接，请确保您已登录');
            fallbackToPolling();
            return;
        }

        // 保存WebSocket管理器引用
        wsManager.value = newWsManager;

        // 添加事件监听器
        wsManager.value.addEventListener('open', () => {
            connected.value = true;
            console.log('WebSocket连接已建立');
        });

        // 处理初始状态事件
        wsManager.value.addEventListener('initial', (data) => {
            try {
                updateTaskInfo(data);
                console.log('WebSocket初始状态:', data);
            } catch (e) {
                console.error('处理初始状态失败:', e);
            }
        });

        // 处理状态更新事件
        wsManager.value.addEventListener('update', (data) => {
            try {
                updateTaskInfo(data);
                console.log('WebSocket状态更新:', data);
            } catch (e) {
                console.error('处理状态更新失败:', e);
            }
        });

        // 处理错误事件
        wsManager.value.addEventListener('error', (event) => {
            console.error('WebSocket错误:', event);
            // 如果已经轮询失败，则尝试使用轮询
            if (!connected.value && !taskPolling.value) {
                message.warning('实时连接失败，将使用轮询获取任务状态');
                fallbackToPolling();
            }
        });

        // 处理断开连接事件
        wsManager.value.addEventListener('close', (event) => {
            connected.value = false;
            console.log('WebSocket连接已关闭:', event);

            // 在正常关闭的情况下不需要特殊处理
            // 重连逻辑由WebSocket管理器内部处理
        });

        // 处理重连失败事件
        wsManager.value.addEventListener('reconnectFailed', () => {
            message.warning('实时连接失败并达到最大重试次数，将使用轮询获取任务状态');
            fallbackToPolling();
        });

        // 处理连接完成事件
        wsManager.value.addEventListener('complete', (data) => {
            try {
                updateTaskInfo(data);
                console.log('任务完成:', data);
                closeWsConnection();
            } catch (e) {
                console.error('处理完成事件失败:', e);
            }
        });

    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        // 回退到轮询方式
        message.warning('无法建立实时连接，将使用轮询获取任务状态');
        fallbackToPolling();
    }
};

// 关闭WebSocket连接
const closeWsConnection = () => {
    if (wsManager.value) {
        wsManager.value.disconnect();
        wsManager.value = null;
        connected.value = false;
    }
};

// 更新任务信息的辅助函数
const updateTaskInfo = (data: any) => {
    // 更新任务信息
    taskInfo.value = {
        ...taskInfo.value,
        ...data
    };

    // 计算进度（如果后端未提供）
    if (taskInfo.value.total_files > 0 && !taskInfo.value.progress) {
        taskInfo.value.progress = Math.min(
            100,
            Math.round((taskInfo.value.processed_files / taskInfo.value.total_files) * 100)
        );
    }
};

// 回退到轮询模式
const fallbackToPolling = () => {
    taskPolling.value = false;
    pollTaskStatus();
};

// 处理生成STRM文件
const handleGenerateStrm = async () => {
    if (!selectedFileId.value) {
        message.warning('请选择一个文件');
        return;
    }

    if (!formModel.value.serverId) {
        message.warning('请选择媒体服务器');
        return;
    }

    if (!formModel.value.downloadServerId) {
        message.warning('请选择下载服务器');
        return;
    }

    generating.value = true;
    let retryCount = 0;
    const maxRetries = 2; // 最多重试2次

    const requestData = {
        record_id: selectedFileId.value,
        server_id: formModel.value.serverId,
        download_server_id: formModel.value.downloadServerId,
        name: formModel.value.name || undefined,
        download_resources: formModel.value.downloadResources
    };

    async function attemptGenerateStrm() {
        try {
            const res = await generateStrm(requestData);

            if (!res || !res.data) {
                throw new Error('服务器响应异常，请稍后重试');
            }

            if (!res.data.task_id) {
                // 处理任务创建失败但API返回成功的情况
                const errorMsg = res.data.error || '创建任务失败，未获取到任务ID';
                throw new Error(errorMsg);
            }

            // 更新任务信息
            taskInfo.value = {
                id: res.data.task_id,
                name: res.data.name || '正在初始化...',
                status: res.data.status || 'pending',
                total_files: 0, // 初始值设为0，稍后通过轮询获取实际值
                processed_files: 0,
                success_files: 0,
                failed_files: 0,
                progress: 0,
                output_dir: '',
                files: [],
                file_count: 0,
                resource_files_count: 0
            };

            // 前进到下一步
            currentStep.value = 2;

            // 优先使用WebSocket获取实时状态更新
            setupTaskStatusEvents();

            message.success(res.data.message || '任务创建成功，开始生成STRM文件');
            return true;
        } catch (error: any) {
            if (retryCount < maxRetries) {
                retryCount++;
                message.warning(`请求失败，正在进行第${retryCount}次重试...`);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
                return attemptGenerateStrm();
            }

            console.error('STRM生成错误:', error);
            message.error(error.message || 'STRM文件生成失败，请稍后重试');
            return false;
        }
    }

    try {
        await attemptGenerateStrm();
    } finally {
        generating.value = false;
    }
};

// 取消任务
const handleCancelTask = async () => {
    if (!taskInfo.value.id) return;

    try {
        await cancelTask(taskInfo.value.id);
        message.success('任务已取消');

        // 更新任务状态
        taskInfo.value.status = 'canceled';
        stepStatus.value = 'error';
    } catch (error: any) {
        message.error(error.message || '取消任务失败');
    }
};

// 跳转到任务管理页面
const goToTasksPage = () => {
    router.push('/strm/tasks');
};

// 重置函数，已更新为可用
const handleReset = () => {
    currentStep.value = 0;
    stepStatus.value = 'process';

    // 清除文件选择
    selectedFileId.value = null;
    fileStats.value = null;

    // 重置表单（但保留服务器选择以提高用户体验）
    formModel.value = {
        serverId: formModel.value.serverId,
        downloadServerId: formModel.value.downloadServerId,
        name: '',
        downloadResources: true
    };

    // 重置任务信息
    taskInfo.value = {
        id: 0,
        name: '',
        status: 'pending',
        total_files: 0,
        processed_files: 0,
        success_files: 0,
        failed_files: 0,
        progress: 0,
        output_dir: '',
        files: [],
        file_count: 0,
        resource_files_count: 0
    };

    // 关闭WebSocket连接
    closeWsConnection();
    // 停止任务轮询
    taskPolling.value = true;

    // 刷新文件列表
    fetchFiles();
};

// 获取状态文本
const getStatusText = (status: string) => {
    return STATUS_TEXT[status as keyof typeof STATUS_TEXT] || status;
};

// 获取进度条状态
const getProgressStatus = (status: string) => {
    if (status === 'completed') return 'success';
    if (status === 'failed' || status === 'canceled') return 'error';
    return 'info';
};

// 格式化日期时间，将ISO格式转换为更易读的格式
const formatDateTime = (dateTimeString: string) => {
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
        return dateTimeString;
    }
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
    switch (status) {
        case 'completed':
        case 'success':
            return 'success';
        case 'failed':
            return 'error';
        case 'canceled':
            return 'warning';
        case 'running':
            return 'info';
        case 'pending':
            return 'default';
        default:
            return 'default';
    }
};

// 获取STRM文件下载URL
const getStrmDownloadUrl = (taskId: number) => {
    return getStrmDownloadUrlApi(taskId);
};

// 组件挂载时获取数据
onMounted(() => {
    fetchFiles();

    // 先获取系统设置，然后获取服务器列表并设置默认值
    fetchSystemSettings().then(() => {
        fetchServers();

        // 添加额外日志，帮助追踪默认服务器的设置情况
        if (systemSettings.value) {
            console.log('系统设置:', {
                default_media_server_id: systemSettings.value.default_media_server_id,
                default_download_server_id: systemSettings.value.default_download_server_id,
                default_server_id: systemSettings.value.default_server_id
            });
        }
    });
});

// 组件卸载时清理资源
onBeforeUnmount(() => {
    // 关闭WebSocket连接
    closeWsConnection();
    // 取消任务轮询
    taskPolling.value = true;
});
</script>

<style lang="scss" scoped>
.n-statistic {
    margin-right: 20px;
}

.step-content {
    min-height: 300px;
    padding: 8px 0;
    animation: fadeIn 0.3s ease-in-out;
}

.step-content-area {
    margin-top: 24px;
    padding: 0 8px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 卡片样式优化
:deep(.n-card) {
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

// 步骤指示器样式优化
:deep(.n-steps) {
    padding: 8px 0;
    margin-bottom: 24px;
}

// 统计卡片样式
:deep(.n-statistic) {
    .n-statistic__value {
        font-size: 24px;
        font-weight: 600;
    }

    .n-statistic__label {
        font-size: 14px;
    }
}

// 进度条样式
:deep(.n-progress) {
    &.n-progress--line {
        margin: 12px 0;
    }
}

// 标签样式
:deep(.n-tag) {
    padding: 4px 8px;
    font-size: 12px;
}

// 响应式布局调整
@media (max-width: 768px) {
    .step-content-area {
        padding: 0;
    }

    :deep(.n-grid) {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

// 按钮组样式
.flex.justify-center.gap-16px,
.flex.justify-between,
.flex.justify-end {
    margin-top: 20px;

    .n-button {
        min-width: 100px;
    }
}
</style>
