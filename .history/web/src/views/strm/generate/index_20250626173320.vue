<template>
    <div class="h-full p-16px">
        <n-card title="STRM文件生成" :bordered="false" class="h-full rounded-8px shadow-sm">
            <!-- 步骤指示器：从垂直变为水平 -->
            <n-steps :current="currentStep" :status="stepStatus" class="mb-24px">
                <n-step title="选择文件" description="选择已解析的文件" />
                <n-step title="配置服务器" description="选择服务器和生成选项" />
                <n-step title="生成进度" description="文件生成进度和结果" />
            </n-steps>

            <!-- 步骤内容区 -->
            <div class="step-content-area">
                <!-- 步骤1：文件选择 -->
                <div v-if="currentStep === 0" class="step-content">
                    <n-card title="选择文件" class="mb-16px">
                        <n-form label-placement="left" label-width="120px">
                            <n-form-item label="文件选择">
                                <n-select v-model:value="selectedFileId" :options="fileOptions"
                                    placeholder="请选择已解析的115目录树文件" :loading="filesLoading" filterable clearable
                                    @update:value="handleFileSelected" />
                            </n-form-item>
                        </n-form>
                    </n-card>

                    <!-- 文件统计信息卡片 -->
                    <n-card v-if="fileStats" title="文件统计" class="mb-16px">
                        <n-grid :cols="7" :x-gap="12" :y-gap="8">
                            <n-grid-item>
                                <n-statistic label="总文件数" :value="fileStats.total" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="视频文件" :value="fileStats.video" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="音频文件" :value="fileStats.audio" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="图片文件" :value="fileStats.image" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="字幕文件" :value="fileStats.subtitle" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="元数据" :value="fileStats.metadata || 0" />
                            </n-grid-item>
                            <n-grid-item>
                                <n-statistic label="其他" :value="fileStats.other || 0" />
                            </n-grid-item>
                        </n-grid>

                        <div v-if="fileStats && fileStats.video === 0" class="mt-16px">
                            <n-alert type="warning">
                                <template #icon>
                                    <icon-mdi-alert-circle />
                                </template>
                                当前文件中没有视频文件，无法生成STRM文件
                            </n-alert>
                        </div>
                    </n-card>

                    <!-- 操作按钮 -->
                    <div class="flex justify-end">
                        <n-button type="primary" :disabled="!selectedFileId || !fileStats || fileStats.video === 0"
                            @click="handleNextStep">
                            <template #icon>
                                <icon-mdi-arrow-right />
                            </template>
                            下一步：配置服务器
                        </n-button>
                    </div>
                </div>

                <!-- 步骤2：配置服务器 -->
                <div v-if="currentStep === 1" class="step-content">
                    <n-card title="服务器设置" class="mb-16px">
                        <n-form ref="formRef" :model="formModel" label-placement="left" label-width="120px">
                            <n-grid :cols="2" :x-gap="16">
                                <n-grid-item>
                                    <n-form-item label="媒体服务器" path="serverId">
                                        <n-select v-model:value="formModel.serverId" :options="serverOptions"
                                            placeholder="请选择媒体服务器" :loading="serversLoading" filterable clearable />
                                    </n-form-item>
                                </n-grid-item>
                                <n-grid-item>
                                    <n-form-item label="下载服务器" path="downloadServerId">
                                        <n-select v-model:value="formModel.downloadServerId"
                                            :options="downloadServerOptions" placeholder="请选择下载服务器"
                                            :loading="serversLoading" filterable clearable />
                                    </n-form-item>
                                </n-grid-item>
                            </n-grid>

                            <n-form-item label="任务名称" path="name">
                                <n-input v-model:value="formModel.name" placeholder="自定义任务名称（可选）" />
                            </n-form-item>

                            <n-form-item label="下载资源文件" path="downloadResources">
                                <n-switch v-model:value="formModel.downloadResources" />
                                <span class="ml-8px text-secondary-text">
                                    启用后将下载图片、字幕等资源文件
                                </span>
                            </n-form-item>
                        </n-form>
                    </n-card>

                    <!-- 操作按钮 -->
                    <div class="flex justify-between">
                        <n-button @click="currentStep = 0">
                            <template #icon>
                                <icon-mdi-arrow-left />
                            </template>
                            上一步
                        </n-button>
                        <n-button type="primary" :loading="generating"
                            :disabled="!formModel.serverId || !formModel.downloadServerId" @click="handleGenerateStrm">
                            <template #icon>
                                <icon-mdi-play />
                            </template>
                            开始生成
                        </n-button>
                    </div>
                </div>

                <!-- 步骤3：生成进度 -->
                <div v-if="currentStep === 2" class="step-content">
                    <n-card title="任务进度" class="mb-16px">
                        <div class="flex justify-between mb-16px">
                            <div>
                                <div class="text-18px font-medium">{{ taskInfo.name }}</div>
                                <div class="text-14px text-secondary-text mt-4px">
                                    <n-tag :type="getStatusTagType(taskInfo.status)">
                                        {{ getStatusText(taskInfo.status) }}
                                    </n-tag>
                                    <n-tag v-if="connected" class="ml-2" type="success" size="small">
                                        <template #icon>
                                            <icon-mdi-wifi />
                                        </template>
                                        实时连接
                                    </n-tag>
                                    <n-tag v-else-if="taskPolling" class="ml-2" type="warning" size="small">
                                        <template #icon>
                                            <icon-mdi-sync />
                                        </template>
                                        轮询中
                                    </n-tag>
                                    <n-tag v-else class="ml-2" type="error" size="small">
                                        <template #icon>
                                            <icon-mdi-wifi-off />
                                        </template>
                                        连接断开
                                    </n-tag>
                                    <span class="ml-2 text-secondary-text">{{ lastUpdateTimeText }}</span>
                                </div>
                            </div>
                            <div>
                                <n-button size="small" @click="refreshTaskStatus" :loading="refreshing">
                                    <template #icon>
                                        <icon-mdi-refresh />
                                    </template>
                                    刷新
                                </n-button>
                            </div>
                        </div>

                        <n-progress type="line" :percentage="taskInfo.progress" :indicator-placement="'inside'"
                            :processing="taskInfo.status === 'running'" />

                        <div class="grid grid-cols-2 gap-4 mt-16px">
                            <n-statistic label="总文件数">
                                <n-tooltip trigger="hover" placement="top">
                                    <template #trigger>
                                        <div class="flex items-center">
                                            {{ taskInfo.total_files || 0 }}
                                            <n-icon class="ml-1 text-primary cursor-pointer">
                                                <icon-mdi-information-outline />
                                            </n-icon>
                                        </div>
                                    </template>
                                    <div>
                                        <div>STRM文件: {{ taskInfo.strm_files_count || 0 }}</div>
                                        <div>资源文件: {{ taskInfo.resource_files_count || 0 }}</div>
                                        <div>视频文件: {{ taskInfo.video_files_count || 0 }}</div>
                                    </div>
                                </n-tooltip>
                            </n-statistic>
                            <n-statistic label="已处理文件">
                                {{ taskInfo.processed_files || 0 }} / {{ taskInfo.total_files || 0 }}
                            </n-statistic>
                            <n-statistic label="成功生成">
                                <n-tooltip trigger="hover" placement="top">
                                    <template #trigger>
                                        <div class="flex items-center text-success">
                                            {{ taskInfo.success_files || 0 }}
                                            <n-icon class="ml-1 text-primary cursor-pointer">
                                                <icon-mdi-information-outline />
                                            </n-icon>
                                        </div>
                                    </template>
                                    <div>
                                        <div>STRM文件: {{ taskInfo.strm_success || 0 }}</div>
                                        <div>资源文件: {{ taskInfo.resource_success || 0 }}</div>
                                    </div>
                                </n-tooltip>
                            </n-statistic>
                            <n-statistic label="失败文件">
                                <n-tooltip trigger="hover" placement="top">
                                    <template #trigger>
                                        <div class="flex items-center text-error">
                                            {{ taskInfo.failed_files || 0 }}
                                            <n-icon class="ml-1 text-primary cursor-pointer">
                                                <icon-mdi-information-outline />
                                            </n-icon>
                                        </div>
                                    </template>
                                    <div>
                                        <div>STRM文件: {{ taskInfo.strm_failed || 0 }}</div>
                                        <div>资源文件: {{ taskInfo.resource_failed || 0 }}</div>
                                    </div>
                                </n-tooltip>
                            </n-statistic>
                        </div>

                        <div v-if="taskInfo.elapsed_time" class="mt-16px">
                            <n-statistic label="耗时">
                                {{ taskInfo.elapsed_time }}
                            </n-statistic>
                        </div>

                        <div v-if="taskInfo.error" class="mt-16px">
                            <n-alert title="处理出错" type="error">
                                {{ taskInfo.error }}
                            </n-alert>
                        </div>

                        <div class="mt-16px">
                            <n-space>
                                <n-button type="primary" :disabled="!isTaskCompleted || !hasSuccessFiles"
                                    @click="downloadFiles">
                                    <template #icon>
                                        <icon-mdi-download />
                                    </template>
                                    下载文件
                                </n-button>
                                <n-button type="error" :disabled="!isTaskActive" @click="handleCancelTask">
                                    <template #icon>
                                        <icon-mdi-cancel />
                                    </template>
                                    取消任务
                                </n-button>
                                <n-button @click="goToHistory">
                                    <template #icon>
                                        <icon-mdi-history />
                                    </template>
                                    历史任务
                                </n-button>
                            </n-space>
                        </div>
                    </n-card>
                </div>
            </div>
        </n-card>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, onBeforeUnmount, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMessage } from 'naive-ui';
import type { FormInst, SelectOption, StepsProps } from 'naive-ui';
import {
    getUploadHistory,
    getParseResult,
    getMediaServers,
    generateStrm,
    getTaskStatus,
    cancelTask,
    getSystemSettings,
    createTaskStatusWebSocket,
    TaskWebSocketManager,
    getStrmDownloadUrl as getStrmDownloadUrlApi
} from '@/service/api/strm';

defineOptions({
    name: 'StrmGenerate'
});

// 路由
const router = useRouter();
const message = useMessage();

// 步骤控制
const currentStep = ref(0);
const stepStatus = ref<StepsProps['status']>('process');

// 文件选择相关
const filesLoading = ref(false);
const fileOptions = ref<SelectOption[]>([]);
const selectedFileId = ref<number | null>(null);
const fileStats = ref<StrmAPI.ParseStats | null>(null);

// 系统设置相关
const systemSettings = ref<Record<string, any> | null>(null);

// 服务器选择相关
const serversLoading = ref(false);
const serverOptions = ref<SelectOption[]>([]);
const downloadServerOptions = ref<SelectOption[]>([]);
const formRef = ref<FormInst | null>(null);
const formModel = ref({
    serverId: null as number | null,
    downloadServerId: null as number | null,
    name: '',
    downloadResources: true // 默认启用下载资源文件
});

// 任务生成相关
const generating = ref(false);
const taskPolling = ref(false);
const refreshing = ref(false);
const noUpdateCounter = ref(0);
const lastUpdateTime = ref<Date | null>(null);
const taskInfo = ref<StrmAPI.StrmTaskDetail>({
    id: 0,
    name: '',
    status: 'pending',
    total_files: 0,
    processed_files: 0,
    success_files: 0,
    failed_files: 0,
    progress: 0,
    output_dir: '',
    files: [],
    file_count: 0,
    resource_files_count: 0
});

// WebSocket连接引用
const wsManager = ref<TaskWebSocketManager | null>(null);
// 是否已连接
const connected = ref(false);
// 最大无更新次数，超过这个次数将强制刷新
const MAX_NO_UPDATE_COUNT = 5;

// 获取最后更新时间文本
const lastUpdateTimeText = computed(() => {
    if (!lastUpdateTime.value) return '等待更新...';

    // 计算时间差
    const now = new Date();
    const diff = Math.floor((now.getTime() - lastUpdateTime.value.getTime()) / 1000);

    if (diff < 5) return '刚刚更新';
    if (diff < 60) return `${diff}秒前`;
    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
    return formatDateTime(lastUpdateTime.value.toISOString());
});

// 状态文本映射
const STATUS_TEXT = {
    pending: '等待开始',
    running: '正在生成',
    completed: '生成完成',
    failed: '生成失败',
    canceled: '已取消'
};

// 获取已上传且已解析的文件列表
const fetchFiles = async () => {
    filesLoading.value = true;
    try {
        const res = await getUploadHistory({
            page: 1,
            page_size: 20
        });

        const records = res.data.records || [];
        // 筛选已解析的文件
        const parsedRecords = records.filter(record => record.status === 'parsed');

        // 转换为选择框选项
        fileOptions.value = parsedRecords.map(record => ({
            label: record.filename,
            value: record.id,
            disabled: false
        }));

        if (fileOptions.value.length === 0) {
            message.warning('没有找到已解析的文件，请先上传并解析文件');
        }
    } catch (error: any) {
        message.error(error.message || '获取文件列表失败');
    } finally {
        filesLoading.value = false;
    }
};

// 获取系统设置
const fetchSystemSettings = async () => {
    try {
        const res = await getSystemSettings();
        systemSettings.value = res.data || null;
    } catch (error: any) {
        message.error(error.message || '获取系统设置失败');
    }
};

// 获取媒体服务器列表
const fetchServers = async () => {
    serversLoading.value = true;
    try {
        const res = await getMediaServers();
        const servers: StrmAPI.MediaServer[] = res.data || [];

        // 转换为Select选项，并根据服务器类型进行过滤
        const allServerOptions = servers.map(server => ({
            label: `${server.name} (${server.server_type})`,
            value: server.id,
            disabled: false,
            server
        }));

        // 根据服务器类型分离媒体服务器和下载服务器选项
        // 媒体服务器：类型为 "xiaoyahost"
        serverOptions.value = allServerOptions.filter(option =>
            option.server.server_type === 'xiaoyahost');

        // 下载服务器：类型为 "cd2host"
        downloadServerOptions.value = allServerOptions.filter(option =>
            option.server.server_type === 'cd2host');

        // 更新选项标签，去除冗余的类型标记
        serverOptions.value = serverOptions.value.map(option => ({
            ...option,
            label: option.server.name
        }));

        downloadServerOptions.value = downloadServerOptions.value.map(option => ({
            ...option,
            label: option.server.name
        }));

        // 从系统设置中获取默认值，确保默认服务器被选中
        if (systemSettings.value) {
            // 设置默认媒体服务器
            if (!formModel.value.serverId) {
                if (systemSettings.value.default_media_server_id) {
                    formModel.value.serverId = systemSettings.value.default_media_server_id;
                } else if (systemSettings.value.default_server_id) {
                    // 兼容旧版本
                    formModel.value.serverId = systemSettings.value.default_server_id;
                }
            }

            // 设置默认下载服务器
            if (!formModel.value.downloadServerId) {
                if (systemSettings.value.default_download_server_id) {
                    formModel.value.downloadServerId = systemSettings.value.default_download_server_id;
                } else if (systemSettings.value.default_server_id) {
                    // 兼容旧版本
                    formModel.value.downloadServerId = systemSettings.value.default_server_id;
                }
            }
        } else {
            // 如果没有系统设置，尝试使用标记为默认的服务器
            const defaultMediaServer = servers.find(s => s.is_default && (s.server_type === 'media' || s.server_type === 'both'));
            const defaultDownloadServer = servers.find(s => s.is_default && (s.server_type === 'download' || s.server_type === 'both'));

            if (!formModel.value.serverId && defaultMediaServer) {
                formModel.value.serverId = defaultMediaServer.id;
            }

            if (!formModel.value.downloadServerId && defaultDownloadServer) {
                formModel.value.downloadServerId = defaultDownloadServer.id;
            }
        }

        // 检查服务器可用性
        if (serverOptions.value.length === 0) {
            message.warning('没有找到xiaoyahost类型的媒体服务器');
        }

        if (downloadServerOptions.value.length === 0) {
            message.warning('没有找到cd2host类型的下载服务器');
        }
    } catch (error: any) {
        message.error(error.message || '获取媒体服务器失败');
    } finally {
        serversLoading.value = false;
    }
};

// 处理文件选择
const handleFileSelected = async (value: number | null) => {
    if (!value) {
        fileStats.value = null;
        return;
    }

    try {
        // 获取文件解析结果
        const res = await getParseResult(value);
        fileStats.value = res.data.stats;
    } catch (error: any) {
        message.error(error.message || '获取文件解析结果失败');
        selectedFileId.value = null;
    }
};

// 下一步
const handleNextStep = () => {
    if (currentStep.value < 2) {
        // 如果进入第二步（配置服务器），确保默认服务器已选中
        if (currentStep.value === 0) {
            // 确保已加载服务器选项
            if (!serverOptions.value.length || !downloadServerOptions.value.length) {
                fetchServers();
            }

            // 再次确认默认值已设置，只在未选择服务器时应用默认值
            if (systemSettings.value) {
                // 设置默认媒体服务器 - 只在当前未选择媒体服务器时设置默认值
                if (!formModel.value.serverId) {
                    if (systemSettings.value.default_media_server_id) {
                        formModel.value.serverId = systemSettings.value.default_media_server_id;
                    } else if (systemSettings.value.default_server_id) {
                        // 兼容旧版本
                        formModel.value.serverId = systemSettings.value.default_server_id;
                    }
                }

                // 设置默认下载服务器 - 只在当前未选择下载服务器时设置默认值
                if (!formModel.value.downloadServerId) {
                    if (systemSettings.value.default_download_server_id) {
                        formModel.value.downloadServerId = systemSettings.value.default_download_server_id;
                    } else if (systemSettings.value.default_server_id) {
                        // 兼容旧版本
                        formModel.value.downloadServerId = systemSettings.value.default_server_id;
                    }
                }
            }
        }

        currentStep.value++;
    }
};

// 获取任务状态
const pollTaskStatus = async () => {
    if (!taskInfo.value.id || taskPolling.value) return;

    taskPolling.value = true;
    try {
        const res = await getTaskStatus(taskInfo.value.id);

        if (!res.data) {
            message.error('获取任务状态失败，将在5秒后重试');
            setTimeout(() => {
                taskPolling.value = false;
                pollTaskStatus();
            }, 5000);
            return;
        }

        // 更新任务信息
        updateTaskInfo(res.data);

        // 计算进度
        if (taskInfo.value.total_files > 0) {
            taskInfo.value.progress = Math.min(
                100,
                Math.round((taskInfo.value.processed_files / taskInfo.value.total_files) * 100)
            );
        }

        // 如果任务还在运行，继续轮询
        if (taskInfo.value.status === 'running' || taskInfo.value.status === 'pending') {
            // 使用递减轮询间隔，初次轮询更频繁，随后减慢
            const pollInterval = taskInfo.value.status === 'pending' ? 1000 : 2000;

            // 增加无更新检测
            noUpdateCounter.value++;
            if (noUpdateCounter.value >= MAX_NO_UPDATE_COUNT) {
                // 如果连续多次没有进度更新，延长轮询间隔
                console.warn(`连续${MAX_NO_UPDATE_COUNT}次未检测到进度更新，可能任务处理缓慢或出现问题`);
                noUpdateCounter.value = 0;
            }

            setTimeout(() => {
                taskPolling.value = false;
                pollTaskStatus();
            }, pollInterval);
        } else {
            // 任务完成，停止轮询
            taskPolling.value = false;

            if (taskInfo.value.status === 'completed') {
                message.success('STRM文件生成完成');
                stepStatus.value = 'finish';
            } else if (taskInfo.value.status === 'failed') {
                message.error(taskInfo.value.error || 'STRM文件生成失败');
                stepStatus.value = 'error';
            } else if (taskInfo.value.status === 'canceled') {
                message.warning('STRM文件生成已取消');
                stepStatus.value = 'error';
            }
        }
    } catch (error: any) {
        console.error('获取任务状态错误:', error);
        message.error(error.message || '获取任务状态失败');

        // 错误后仍然继续轮询，但使用更长的间隔
        setTimeout(() => {
            taskPolling.value = false;
            pollTaskStatus();
        }, 5000);
    }
};

// 更新任务信息的辅助函数
const updateTaskInfo = (data: any) => {
    if (!data) return;

    // 更新任务信息
    taskInfo.value = {
        ...taskInfo.value,
        ...data
    };

    // 计算进度（如果后端未提供）
    if (taskInfo.value.total_files > 0 && !taskInfo.value.progress) {
        taskInfo.value.progress = Math.min(
            100,
            Math.round((taskInfo.value.processed_files / taskInfo.value.total_files) * 100)
        );
    }

    // 更新最后更新时间
    lastUpdateTime.value = new Date();
};

// 手动刷新任务状态
const handleManualRefresh = async () => {
    if (!taskInfo.value.id || refreshing.value) return;

    refreshing.value = true;
    try {
        // 直接获取最新状态
        const res = await getTaskStatus(taskInfo.value.id);

        if (!res.data) {
            message.warning('获取任务状态失败');
            return;
        }

        // 更新任务信息
        updateTaskInfo(res.data);
        message.success('已刷新任务状态');

        // 如果当前没有轮询或WebSocket连接，则启动轮询
        if (!connected.value && !taskPolling.value) {
            // 只有在任务仍在运行中时才重新启动轮询
            if (taskInfo.value.status === 'running' || taskInfo.value.status === 'pending') {
                message.info('重新启动状态更新');
                fallbackToPolling();
            }
        }
    } catch (error: any) {
        message.error(error.message || '刷新状态失败');
    } finally {
        refreshing.value = false;
    }
};

// 处理生成STRM文件
const handleGenerateStrm = async () => {
    if (!selectedFileId.value) {
        message.warning('请选择一个文件');
        return;
    }

    if (!formModel.value.serverId) {
        message.warning('请选择媒体服务器');
        return;
    }

    if (!formModel.value.downloadServerId) {
        message.warning('请选择下载服务器');
        return;
    }

    generating.value = true;
    let retryCount = 0;
    const maxRetries = 2; // 最多重试2次

    const requestData = {
        record_id: selectedFileId.value,
        server_id: formModel.value.serverId,
        download_server_id: formModel.value.downloadServerId,
        name: formModel.value.name || undefined,
        download_resources: formModel.value.downloadResources
    };

    async function attemptGenerateStrm() {
        try {
            const res = await generateStrm(requestData);

            if (!res || !res.data) {
                throw new Error('服务器响应异常，请稍后重试');
            }

            if (!res.data.task_id) {
                // 处理任务创建失败但API返回成功的情况
                const errorMsg = res.data.error || '创建任务失败，未获取到任务ID';
                throw new Error(errorMsg);
            }

            // 更新任务信息
            taskInfo.value = {
                id: res.data.task_id,
                name: res.data.name || '正在初始化...',
                status: res.data.status || 'pending',
                total_files: 0, // 初始值设为0，稍后通过轮询获取实际值
                processed_files: 0,
                success_files: 0,
                failed_files: 0,
                progress: 0,
                output_dir: '',
                files: [],
                file_count: 0,
                resource_files_count: 0
            };

            // 前进到下一步
            currentStep.value = 2;

            // 优先使用WebSocket获取实时状态更新
            setupTaskStatusEvents();

            message.success(res.data.message || '任务创建成功，开始生成STRM文件');
            return true;
        } catch (error: any) {
            if (retryCount < maxRetries) {
                retryCount++;
                message.warning(`请求失败，正在进行第${retryCount}次重试...`);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒后重试
                return attemptGenerateStrm();
            }

            console.error('STRM生成错误:', error);
            message.error(error.message || 'STRM文件生成失败，请稍后重试');
            return false;
        }
    }

    try {
        await attemptGenerateStrm();
    } finally {
        generating.value = false;
    }
};

// 取消任务
const handleCancelTask = async () => {
    if (!taskInfo.value.id) return;

    try {
        await cancelTask(taskInfo.value.id);
        message.success('任务已取消');

        // 更新任务状态
        taskInfo.value.status = 'canceled';
        stepStatus.value = 'error';
    } catch (error: any) {
        message.error(error.message || '取消任务失败');
    }
};

// 跳转到任务管理页面
const goToTasksPage = () => {
    router.push('/strm/tasks');
};

// 重置函数，已更新为可用
const handleReset = () => {
    currentStep.value = 0;
    stepStatus.value = 'process';

    // 清除文件选择
    selectedFileId.value = null;
    fileStats.value = null;

    // 重置表单（但保留服务器选择以提高用户体验）
    formModel.value = {
        serverId: formModel.value.serverId,
        downloadServerId: formModel.value.downloadServerId,
        name: '',
        downloadResources: true
    };

    // 重置任务信息
    taskInfo.value = {
        id: 0,
        name: '',
        status: 'pending',
        total_files: 0,
        processed_files: 0,
        success_files: 0,
        failed_files: 0,
        progress: 0,
        output_dir: '',
        files: [],
        file_count: 0,
        resource_files_count: 0
    };

    // 关闭WebSocket连接
    closeWsConnection();
    // 停止任务轮询
    taskPolling.value = true;

    // 刷新文件列表
    fetchFiles();
};

// 获取状态文本
const getStatusText = (status: string) => {
    return STATUS_TEXT[status as keyof typeof STATUS_TEXT] || status;
};

// 获取进度条状态
const getProgressStatus = (status: string) => {
    if (status === 'completed') return 'success';
    if (status === 'failed' || status === 'canceled') return 'error';
    return 'info';
};

// 格式化日期时间，将ISO格式转换为更易读的格式
const formatDateTime = (dateTimeString: string) => {
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (error) {
        return dateTimeString;
    }
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
    switch (status) {
        case 'completed':
        case 'success':
            return 'success';
        case 'failed':
            return 'error';
        case 'canceled':
            return 'warning';
        case 'running':
            return 'info';
        case 'pending':
            return 'default';
        default:
            return 'default';
    }
};

// 获取STRM文件下载URL
const getStrmDownloadUrl = (taskId: number) => {
    return getStrmDownloadUrlApi(taskId);
};

// 设置任务状态事件监听
const setupTaskStatusEvents = () => {
    closeWsConnection(); // 确保先关闭之前的连接

    if (!taskInfo.value.id) {
        message.warning('无法获取任务ID，将使用轮询方式获取状态');
        fallbackToPolling();
        return;
    }

    try {
        // 创建WebSocket连接
        const newWsManager = createTaskStatusWebSocket(taskInfo.value.id);

        // 检查是否成功创建了WebSocket连接
        if (!newWsManager) {
            message.error('无法创建实时连接：未找到有效的认证令牌，请尝试重新登录后再试');
            fallbackToPolling();
            return;
        }

        // 保存WebSocket管理器引用
        wsManager.value = newWsManager;

        // 添加事件监听器
        wsManager.value.addEventListener('open', () => {
            connected.value = true;
            console.log('WebSocket连接已建立');
            // 连接成功时清除之前的轮询
            taskPolling.value = true;
        });

        // 处理初始状态事件
        wsManager.value.addEventListener('initial', (data) => {
            try {
                updateTaskInfo(data);
                console.log('WebSocket初始状态:', data);
            } catch (e) {
                console.error('处理初始状态失败:', e);
            }
        });

        // 处理状态更新事件
        wsManager.value.addEventListener('update', (data) => {
            try {
                updateTaskInfo(data);
                console.log('WebSocket状态更新:', data);
                // 重置无更新计数器
                noUpdateCounter.value = 0;
            } catch (e) {
                console.error('处理状态更新失败:', e);
            }
        });

        // 处理错误事件
        wsManager.value.addEventListener('error', (event) => {
            console.error('WebSocket错误:', event);
            // 如果已经轮询失败，则尝试使用轮询
            if (!connected.value && !taskPolling.value) {
                message.warning('实时连接失败，将使用轮询获取任务状态');
                fallbackToPolling();
            }
        });

        // 处理断开连接事件
        wsManager.value.addEventListener('close', (event) => {
            connected.value = false;
            console.log('WebSocket连接已关闭:', event);

            // 在正常关闭的情况下不需要特殊处理
            // 重连逻辑由WebSocket管理器内部处理
        });

        // 处理重连失败事件
        wsManager.value.addEventListener('reconnectFailed', () => {
            message.warning('实时连接失败并达到最大重试次数，将使用轮询获取任务状态');
            fallbackToPolling();
        });

        // 处理重连尝试事件
        wsManager.value.addEventListener('reconnecting', (data) => {
            console.log(`WebSocket重连尝试: ${data.attempt}/${data.maxAttempts}`);
            if (data.attempt >= 3) {
                message.warning(`实时连接尝试恢复中 (${data.attempt}/${data.maxAttempts})...`);
            }
        });

        // 处理超时事件
        wsManager.value.addEventListener('timeout', (data) => {
            console.warn(`WebSocket连接可能已断开: ${data.elapsed}ms未收到消息`);
            message.warning('实时连接似乎已断开，正在尝试恢复...');

            // 超时太久时，主动尝试一次轮询以获取最新状态
            if (!taskPolling.value) {
                getTaskStatusOnce();
            }
        });

        // 处理连接完成事件
        wsManager.value.addEventListener('complete', (data) => {
            try {
                updateTaskInfo(data);
                console.log('任务完成:', data);
                closeWsConnection();
            } catch (e) {
                console.error('处理完成事件失败:', e);
            }
        });

    } catch (error) {
        console.error('创建WebSocket连接失败:', error);
        // 回退到轮询方式
        message.warning('无法建立实时连接，将使用轮询获取任务状态');
        fallbackToPolling();
    }
};

// 添加一个单次获取任务状态的函数，不设置轮询
const getTaskStatusOnce = async () => {
    if (!taskInfo.value.id) return;

    try {
        const res = await getTaskStatus(taskInfo.value.id);

        if (!res.data) {
            console.error('获取任务状态失败');
            return;
        }

        // 更新任务信息
        updateTaskInfo(res.data);
        console.log('单次获取任务状态成功:', res.data);

        // 检查任务是否已结束
        if (res.data.status === 'completed' || res.data.status === 'failed' || res.data.status === 'canceled') {
            taskPolling.value = true; // 停止轮询
            // 更新任务完成状态
            if (res.data.status === 'completed') {
                message.success('STRM文件生成完成');
                stepStatus.value = 'finish';
            } else if (res.data.status === 'failed') {
                message.error(res.data.error || 'STRM文件生成失败');
                stepStatus.value = 'error';
            } else if (res.data.status === 'canceled') {
                message.warning('STRM文件生成已取消');
                stepStatus.value = 'error';
            }
        }
    } catch (error) {
        console.error('单次获取任务状态失败:', error);
    }
};

// 关闭WebSocket连接
const closeWsConnection = () => {
    if (wsManager.value) {
        wsManager.value.disconnect();
        wsManager.value = null;
        connected.value = false;
    }
};

// 回退到轮询模式
const fallbackToPolling = () => {
    taskPolling.value = false;
    pollTaskStatus();
};

// 组件挂载时获取数据
onMounted(() => {
    fetchFiles();

    // 先获取系统设置，然后获取服务器列表并设置默认值
    fetchSystemSettings().then(() => {
        fetchServers();
    });
});

// 组件卸载时清理资源
onBeforeUnmount(() => {
    // 关闭WebSocket连接
    closeWsConnection();
    // 取消任务轮询
    taskPolling.value = true;
});

// 计算是否有成功文件
const hasSuccessFiles = computed(() => taskInfo.value.success_files > 0);

// 判断任务是否完成
const isTaskCompleted = computed(() =>
    ['completed', 'failed', 'canceled'].includes(taskInfo.value.status || '')
);

// 判断任务是否活跃（可以取消）
const isTaskActive = computed(() =>
    ['pending', 'running'].includes(taskInfo.value.status || '')
);

// 下载文件
const downloadFiles = () => {
    if (taskInfo.value.id) {
        const downloadUrl = getStrmDownloadUrlApi(taskInfo.value.id);
        window.open(downloadUrl, '_blank');
    }
};

// 跳转到任务管理页面
const goToHistory = () => {
    router.push('/strm/tasks');
};

// 手动刷新任务状态
const refreshTaskStatus = async () => {
    if (!taskInfo.value.id || refreshing.value) return;

    refreshing.value = true;
    try {
        const res = await getTaskStatus(taskInfo.value.id);
        if (res.data) {
            updateTaskInfo(res.data);
            lastUpdateTime.value = new Date();
            message.success('状态已更新');
        } else {
            message.error('获取任务状态失败');
        }
    } catch (error) {
        console.error('刷新任务状态出错:', error);
        message.error('刷新任务状态出错');
    } finally {
        refreshing.value = false;
    }
};
</script>

<style lang="scss" scoped>
.n-statistic {
    margin-right: 20px;
}

.step-content {
    min-height: 300px;
    padding: 8px 0;
    animation: fadeIn 0.3s ease-in-out;
}

.step-content-area {
    margin-top: 24px;
    padding: 0 8px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// 卡片样式优化
:deep(.n-card) {
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

// 步骤指示器样式优化
:deep(.n-steps) {
    padding: 8px 0;
    margin-bottom: 24px;
}

// 统计卡片样式
:deep(.n-statistic) {
    .n-statistic__value {
        font-size: 24px;
        font-weight: 600;
    }

    .n-statistic__label {
        font-size: 14px;
    }
}

// 进度条样式
:deep(.n-progress) {
    &.n-progress--line {
        margin: 12px 0;
    }
}

// 标签样式
:deep(.n-tag) {
    padding: 4px 8px;
    font-size: 12px;
}

// 响应式布局调整
@media (max-width: 768px) {
    .step-content-area {
        padding: 0;
    }

    :deep(.n-grid) {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

// 按钮组样式
.flex.justify-center.gap-16px,
.flex.justify-between,
.flex.justify-end {
    margin-top: 20px;

    .n-button {
        min-width: 100px;
    }
}
</style>
