<template>
  <div>
    <n-card :bordered="false" class="h-full rounded-8px shadow-sm">
      <div class="flex flex-col space-y-4 mb-4">
        <div class="flex justify-between">
          <h2 class="text-xl font-bold">STRM生成任务管理</h2>
          <n-button type="primary" @click="goToGenerate">新建任务</n-button>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <n-input v-model:value="searchValue" placeholder="搜索任务名称..." clearable style="width: 200px"
            @keyup.enter="handleSearch" />

          <n-date-picker v-model:value="dateRange" type="daterange" clearable style="width: 260px" placeholder="选择时间范围"
            :shortcuts="dateShortcuts" />

          <n-select v-model:value="statusFilter" placeholder="任务状态" clearable style="width: 150px" :options="statusOptions" />

          <n-button type="primary" size="medium" style="width: 80px; height: 34px;" @click="handleSearch">
            搜索
          </n-button>

          <n-button size="medium" style="height: 34px;" @click="clearFilters">
            清除筛选
          </n-button>
        </div>
      </div>

      <n-data-table :columns="columns" :data="tasksData" :loading="loading" :pagination="{
        page: pagination.page,
        pageSize: pagination.pageSize,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        itemCount: pagination.itemCount,
        prefix: ({ itemCount }) => `共 ${itemCount} 条`,
        showQuickJumper: true
      }" remote :row-key="row => row.id" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" />
    </n-card>

    <!-- 任务详情对话框 -->
    <n-modal v-model:show="showTaskDetailModal" preset="card" title="任务详情" :bordered="false" size="huge"
      style="max-width: 960px;">
      <div v-if="currentTask" class="task-detail-container">
        <!-- 基本信息卡片 -->
        <n-card title="基本信息" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
          <n-grid :cols="2" :x-gap="16">
            <n-gi>
              <div class="info-item">
                <span class="label">任务ID：</span>
                <span class="value">{{ currentTask.id }}</span>
              </div>
            </n-gi>
            <n-gi>
              <div class="info-item">
                <span class="label">任务名称：</span>
                <span class="value">{{ currentTask.name }}</span>
              </div>
            </n-gi>
            <n-gi>
              <div class="info-item">
                <span class="label">状态：</span>
                <span class="value">
                  <TaskStatusDisplay :status="currentTask.status" />
                </span>
              </div>
            </n-gi>
            <n-gi>
              <div class="info-item">
                <span class="label">输出目录：</span>
                <span class="value">{{ currentTask.output_dir || '默认输出目录' }}</span>
              </div>
            </n-gi>
          </n-grid>
        </n-card>

        <!-- 处理进度卡片 -->
        <n-card title="处理进度" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
          <div class="progress-container">
            <!-- 大型进度条 -->
            <div class="big-progress mb-4">
              <n-progress
                type="line"
                :percentage="getTaskProgressPercentage(currentTask)"
                :processing="currentTask.status === 'RUNNING'"
                :status="getTaskProgressStatus(currentTask)"
                :show-indicator="false"
                :height="20"
                :border-radius="10"
              />
              <div class="text-center mt-2 text-lg font-medium">
                {{ getTaskProgressPercentage(currentTask) }}%
              </div>
            </div>

            <!-- 详细统计信息 -->
            <n-grid :cols="4" :x-gap="16" class="stat-grid">
              <n-gi>
                <n-statistic label="总文件数">
                  <div class="stat-value">{{ currentTask.total_files || 0 }}</div>
                </n-statistic>
              </n-gi>
              <n-gi>
                <n-statistic label="已处理文件">
                  <div class="stat-value">{{ currentTask.processed_files || 0 }}</div>
                </n-statistic>
              </n-gi>
              <n-gi>
                <n-statistic label="成功文件数" :value="currentTask.success_files || 0">
                  <template #prefix>
                    <n-icon class="text-success">
                      <Icon icon="mdi:check-circle" />
                    </n-icon>
                  </template>
                </n-statistic>
              </n-gi>
              <n-gi>
                <n-statistic label="失败文件数" :value="currentTask.failed_files || 0">
                  <template #prefix>
                    <n-icon class="text-error">
                      <Icon icon="mdi:close-circle" />
                    </n-icon>
                  </template>
                </n-statistic>
              </n-gi>
            </n-grid>

            <n-divider />

            <!-- 时间信息 -->
            <n-grid :cols="3" :x-gap="16" class="time-grid">
              <n-gi>
                <n-statistic label="创建时间">
                  <div class="stat-time">{{ formatDate(currentTask.create_time) }}</div>
                </n-statistic>
              </n-gi>
              <n-gi>
                <n-statistic label="开始时间">
                  <div class="stat-time">{{ currentTask.start_time ? formatDate(currentTask.start_time) : '尚未开始' }}</div>
                </n-statistic>
              </n-gi>
              <n-gi>
                <n-statistic label="完成时间">
                  <div class="stat-time">{{ currentTask.finish_time ? formatDate(currentTask.finish_time) : '尚未完成' }}</div>
                </n-statistic>
              </n-gi>
            </n-grid>
          </div>
        </n-card>

        <!-- 文件列表卡片 -->
        <n-card title="处理文件列表" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
          <template #header-extra>
            <n-space>
              <n-input v-model:value="fileSearchText" placeholder="搜索文件路径..." clearable style="width: 200px" />
              <n-select v-model:value="fileStatusFilter" placeholder="状态过滤" clearable style="width: 120px"
                :options="fileStatusOptions" />
              <n-button v-if="currentTask.status === 'SUCCESS'" size="small" type="primary" @click="handleDownload(currentTask.id)">
                下载STRM文件
              </n-button>
            </n-space>
          </template>
          <n-data-table
            :columns="fileColumns"
            :data="filteredFiles"
            :loading="fileLoading"
            :pagination="{ pageSize: 10 }"
            :row-key="row => row.id || row.source_path"
          />
        </n-card>
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTaskDetailModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, defineComponent, h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { DataTableColumns, PaginationProps, SelectOption } from 'naive-ui';
import {
  NButton,
  NCard,
  NDataTable,
  NDatePicker,
  NDivider,
  NGi,
  NGrid,
  NIcon,
  NInput,
  NModal,
  NPopconfirm,
  NProgress,
  NSelect,
  NSpace,
  NStatistic,
  NTag,
  useMessage
} from 'naive-ui';
import { Icon } from '@iconify/vue';
import {
  getTaskList,
  getTaskStatus,
  cancelTask,
  deleteTask,
  getStrmDownloadUrl
} from '@/service/api/strm';
import { formatDate } from '@/utils/common';

defineOptions({
  name: 'StrmTasks'
});

const message = useMessage();
const router = useRouter();
const loading = ref(false);
const tasksData = ref<any[]>([]);
const fileLoading = ref(false);

// 任务详情相关
const showTaskDetailModal = ref(false);
const currentTask = ref<any>(null);

// 分页配置
const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  showQuickJumper: true
});

// 搜索参数
const searchValue = ref('');
const dateRange = ref<[number, number] | null>(null);
const statusFilter = ref<string | null>(null);

// 状态选项
const statusOptions: SelectOption[] = [
  { label: '等待中', value: 'PENDING' },
  { label: '处理中', value: 'RUNNING' },
  { label: '已完成', value: 'SUCCESS' },
  { label: '已取消', value: 'CANCELED' },
  { label: '失败', value: 'FAILED' }
];

// 日期快捷选项
const dateShortcuts = {
  '今天': () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [today.getTime(), end.getTime()] as [number, number];
  },
  '昨天': () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setDate(end.getDate() - 1);
    end.setHours(23, 59, 59, 999);
    return [yesterday.getTime(), end.getTime()] as [number, number];
  },
  '最近7天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 6);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  },
  '最近30天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 29);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  }
};

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: pagination.page,
      page_size: pagination.pageSize
    };

    // 添加搜索条件
    if (searchValue.value) {
      params.search = searchValue.value;
    }

    // 添加日期范围
    if (dateRange.value) {
      params.start_date = new Date(dateRange.value[0]).toISOString().split('T')[0];
      params.end_date = new Date(dateRange.value[1]).toISOString().split('T')[0];
    }

    // 添加状态过滤
    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    const { data } = await getTaskList(params);
    if (data) {
      tasksData.value = data.tasks || [];
      pagination.itemCount = data.total || 0;
    }
  } catch (error: any) {
    message.error(`获取任务列表失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 获取任务详情
const fetchTaskDetail = async (taskId: number) => {
  fileLoading.value = true;
  try {
    const { data } = await getTaskStatus(taskId);
    if (data) {
      currentTask.value = data;
      showTaskDetailModal.value = true;
    }
  } catch (error: any) {
    message.error(`获取任务详情失败: ${error.message || '未知错误'}`);
  } finally {
    fileLoading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchTasks();
};

// 处理每页数量变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchTasks();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchTasks();
};

// 清除筛选条件
const clearFilters = () => {
  searchValue.value = '';
  dateRange.value = null;
  statusFilter.value = null;
  pagination.page = 1;
  fetchTasks();
};

// 跳转到生成页面
const goToGenerate = () => {
  router.push('/strm/generate');
};

// 为状态显示创建独立组件
const StatusDisplay = defineComponent({
  name: 'StatusDisplay',
  props: {
    status: {
      type: String,
      required: true
    }
  },
  setup(props) {
    return () => {
      // 统一将状态值转为大写，并确保不为空
      const status = (props.status || '').toUpperCase();
      const statusText = getStatusText(status);

      // 根据状态设置不同的图标和样式
      let iconName;
      let customStyle: Record<string, string | number> = {
        padding: '4px 10px',
        fontWeight: 500,
        borderRadius: '12px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '4px'
      };

      switch (status) {
        case 'SUCCESS':
        case 'COMPLETED':
          iconName = 'mdi:check-circle';
          customStyle = {
            ...customStyle,
            backgroundColor: 'rgba(42, 184, 94, 0.15)',
            color: '#2ab85e',
            border: '1px solid #2ab85e'
          };
          break;
        case 'RUNNING':
          iconName = 'mdi:loading';
          customStyle = {
            ...customStyle,
            backgroundColor: 'rgba(250, 173, 20, 0.15)',
            color: '#faad14',
            border: '1px solid #faad14'
          };
          break;
        case 'PENDING':
          iconName = 'mdi:clock-outline';
          customStyle = {
            ...customStyle,
            backgroundColor: 'rgba(24, 144, 255, 0.15)',
            color: '#1890ff',
            border: '1px solid #1890ff'
          };
          break;
        case 'FAILED':
          iconName = 'mdi:close-circle';
          customStyle = {
            ...customStyle,
            backgroundColor: 'rgba(245, 34, 45, 0.15)',
            color: '#f5222d',
            border: '1px solid #f5222d'
          };
          break;
        case 'CANCELED':
          iconName = 'mdi:stop-circle';
          customStyle = {
            ...customStyle,
            backgroundColor: 'rgba(140, 140, 140, 0.15)',
            color: '#8c8c8c',
            border: '1px solid #8c8c8c'
          };
          break;
        default:
          iconName = 'mdi:help-circle';
          customStyle = {
            ...customStyle,
            backgroundColor: 'rgba(140, 140, 140, 0.15)',
            color: '#8c8c8c',
            border: '1px solid #8c8c8c'
          };
      }

      return h('div', { style: customStyle }, [
        h(Icon, {
          icon: iconName,
          style: { fontSize: '16px' },
          class: status === 'RUNNING' ? 'animate-spin' : ''
        }),
        statusText
      ]);
    };
  }
});

// 表格列定义
const columns: DataTableColumns<any> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '任务名称',
    key: 'name',
    width: 300,
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    align: 'center',
    render(row) {
      return h(StatusDisplay, { status: row.status });
    }
  },
  {
    title: '处理进度',
    key: 'progress',
    width: 200,
    align: 'center',
    render(row) {
      const total = row.total_files || 0;
      const processed = row.processed_files || 0;
      const percentage = total ? Math.round((processed / total) * 100) : 0;

      let status: 'default' | 'success' | 'error' | 'warning' | 'info' = 'default';
      if (row.status === 'FAILED') {
        status = 'error';
      } else if (row.status === 'SUCCESS') {
        status = 'success';
      }

      return h('div', { class: 'flex flex-col items-center' }, [
        h(
          NProgress,
          {
            percentage,
            showIndicator: false,
            processing: row.status === 'RUNNING',
            type: 'line',
            status,
            style: 'width: 100%;'
          }
        ),
        h('div', { class: 'flex flex-col items-center mt-1' }, [
          h('span', { class: 'text-xs', style: 'white-space: nowrap;' }, `${percentage}%`),
          h('span', { class: 'text-xs text-gray-500' }, `${processed}/${total} 文件`)
        ])
      ]);
    }
  },
  {
    title: '开始时间',
    key: 'create_time',
    width: 180,
    align: 'center',
    render(row) {
      // 使用start_time作为创建时间的数据源
      const timeValue = row.start_time || row.create_time || row.created_at;
      return formatDate(timeValue);
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    fixed: 'right',
    render(row) {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => fetchTaskDetail(row.id)
            },
            { default: () => '详情' }
          ),
          row.status === 'SUCCESS' ?
            h(
              NButton,
              {
                size: 'small',
                type: 'success',
                onClick: () => handleDownload(row.id)
              },
              { default: () => '下载' }
            ) : null,
          row.status === 'PENDING' || row.status === 'RUNNING' ?
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleCancelTask(row.id)
              },
              {
                default: () => '确定要取消此任务吗？',
                trigger: () => h(
                  NButton,
                  {
                    size: 'small',
                    type: 'warning'
                  },
                  { default: () => '取消' }
                )
              }
            ) : null,
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDeleteTask(row.id)
            },
            {
              default: () => '确定要删除此任务吗？',
              trigger: () => h(
                NButton,
                {
                  size: 'small',
                  type: 'error'
                },
                { default: () => '删除' }
              )
            }
          )
        ]
      });
    }
  }
];

// 文件列表的列定义
const fileColumns: DataTableColumns<any> = [
  {
    title: '源文件路径',
    key: 'source_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '目标文件路径',
    key: 'target_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'is_success',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.is_success ? 'success' : 'error' },
        { default: () => row.is_success ? '成功' : '失败' }
      );
    }
  },
  {
    title: '错误信息',
    key: 'error_message',
    ellipsis: {
      tooltip: true
    },
    align: 'center',
    render(row) {
      return row.error_message || '-';
    }
  }
];

// 文件列表搜索和过滤
const fileSearchText = ref('');
const fileStatusFilter = ref<string | null>(null);
const fileStatusOptions = [
  { label: '成功', value: 'true', type: 'success' as const },
  { label: '失败', value: 'false', type: 'error' as const }
];

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (!currentTask.value?.files) return [];

  let filtered = [...(currentTask.value.files || [])];

  // 应用搜索过滤
  if (fileSearchText.value) {
    const searchText = fileSearchText.value.toLowerCase();
    filtered = filtered.filter(file =>
      (file.source_path || '').toLowerCase().includes(searchText) ||
      (file.target_path || '').toLowerCase().includes(searchText)
    );
  }

  // 应用状态过滤
  if (fileStatusFilter.value !== null) {
    // 转换为布尔值
    const filterValue = fileStatusFilter.value === 'true';
    filtered = filtered.filter(file => file.is_success === filterValue);
  }

  return filtered;
});

// 获取任务进度百分比
const getTaskProgressPercentage = (task: any) => {
  const total = task.total_files || 0;
  const processed = task.processed_files || 0;
  return total ? Math.round((processed / total) * 100) : 0;
};

// 获取任务进度状态
const getTaskProgressStatus = (task: any): 'default' | 'success' | 'error' | 'warning' | 'info' => {
  const status = (task.status || '').toUpperCase();
  if (status === 'FAILED') return 'error';
  if (status === 'SUCCESS' || status === 'COMPLETED') return 'success';
  if (status === 'RUNNING') return 'warning';
  return 'default';
};

// 处理下载STRM文件
const handleDownload = (taskId: number) => {
  const url = getStrmDownloadUrl(taskId);
  window.open(url, '_blank');
};

// 处理取消任务
const handleCancelTask = async (taskId: number) => {
  try {
    await cancelTask(taskId);
    message.success('任务已取消');
    fetchTasks();
  } catch (error: any) {
    message.error(`取消任务失败: ${error.message || '未知错误'}`);
  }
};

// 处理删除任务
const handleDeleteTask = async (taskId: number) => {
  try {
    await deleteTask(taskId);
    message.success('任务已删除');
    fetchTasks();
  } catch (error: any) {
    message.error(`删除任务失败: ${error.message || '未知错误'}`);
  }
};

// 状态文本映射
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'PENDING': '等待中',
    'RUNNING': '处理中',
    'SUCCESS': '已完成',
    'CANCELED': '已取消',
    'FAILED': '失败',
    'COMPLETED': '已完成'
  };
  return map[status] || status;
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks();
});
</script>

<style scoped>
.task-detail-container {
  padding: 0;
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  flex-shrink: 0;
  width: 80px;
}

.info-item .value {
  flex: 1;
}

.big-progress {
  padding: 8px 0;
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
}

.stat-time {
  font-size: 14px;
  color: #606266;
}

.text-success {
  color: #2ab85e;
}

.text-error {
  color: #f5222d;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.text-center {
  text-align: center;
}

.text-lg {
  font-size: 18px;
}

.font-medium {
  font-weight: 500;
}
</style>
