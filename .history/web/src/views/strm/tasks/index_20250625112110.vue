<template>
  <div>
    <n-card :bordered="false" class="h-full rounded-8px shadow-sm">
      <div class="flex flex-col space-y-4 mb-4">
        <div class="flex justify-between">
          <h2 class="text-xl font-bold">STRM生成任务管理</h2>
          <n-button type="primary" @click="goToGenerate">新建任务</n-button>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <n-input v-model:value="searchValue" placeholder="搜索任务名称..." clearable style="width: 200px"
            @keyup.enter="handleSearch" />

          <n-date-picker v-model:value="dateRange" type="daterange" clearable style="width: 260px" placeholder="选择时间范围"
            :shortcuts="dateShortcuts" />

          <n-select v-model:value="statusFilter" placeholder="任务状态" clearable style="width: 150px" :options="statusOptions" />

          <n-button type="primary" size="medium" style="width: 80px; height: 34px;" @click="handleSearch">
            搜索
          </n-button>

          <n-button size="medium" style="height: 34px;" @click="clearFilters">
            清除筛选
          </n-button>
        </div>
      </div>

      <n-data-table :columns="columns" :data="tasksData" :loading="loading" :pagination="{
        page: pagination.page,
        pageSize: pagination.pageSize,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        itemCount: pagination.itemCount,
        prefix: ({ itemCount }) => `共 ${itemCount} 条`,
        showQuickJumper: true
      }" remote :row-key="row => row.id" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" />
    </n-card>

    <!-- 任务详情对话框 -->
    <n-modal v-model:show="showTaskDetailModal" preset="card" title="任务详情" :bordered="false" size="huge"
      style="max-width: 900px;">
      <div v-if="currentTask">
        <n-descriptions :column="2" bordered>
          <n-descriptions-item label="任务ID">
            {{ currentTask.id }}
          </n-descriptions-item>
          <n-descriptions-item label="任务名称">
            {{ currentTask.name }}
          </n-descriptions-item>
          <n-descriptions-item label="状态">
            <n-tag :type="getStatusType(currentTask.status)">{{ getStatusText(currentTask.status) }}</n-tag>
          </n-descriptions-item>
          <n-descriptions-item label="创建时间">
            {{ formatDate(currentTask.create_time) }}
          </n-descriptions-item>
          <n-descriptions-item label="开始时间">
            {{ currentTask.start_time ? formatDate(currentTask.start_time) : '尚未开始' }}
          </n-descriptions-item>
          <n-descriptions-item label="完成时间">
            {{ currentTask.finish_time ? formatDate(currentTask.finish_time) : '尚未完成' }}
          </n-descriptions-item>
          <n-descriptions-item label="总文件数">
            {{ currentTask.total_files || 0 }}
          </n-descriptions-item>
          <n-descriptions-item label="处理进度">
            {{ currentTask.processed_files || 0 }} / {{ currentTask.total_files || 0 }}
            ({{ currentTask.total_files ? Math.round((currentTask.processed_files / currentTask.total_files) * 100) : 0 }}%)
          </n-descriptions-item>
          <n-descriptions-item label="成功文件数">
            {{ currentTask.success_files || 0 }}
          </n-descriptions-item>
          <n-descriptions-item label="失败文件数">
            {{ currentTask.failed_files || 0 }}
          </n-descriptions-item>
          <n-descriptions-item label="输出目录" :span="2">
            {{ currentTask.output_dir || '默认输出目录' }}
          </n-descriptions-item>
        </n-descriptions>

        <div class="mt-4">
          <n-card title="处理文件列表" size="small">
            <template #header-extra>
              <n-space>
                <n-button v-if="currentTask.status === 'SUCCESS'" size="small" type="primary" @click="handleDownload(currentTask.id)">
                  下载STRM文件
                </n-button>
              </n-space>
            </template>
            <n-data-table :columns="fileColumns" :data="currentTask.files || []" :loading="fileLoading"
              :pagination="{ pageSize: 10 }" :row-key="row => row.id || row.source_path" />
          </n-card>
        </div>
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTaskDetailModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { DataTableColumns, PaginationProps, SelectOption } from 'naive-ui';
import {
  NButton,
  NCard,
  NDataTable,
  NDatePicker,
  NDescriptions,
  NDescriptionsItem,
  NInput,
  NModal,
  NPopconfirm,
  NProgress,
  NSelect,
  NSpace,
  NTag,
  useMessage
} from 'naive-ui';
import {
  getTaskList,
  getTaskStatus,
  cancelTask,
  deleteTask,
  getStrmDownloadUrl
} from '@/service/api/strm';
import { formatDate } from '@/utils/common';

defineOptions({
  name: 'StrmTasks'
});

const message = useMessage();
const router = useRouter();
const loading = ref(false);
const tasksData = ref<any[]>([]);
const fileLoading = ref(false);

// 任务详情相关
const showTaskDetailModal = ref(false);
const currentTask = ref<any>(null);

// 分页配置
const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  showQuickJumper: true
});

// 搜索参数
const searchValue = ref('');
const dateRange = ref<[number, number] | null>(null);
const statusFilter = ref<string | null>(null);

// 状态选项
const statusOptions: SelectOption[] = [
  { label: '等待中', value: 'PENDING' },
  { label: '处理中', value: 'RUNNING' },
  { label: '已完成', value: 'SUCCESS' },
  { label: '已取消', value: 'CANCELED' },
  { label: '失败', value: 'FAILED' }
];

// 日期快捷选项
const dateShortcuts = {
  '今天': () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [today.getTime(), end.getTime()] as [number, number];
  },
  '昨天': () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setDate(end.getDate() - 1);
    end.setHours(23, 59, 59, 999);
    return [yesterday.getTime(), end.getTime()] as [number, number];
  },
  '最近7天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 6);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  },
  '最近30天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 29);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  }
};

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: pagination.page,
      page_size: pagination.pageSize
    };

    // 添加搜索条件
    if (searchValue.value) {
      params.search = searchValue.value;
    }

    // 添加日期范围
    if (dateRange.value) {
      params.start_date = new Date(dateRange.value[0]).toISOString().split('T')[0];
      params.end_date = new Date(dateRange.value[1]).toISOString().split('T')[0];
    }

    // 添加状态过滤
    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    const { data } = await getTaskList(params);
    if (data) {
      tasksData.value = data.tasks || [];
      pagination.itemCount = data.total || 0;
    }
  } catch (error: any) {
    message.error(`获取任务列表失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 获取任务详情
const fetchTaskDetail = async (taskId: number) => {
  fileLoading.value = true;
  try {
    const { data } = await getTaskStatus(taskId);
    if (data) {
      currentTask.value = data;
      showTaskDetailModal.value = true;
    }
  } catch (error: any) {
    message.error(`获取任务详情失败: ${error.message || '未知错误'}`);
  } finally {
    fileLoading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchTasks();
};

// 处理每页数量变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchTasks();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchTasks();
};

// 清除筛选条件
const clearFilters = () => {
  searchValue.value = '';
  dateRange.value = null;
  statusFilter.value = null;
  pagination.page = 1;
  fetchTasks();
};

// 跳转到生成页面
const goToGenerate = () => {
  router.push('/strm/generate');
};

// 状态类型映射
const getStatusType = (status: string) => {
  const map: Record<string, 'default' | 'success' | 'warning' | 'error' | 'info'> = {
    'PENDING': 'info',
    'RUNNING': 'warning',
    'SUCCESS': 'success',
    'CANCELED': 'default',
    'FAILED': 'error'
  };
  return map[status] || 'default';
};

// 状态文本映射
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    'PENDING': '等待中',
    'RUNNING': '处理中',
    'SUCCESS': '已完成',
    'CANCELED': '已取消',
    'FAILED': '失败'
  };
  return map[status] || status;
};

// 处理下载STRM文件
const handleDownload = (taskId: number) => {
  const url = getStrmDownloadUrl(taskId);
  window.open(url, '_blank');
};

// 处理取消任务
const handleCancelTask = async (taskId: number) => {
  try {
    await cancelTask(taskId);
    message.success('任务已取消');
    fetchTasks();
  } catch (error: any) {
    message.error(`取消任务失败: ${error.message || '未知错误'}`);
  }
};

// 处理删除任务
const handleDeleteTask = async (taskId: number) => {
  try {
    await deleteTask(taskId);
    message.success('任务已删除');
    fetchTasks();
  } catch (error: any) {
    message.error(`删除任务失败: ${error.message || '未知错误'}`);
  }
};

// 表格列定义
const columns: DataTableColumns<any> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '任务名称',
    key: 'name',
    width: 300,
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: getStatusType(row.status) },
        { default: () => getStatusText(row.status) }
      );
    }
  },
  {
    title: '处理进度',
    key: 'progress',
    width: 200,
    align: 'center',
    render(row) {
      const total = row.total_files || 0;
      const processed = row.processed_files || 0;
      const percentage = total ? Math.round((processed / total) * 100) : 0;

      let status = 'default';
      if (row.status === 'FAILED') {
        status = 'error';
      } else if (row.status === 'SUCCESS') {
        status = 'success';
      }

      return h('div', { class: 'flex flex-col items-center' }, [
        h(
          NProgress,
          {
            percentage,
            showIndicator: true,
            processing: row.status === 'RUNNING',
            type: 'line',
            status,
            style: 'width: 100%;'
          }
        ),
        h('span', { class: 'text-xs text-gray-500 mt-1' }, `${processed}/${total} 文件`)
      ]);
    }
  },
  {
    title: '创建时间',
    key: 'create_time',
    width: 180,
    align: 'center',
    render(row) {
      // 使用start_time作为创建时间的数据源
      const timeValue = row.start_time || row.create_time || row.created_at;
      return formatDate(timeValue);
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    fixed: 'right',
    render(row) {
      return h(NSpace, {}, {
        default: () => [
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              onClick: () => fetchTaskDetail(row.id)
            },
            { default: () => '详情' }
          ),
          row.status === 'SUCCESS' ?
            h(
              NButton,
              {
                size: 'small',
                type: 'success',
                onClick: () => handleDownload(row.id)
              },
              { default: () => '下载' }
            ) : null,
          row.status === 'PENDING' || row.status === 'RUNNING' ?
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleCancelTask(row.id)
              },
              {
                default: () => '确定要取消此任务吗？',
                trigger: () => h(
                  NButton,
                  {
                    size: 'small',
                    type: 'warning'
                  },
                  { default: () => '取消' }
                )
              }
            ) : null,
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDeleteTask(row.id)
            },
            {
              default: () => '确定要删除此任务吗？',
              trigger: () => h(
                NButton,
                {
                  size: 'small',
                  type: 'error'
                },
                { default: () => '删除' }
              )
            }
          )
        ]
      });
    }
  }
];

// 文件列表的列定义
const fileColumns: DataTableColumns<any> = [
  {
    title: '源文件路径',
    key: 'source_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '目标文件路径',
    key: 'target_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'is_success',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.is_success ? 'success' : 'error' },
        { default: () => row.is_success ? '成功' : '失败' }
      );
    }
  },
  {
    title: '错误信息',
    key: 'error_message',
    ellipsis: {
      tooltip: true
    },
    align: 'center',
    render(row) {
      return row.error_message || '-';
    }
  }
];

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks();
});
</script>
