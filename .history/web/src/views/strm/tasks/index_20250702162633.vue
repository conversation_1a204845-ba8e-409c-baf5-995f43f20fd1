<template>
  <div>
    <n-card :bordered="false" class="h-full rounded-8px shadow-sm">
      <div class="flex flex-col space-y-4 mb-4">
        <div class="flex justify-between">
          <h2 class="text-xl font-bold">STRM处理任务管理</h2>
          <n-space>
            <n-button type="primary" @click="goToGenerate">新建STRM任务</n-button>
          </n-space>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <n-input v-model:value="searchValue" placeholder="搜索任务名称..." clearable style="width: 200px"
            @keyup.enter="handleSearch" />

          <n-date-picker v-model:value="dateRange" type="daterange" clearable style="width: 260px" placeholder="选择时间范围"
            :shortcuts="dateShortcuts" />

          <n-select v-model:value="statusFilter" placeholder="任务状态" clearable style="width: 150px"
            :options="statusOptions" />

          <n-button type="primary" size="medium" style="width: 80px; height: 34px;" @click="handleSearch">
            搜索
          </n-button>

          <n-button size="medium" style="height: 34px;" @click="clearFilters">
            清除筛选
          </n-button>
        </div>
      </div>

      <n-data-table :columns="columns" :data="tasksData" :loading="loading" :pagination="{
        page: pagination.page,
        pageSize: pagination.pageSize,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        itemCount: pagination.itemCount,
        prefix: ({ itemCount }) => `共 ${itemCount} 条`,
        showQuickJumper: true
      }" remote :row-key="row => row.id" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" />
    </n-card>

    <!-- 任务详情对话框 -->
    <n-modal v-model:show="showTaskDetailModal" preset="card" title="任务详情" :bordered="false" size="huge"
      style="max-width: 960px; width: 90vw">
      <div v-if="currentTask" class="task-detail-container">
        <div class="task-detail-header mb-4">
          <div class="text-2xl font-bold">{{ currentTask.name }}</div>
          <n-space>
            <n-tag size="large">ID: {{ currentTask.id }}</n-tag>
            <TaskStatusDisplay :status="currentTask.status" :taskType="currentTask.task_type" />
          </n-space>
        </div>

        <n-tabs type="segment" animated>
          <n-tab-pane name="overview" tab="任务概览">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 基本信息卡片 -->
              <n-card title="基本信息" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
                <n-grid :cols="1" :x-gap="16">
                  <n-gi>
                    <div class="info-item">
                      <span class="label">任务类型：</span>
                      <span class="value">STRM处理任务</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">输出目录：</span>
                      <span class="value">{{ currentTask.output_dir || '默认输出目录' }}</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">开始时间：</span>
                      <span class="value">{{ currentTask.start_time ? formatDate(currentTask.start_time) : '尚未开始'
                        }}</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">完成时间：</span>
                      <span class="value">{{ currentTask.end_time ? formatDate(currentTask.end_time) : '尚未完成'
                        }}</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">处理时长：</span>
                      <span class="value">
                        <span :class="{ 'processing-status': isTaskProcessing(currentTask) }">{{
                          getTaskDuration(currentTask) }}</span>
                      </span>
                    </div>
                  </n-gi>
                </n-grid>
              </n-card>

              <!-- 处理进度卡片 -->
              <n-card title="处理进度" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
                <!-- 添加文件总数为0时的警告信息 -->
                <n-alert v-if="currentTask && currentTask.total_files === 0" type="warning" class="mb-4">
                  未找到任何视频文件可处理。请检查上传的文件是否包含视频文件，或检查系统设置中的文件类型配置是否正确。
                </n-alert>

                <div class="progress-container flex items-center justify-center" v-if="currentTask && currentTask.total_files > 0">
                  <n-progress type="circle" :percentage="getTaskProgressPercentage(currentTask)"
                    :processing="currentTask.status === 'RUNNING'" :status="getTaskProgressStatus(currentTask)"
                    :stroke-width="12">
                    <div class="text-center">
                      <div class="percent-text" v-html="getTaskProgressText(currentTask)"></div>
                      <div>{{ currentTask.processed_files || 0 }}/{{ currentTask.total_files || 0 }}</div>
                    </div>
                  </n-progress>
                </div>
                <div class="progress-container flex items-center justify-center" v-else-if="currentTask">
                  <n-empty description="没有需要处理的文件" />
                </div>
              </n-card>
            </div>

            <!-- 详细统计信息 -->
            <n-card title="文件处理统计" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
              <!-- 添加文件总数为0时的警告信息 -->
              <n-alert v-if="currentTask && currentTask.total_files === 0" type="warning" class="mb-4">
                未找到任何视频文件可处理。请检查上传的文件是否包含视频文件，或检查系统设置中的文件类型配置是否正确。
              </n-alert>

              <n-grid :cols="4" :x-gap="16" class="stat-grid">
                <n-gi>
                  <n-statistic label="总文件数">
                    <div class="stat-value">
                      <n-icon size="24" class="mr-1">
                        <Icon icon="mdi:file-multiple" />
                      </n-icon>
                      {{ currentTask.total_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
                <n-gi>
                  <n-statistic label="已处理文件">
                    <div class="stat-value">
                      <n-icon size="24" class="mr-1">
                        <Icon icon="mdi:file-sync" />
                      </n-icon>
                      {{ currentTask.processed_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
                <n-gi>
                  <n-statistic label="成功文件数">
                    <div class="stat-value">
                      <n-icon size="24" class="text-success mr-1">
                        <Icon icon="mdi:check-circle" />
                      </n-icon>
                      {{ currentTask.success_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
                <n-gi>
                  <n-statistic label="失败文件数">
                    <div class="stat-value">
                      <n-icon size="24" class="text-error mr-1">
                        <Icon icon="mdi:close-circle" />
                      </n-icon>
                      {{ currentTask.failed_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
              </n-grid>
            </n-card>

            <!-- 任务操作 -->
            <n-card title="任务操作" v-if="['SUCCESS', 'COMPLETED'].includes(currentTask.status)" class="mb-4" size="small"
              :bordered="false" :segmented="{ content: true }">
              <n-space justify="center">
                <n-button type="primary" @click="handleDownload(currentTask.id)">
                  <template #icon>
                    <n-icon>
                      <Icon icon="mdi:download" />
                    </n-icon>
                  </template>
                  下载文件
                </n-button>
              </n-space>
            </n-card>
          </n-tab-pane>

          <n-tab-pane name="files" tab="文件列表">
            <!-- 文件列表卡片 -->
            <n-card class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
              <template #header>
                <div class="flex justify-between items-center">
                  <span class="text-lg font-medium">处理文件列表</span>
                  <n-space>
                    <n-input v-model:value="fileSearchText" placeholder="搜索文件路径..." clearable style="width: 200px" />
                    <n-select v-model:value="fileStatusFilter" placeholder="状态过滤" clearable style="width: 120px"
                      :options="fileStatusOptions" />
                    <n-radio-group v-model:value="fileViewMode" size="small">
                      <n-radio-button value="table">
                        <n-icon>
                          <Icon icon="material-symbols:table" />
                        </n-icon>
                        表格
                      </n-radio-button>
                      <n-radio-button value="tree">
                        <n-icon>
                          <Icon icon="ph:tree-structure" />
                        </n-icon>
                        树形
                      </n-radio-button>
                    </n-radio-group>
                  </n-space>
                </div>
              </template>

              <!-- 表格视图 -->
              <div v-show="fileViewMode === 'table'">
                <n-data-table :columns="fileColumns" :data="filteredFiles" :loading="fileLoading"
                  :pagination="{ pageSize: 15, showSizePicker: true, pageSizes: [15, 30, 50] }"
                  :row-key="row => row.id || row.source_path" />
              </div>

              <!-- 树形视图 (占位符) -->
              <div v-show="fileViewMode === 'tree'" class="min-h-300px">
                <n-empty v-if="filteredFiles.length === 0" description="没有符合条件的文件" />
                <div v-else class="tree-view-container">
                  <!-- 这里将来可以添加文件树形结构的展示 -->
                  <p class="text-center text-secondary-text">文件树形视图正在开发中...</p>
                </div>
              </div>
            </n-card>
          </n-tab-pane>
        </n-tabs>
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTaskDetailModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 添加任务日志对话框 -->
    <n-modal v-model:show="showTaskLogModal" preset="card" title="任务日志" :bordered="false" size="huge"
      style="max-width: 960px; width: 90vw">
      <div class="task-logs-container">
        <div class="flex flex-wrap items-center gap-2 mb-4">
          <n-input v-model:value="logSearch" placeholder="搜索日志内容..." clearable style="width: 200px"
            @keyup.enter="handleLogSearch" />

          <n-select v-model:value="logLevel" placeholder="日志级别" clearable style="width: 150px"
            :options="logLevelOptions" />

          <n-button type="primary" size="medium" @click="handleLogSearch">
            搜索
          </n-button>

          <n-button size="medium" @click="clearLogFilters">
            清除筛选
          </n-button>

          <div class="ml-auto">
            <n-button type="success" @click="exportLogs">
              <template #icon>
                <n-icon>
                  <Icon icon="mdi:file-export" />
                </n-icon>
              </template>
              导出日志
            </n-button>
          </div>
        </div>

        <!-- 替换表格为控制台日志查看器 -->
        <ConsoleLogViewer :logs="parsedLogLines" :height="500" :searchTerm="logSearch" :levelFilter="logLevel" />
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTaskLogModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 资源下载任务创建对话框 -->
    <n-modal v-model:show="showResourceDownloadModal" preset="card" title="创建资源下载任务"
      style="max-width: 800px; width: 90vw">
      <div v-if="showResourceDownloadModal" class="resource-download-form">
        <n-form ref="resourceFormRef" :model="resourceDownloadForm" :rules="resourceDownloadRules" label-placement="left"
          label-width="100">
          <n-form-item label="记录ID" path="record_id">
            <n-input-number v-model:value="resourceDownloadForm.record_id" placeholder="请输入解析记录ID" clearable />
          </n-form-item>

          <n-form-item label="任务名称" path="name">
            <n-input v-model:value="resourceDownloadForm.name" placeholder="请输入任务名称（可选）" clearable />
          </n-form-item>

          <n-form-item label="输出目录" path="output_dir">
            <n-input v-model:value="resourceDownloadForm.output_dir" placeholder="请输入输出目录（可选）" clearable />
          </n-form-item>

          <n-form-item label="文件路径" path="file_paths">
            <n-input v-model:value="filePathsText" placeholder="每行输入一个文件路径" type="textarea"
              :rows="5" clearable />
          </n-form-item>
        </n-form>

        <div class="flex justify-end mt-4 space-x-2">
          <n-button @click="showResourceDownloadModal = false">取消</n-button>
          <n-button type="primary" :loading="creating" @click="createResourceDownload">创建任务</n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import type { DataTableColumns, PaginationProps, SelectOption } from 'naive-ui';
import {
  NButton,
  NCard,
  NDataTable,
  NDatePicker,
  NEmpty,
  NGi,
  NGrid,
  NIcon,
  NInput,
  NModal,
  NPopconfirm,
  NProgress,
  NRadioButton,
  NRadioGroup,
  NSelect,
  NSpace,
  NStatistic,
  NTabPane,
  NTabs,
  NTag,
  NAlert,
  useMessage
} from 'naive-ui';
import { Icon } from '@iconify/vue';
import dayjs from 'dayjs';
import {
  getTaskList,
  getTaskStatus,
  getStrmDownloadUrl,
  cancelTask,
  deleteTask,
  getTaskLogs
} from '@/service/api/strm';
import { formatDate } from '@/utils/common';
import TaskStatusDisplay from '@/components/custom/task-status-display.vue';
import ConsoleLogViewer from '@/components/custom/console-log-viewer.vue';
import FileTreeView from '@/components/custom/file-tree-view.vue';

defineOptions({
  name: 'StrmTasks'
});

const message = useMessage();
const router = useRouter();
const loading = ref(false);
const tasksData = ref<any[]>([]);
const fileLoading = ref(false);

// 任务详情相关
const showTaskDetailModal = ref(false);
const currentTask = ref<any>(null);

// 分页配置
const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  showQuickJumper: true
});

// 搜索参数
const searchValue = ref('');
const dateRange = ref<[number, number] | null>(null);
const statusFilter = ref<string | null>(null);

// 状态选项
const statusOptions: SelectOption[] = [
  { label: '等待中', value: 'PENDING' },
  { label: '处理中', value: 'RUNNING' },
  { label: '已完成', value: 'SUCCESS' },
  { label: '已取消', value: 'CANCELED' },
  { label: '失败', value: 'FAILED' }
];

// 日期快捷选项
const dateShortcuts = {
  '今天': () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [today.getTime(), end.getTime()] as [number, number];
  },
  '昨天': () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setDate(end.getDate() - 1);
    end.setHours(23, 59, 59, 999);
    return [yesterday.getTime(), end.getTime()] as [number, number];
  },
  '最近7天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 6);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  },
  '最近30天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 29);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  }
};

// 任务类型筛选已移除，所有任务统一为STRM处理任务
const taskTypeFilter = ref<string | null>(null);

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: pagination.page,
      page_size: pagination.pageSize
    };

    // 添加搜索条件
    if (searchValue.value) {
      params.search = searchValue.value;
    }

    // 添加状态过滤
    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    // 添加任务类型过滤
    if (taskTypeFilter.value) {
      params.task_type = taskTypeFilter.value;
    }

    // 添加日期范围过滤
    if (dateRange.value && Array.isArray(dateRange.value)) {
      const [start, end] = dateRange.value;
      if (start && end) {
        params.start_date = dayjs(start).format('YYYY-MM-DD');
        params.end_date = dayjs(end).format('YYYY-MM-DD 23:59:59');
      }
    }

    const response = await getTaskList(params);

    // 检查响应格式并提取数据
    if (response) {
      let tasksArray = [];
      let totalCount = 0;

      // 检查是否有嵌套的data对象（新格式）
      if (response.data) {
        // 如果data下有tasks数组
        if (response.data.tasks && Array.isArray(response.data.tasks)) {
          tasksArray = response.data.tasks;
          totalCount = response.data.total || 0;
        }
        // 如果data本身就是任务数组
        else if (Array.isArray(response.data)) {
          tasksArray = response.data;
          totalCount = response.total || 0;
        }
      }
      // 如果直接包含tasks数组（旧格式）
      else if (response.tasks && Array.isArray(response.tasks)) {
        tasksArray = response.tasks;
        totalCount = response.total || 0;
      }
      // 如果response本身就是任务数组
      else if (Array.isArray(response)) {
        tasksArray = response;
        totalCount = tasksArray.length;
      }

      if (tasksArray.length > 0) {
        // 标准化任务数据
        tasksData.value = tasksArray.map((task: any) => ({
          ...task,
          // 如果后端没有提供task_type字段，默认为'strm'类型
          task_type: task.task_type || 'strm',
          // 确保状态字段统一为大写
          status: task.status ? String(task.status).toUpperCase() : 'UNKNOWN'
        }));

        pagination.itemCount = totalCount;
      } else {
        tasksData.value = [];
        pagination.itemCount = 0;
      }
    } else {
      console.error('获取任务列表失败：响应为空');
      message.error('获取任务列表失败：响应为空');
      tasksData.value = [];
      pagination.itemCount = 0;
    }
  } catch (error: any) {
    console.error('获取任务列表失败', error);
    if (error.response?.data) {
      console.error('响应数据:', error.response.data);
    }
    message.error(`获取任务列表失败: ${error.message || '未知错误'}`);
    tasksData.value = [];
    pagination.itemCount = 0;
  } finally {
    loading.value = false;
  }
};

// 获取任务详情
const fetchTaskDetail = async (taskId: number) => {
  try {
    loading.value = true;
    const response = await getTaskStatus(taskId);

    // 检查并提取任务详情
    if (response) {
      // 检查是否有嵌套的data对象
      if (response.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
        currentTask.value = response.data;
      } else {
        currentTask.value = response;
      }

      // 设置默认任务类型
      if (!currentTask.value.task_type) {
        currentTask.value.task_type = 'strm';
      }

      // 将状态标准化为大写
      if (currentTask.value.status && typeof currentTask.value.status === 'string') {
        currentTask.value.status = currentTask.value.status.toUpperCase();
      }

      // 对不同类型的任务可能需要做不同的处理
      if (currentTask.value.task_type === 'resource_download') {
        // 特殊处理资源下载任务的详情
        // ...
      }

      showTaskDetailModal.value = true;

      // 如果任务对象中已包含files字段，无需再次请求
      if (currentTask.value.files && Array.isArray(currentTask.value.files) && currentTask.value.files.length > 0) {
        console.log('任务详情中已包含文件列表，跳过单独请求');
      } else {
        // 否则请求文件列表
        await fetchTaskFiles(taskId);
      }
    } else {
      console.error('获取任务详情失败：响应为空');
      message.error('获取任务详情失败：响应为空');
    }
  } catch (error: any) {
    console.error('获取任务详情失败', error);
    if (error.response?.data) {
      console.error('响应数据:', error.response.data);
    }
    message.error(`获取任务详情失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 获取任务文件列表
const fetchTaskFiles = async (taskId: number) => {
  fileLoading.value = true;
  try {
    const response = await getTaskStatus(taskId);

    if (!currentTask.value) {
      currentTask.value = { files: [] } as any;
    }

    // 检查文件列表的位置
    if (response) {
      if (response.data && response.data.files && Array.isArray(response.data.files)) {
        // 从嵌套data对象中获取
        currentTask.value.files = response.data.files;
      } else if (response.files && Array.isArray(response.files)) {
        // 从响应根对象获取
        currentTask.value.files = response.files;
      } else {
        // 没有找到文件列表
        currentTask.value.files = [];
        console.warn('任务无文件列表或文件列表为空');
      }
    }
  } catch (error: any) {
    console.error('获取任务文件列表失败', error);
    if (error.response?.data) {
      console.error('响应数据:', error.response.data);
    }
  } finally {
    fileLoading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchTasks();
};

// 处理每页数量变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchTasks();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchTasks();
};

// 清除筛选条件
const clearFilters = () => {
  searchValue.value = '';
  dateRange.value = null;
  statusFilter.value = null;
  taskTypeFilter.value = null;
  handleSearch();
};

// 跳转到生成页面
const goToGenerate = () => {
  router.push('/strm/generate');
};

// 处理下载STRM文件
const handleDownload = (taskId: number) => {
  const url = getStrmDownloadUrl(taskId);
  window.open(url, '_blank');
};

// 处理取消任务
const handleCancelTask = async (taskId: number) => {
  try {
    await cancelTask(taskId);
    message.success('任务已取消');
    fetchTasks();
  } catch (error: any) {
    message.error(`取消任务失败: ${error.message || '未知错误'}`);
  }
};

// 处理删除任务
const handleDeleteTask = async (taskId: number) => {
  try {
    await deleteTask(taskId);
    message.success('任务已删除');
    fetchTasks();
  } catch (error: any) {
    message.error(`删除任务失败: ${error.message || '未知错误'}`);
  }
};

// 获取任务进度百分比
const getTaskProgressPercentage = (task: any) => {
  if (!task) return 0;

  // 根据任务类型选择不同的计算方式
  if (task.task_type === 'resource_download') {
    // 资源下载任务可能有特殊的进度计算
    return task.progress !== undefined ? Math.round(task.progress) : 0;
  }

  // STRM生成任务
  const total = task.total_files || 0;
  const processed = task.processed_files || 0;
  return total > 0 ? Math.round((processed / total) * 100) : 0;
};

// 获取任务进度百分比文本（带%符号）
const getTaskProgressText = (task: any) => {
  // 使用不换行空格(\u00A0)连接数字和百分比符号
  return `${getTaskProgressPercentage(task)}\u00A0%`;
};

// 获取任务进度状态
const getTaskProgressStatus = (task: any): 'default' | 'success' | 'error' | 'warning' | 'info' => {
  const status = (task.status || '').toString().toUpperCase();

  // 处理不同的状态格式
  if (status === 'FAILED' || status === 'ERROR') return 'error';
  if (status === 'SUCCESS' || status === 'COMPLETED') return 'success';
  if (status === 'RUNNING' || status === 'PROCESSING' || status === 'IN_PROGRESS') return 'warning';
  if (status === 'CANCELED' || status === 'CANCELLED') return 'info';
  return 'default';
};

// 文件列表搜索和过滤
const fileSearchText = ref('');
const fileStatusFilter = ref<string | null>(null);
const fileStatusOptions = [
  { label: '成功', value: 'true', type: 'success' as const },
  { label: '失败', value: 'false', type: 'error' as const }
];

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (!currentTask.value?.files) return [];

  let filtered = [...(currentTask.value.files || [])];

  // 应用搜索过滤
  if (fileSearchText.value) {
    const searchText = fileSearchText.value.toLowerCase();
    filtered = filtered.filter(file =>
      (file.source_path || '').toLowerCase().includes(searchText) ||
      (file.target_path || '').toLowerCase().includes(searchText)
    );
  }

  // 应用状态过滤
  if (fileStatusFilter.value !== null) {
    // 转换为布尔值
    const filterValue = fileStatusFilter.value === 'true';
    filtered = filtered.filter(file => file.is_success === filterValue);
  }

  return filtered;
});

// 表格列定义
const columns: DataTableColumns<any> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '任务名称',
    key: 'name',
    width: 300,
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '任务类型',
    key: 'task_type',
    width: 120,
    align: 'center',
    render(row) {
      return h('span', {}, 'STRM处理');
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    align: 'center',
    render(row) {
      return h(TaskStatusDisplay, {
        status: row.status,
        task: row,
        taskType: row.task_type || 'strm'
      });
    }
  },
  {
    title: '处理进度',
    key: 'progress',
    width: 200,
    align: 'center',
    render(row) {
      const total = row.total_files || 0;
      const processed = row.processed_files || 0;
      const percentage = total ? Math.round((processed / total) * 100) : 0;

      let status: 'default' | 'success' | 'error' | 'warning' | 'info' = 'default';
      if (row.status === 'FAILED') {
        status = 'error';
      } else if (row.status === 'SUCCESS' || row.status === 'COMPLETED') {
        // 检查是否有失败的文件
        const failedFiles = row.failed_files || 0;
        const resourceFailed = row.resource_failed || 0;

        if (failedFiles > 0 || resourceFailed > 0) {
          status = 'warning'; // 部分成功
        } else {
          status = 'success'; // 完全成功
        }
      }

      return h('div', { class: 'flex flex-col items-center' }, [
        h(
          NProgress,
          {
            percentage,
            showIndicator: false,
            processing: row.status === 'RUNNING',
            type: 'line',
            status,
            style: 'width: 100%;'
          }
        ),
        h('div', { class: 'flex flex-col items-center mt-1' }, [
          h('span', { class: 'text-xs', style: 'white-space: nowrap;' }, `${percentage}%`),
          h('span', { class: 'text-xs text-gray-500' }, `${processed}/${total} 文件`)
        ])
      ]);
    }
  },
  {
    title: '开始时间',
    key: 'create_time',
    width: 180,
    align: 'center',
    render(row) {
      // 使用start_time作为创建时间的数据源
      const timeValue = row.start_time || row.create_time || row.created_at;
      return formatDate(timeValue);
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 190,
    align: 'center',
    fixed: 'right',
    render(row: any) {
      return h(
        NSpace,
        { justify: "center" },
        {
          default: () => [
            // 查看按钮
            h(
              NButton,
              {
                size: 'small',
                onClick: () => fetchTaskDetail(row.id),
              },
              { default: () => '详情' }
            ),
            // 日志按钮
            h(
              NButton,
              {
                size: 'small',
                onClick: () => openLogViewer(row.id),
                type: 'info',
              },
              { default: () => '日志' }
            ),
            row.status === 'SUCCESS' ?
              h(
                NButton,
                {
                  size: 'small',
                  type: 'success',
                  onClick: () => handleDownload(row.id)
                },
                { default: () => '下载' }
              ) : null,
            row.status === 'PENDING' || row.status === 'RUNNING' ?
              h(
                NPopconfirm,
                {
                  onPositiveClick: () => handleCancelTask(row.id)
                },
                {
                  default: () => '确定要取消此任务吗？',
                  trigger: () => h(
                    NButton,
                    {
                      size: 'small',
                      type: 'warning'
                    },
                    { default: () => '取消' }
                  )
                }
              ) : null,
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDeleteTask(row.id)
              },
              {
                default: () => '确定要删除此任务吗？',
                trigger: () => h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error'
                  },
                  { default: () => '删除' }
                )
              }
            )
          ]
        }
      );
    }
  }
];

// 文件列表的列定义
const fileColumns: DataTableColumns<any> = [
  {
    title: '源文件路径',
    key: 'source_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '目标文件路径',
    key: 'target_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'is_success',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.is_success ? 'success' : 'error' },
        { default: () => row.is_success ? '成功' : '失败' }
      );
    }
  },
  {
    title: '错误信息',
    key: 'error_message',
    ellipsis: {
      tooltip: true
    },
    align: 'center',
    render(row) {
      return row.error_message || '-';
    }
  }
];

// 文件视图模式
const fileViewMode = ref<'table' | 'tree'>('table');

// 日志查看相关
const showTaskLogModal = ref(false);
const currentTaskId = ref<number | null>(null);
const logLoading = ref(false);
const logData = ref<any[]>([]);
const logSearch = ref('');
const logLevel = ref<string | null>(null);
const logLevelOptions = [
  { label: '信息', value: 'INFO', type: 'info' as const },
  { label: '错误', value: 'ERROR', type: 'error' as const },
  { label: '警告', value: 'WARNING', type: 'warning' as const },
  { label: '调试', value: 'DEBUG', type: 'default' as const }
];

// 解析后的日志行数据 - 用于控制台显示
const parsedLogLines = computed(() => {
  // 如果没有数据，返回空数组
  if (!logData.value || logData.value.length === 0) {
    return [];
  }

  // 将日志记录转换为纯文本行格式
  return logData.value.map(line => {
    // 如果已经是字符串，直接返回
    if (typeof line === 'string') {
      return line;
    }

    // 兼容处理：如果是对象格式，则格式化（保留向后兼容性）
    const timestamp = line.timestamp ? formatDate(line.timestamp, 'YYYY-MM-DD HH:mm:ss') : '';
    const level = line.level || 'INFO';
    const message = line.message || '';

    // 构建日志行
    let logLine = `[${timestamp}] [${level}] ${message}`;

    // 添加文件路径信息（如果有）
    if (line.file_path) {
      logLine += ` | 文件: ${line.file_path}`;
    }

    // 添加目标路径信息（如果有）
    if (line.target_path) {
      logLine += ` | 目标: ${line.target_path}`;
    }

    // 添加错误信息（如果有）
    if (line.error_message) {
      logLine += ` | 错误: ${line.error_message}`;
    }

    // 添加状态信息（如果有）
    if (typeof line.is_success === 'boolean') {
      logLine += ` | 状态: ${line.is_success ? '成功' : '失败'}`;
    }

    // 返回日志行
    return logLine;
  });
});

// 获取任务日志
const fetchTaskLogs = async () => {
  if (!currentTaskId.value) return;

  logLoading.value = true;
  try {
    const params: Record<string, any> = {};

    // 添加搜索和日志级别过滤
    if (logSearch.value) {
      params.search = logSearch.value;
    }

    if (logLevel.value) {
      params.level = logLevel.value;
    }

    const { data } = await getTaskLogs(currentTaskId.value, params);
    if (data) {
      // 处理日志原始内容
      if (data.raw_content) {
        // 从raw_content分割日志行
        const logLines = data.raw_content.split('\n')
          .filter((line: string) => line.trim() !== '')
          .map((line: string) => line);

        logData.value = logLines;
      } else {
        logData.value = [];
      }
    }
  } catch (error: any) {
    message.error(`获取任务日志失败: ${error.message || '未知错误'}`);
  } finally {
    logLoading.value = false;
  }
};

// 处理日志搜索
const handleLogSearch = () => {
  fetchTaskLogs();
};

// 清除日志筛选条件
const clearLogFilters = () => {
  logSearch.value = '';
  logLevel.value = null;
  fetchTaskLogs();
};

// 打开日志查看器
const openLogViewer = (taskId: number) => {
  currentTaskId.value = taskId;
  showLogViewerModal.value = true;
  fetchTaskLogs(taskId);
};

// 根据日志级别获取标签类型
const getLogLevelType = (level: string): 'success' | 'error' | 'warning' | 'info' | 'default' => {
  switch (level?.toUpperCase()) {
    case 'INFO':
      return 'info';
    case 'ERROR':
      return 'error';
    case 'WARNING':
      return 'warning';
    case 'DEBUG':
      return 'default';
    default:
      return 'default';
  }
};

// 导出日志
const exportLogs = () => {
  if (!logData.value || logData.value.length === 0) {
    message.warning('暂无日志可导出');
    return;
  }

  const taskName = currentTask.value?.name || `任务${currentTaskId.value}`;
  const fileName = `${taskName}_日志_${formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.txt`;

  // 将日志数据转换为文本
  const logText = parsedLogLines.value.join('\n');

  // 创建Blob对象
  const blob = new Blob([logText], { type: 'text/plain;charset=utf-8' });

  // 创建下载链接
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;

  // 点击下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  message.success('日志导出成功');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks();
});

// 添加计算处理时长的辅助函数
const formatDuration = (seconds: number) => {
  if (seconds < 0) return '-'; // 处理异常情况

  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (days > 0) {
    return `${days}天${hours}小时`;
  }

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }

  if (minutes > 0) {
    return `${minutes}分钟${remainingSeconds}秒`;
  }

  // 当时间差小于1秒但大于0时，显示"不到1秒"而不是"0秒"
  if (seconds > 0 && remainingSeconds === 0) {
    return "不到1秒";
  }

  return `${remainingSeconds}秒`;
};

const isTaskProcessing = (task: any) => {
  return task.status === 'RUNNING';
};

const getTaskDuration = (task: any) => {
  if (!task.start_time) return '尚未开始';

  const start = new Date(task.start_time).getTime();

  // 如果任务已完成，计算开始到结束的时长
  if (task.end_time) {
    const end = new Date(task.end_time).getTime();
    const duration = (end - start) / 1000; // 转换为秒
    return formatDuration(duration);
  }

  // 如果任务正在进行中，计算开始到现在的时长，并标记为进行中
  if (isTaskProcessing(task)) {
    const now = new Date().getTime();
    const duration = (now - start) / 1000; // 转换为秒
    return `${formatDuration(duration)} (进行中)`;
  }

  return '尚未完成';
};

// 资源下载任务创建表单
const showResourceDownloadModal = ref(false);
const resourceFormRef = ref<any>(null);
const resourceDownloadForm = ref({
  record_id: null as null | number,
  name: '',
  output_dir: '',
  file_paths: [] as string[]
});
const filePathsText = ref('');

// 监听文件路径文本变化，转换为数组
watch(filePathsText, (newVal) => {
  const paths = newVal.split('\n').filter(line => line.trim() !== '');
  resourceDownloadForm.value.file_paths = paths;
});

// 表单验证规则
const resourceDownloadRules = {
  record_id: [
    { required: true, message: '请输入解析记录ID', trigger: 'blur' }
  ],
  file_paths: [
    {
      validator: (_: any, value: string[]) => value.length > 0,
      message: '请至少输入一个文件路径',
      trigger: 'blur'
    }
  ]
};

// 创建资源下载任务
const creating = ref(false);
const createResourceDownload = () => {
  if (!resourceFormRef.value) return;

  resourceFormRef.value.validate(async (errors: any) => {
    if (errors) return;

    creating.value = true;
    try {
      await createResourceDownloadTask({
        record_id: resourceDownloadForm.value.record_id!,
        file_paths: resourceDownloadForm.value.file_paths,
        name: resourceDownloadForm.value.name || undefined,
        output_dir: resourceDownloadForm.value.output_dir || undefined
      });

      message.success('资源下载任务创建成功');
      showResourceDownloadModal.value = false;
      fetchTasks(); // 刷新任务列表

      // 重置表单
      resourceDownloadForm.value = {
        record_id: null,
        name: '',
        output_dir: '',
        file_paths: []
      };
      filePathsText.value = '';
    } catch (error) {
      console.error('创建资源下载任务失败', error);
      message.error('创建资源下载任务失败');
    } finally {
      creating.value = false;
    }
  });
};
</script>

<style scoped>
.task-detail-container {
  padding: 0;
}

.task-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(239, 239, 245, 0.7);
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  flex-shrink: 0;
  width: 80px;
}

.info-item .value {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.stat-time {
  font-size: 14px;
  color: #606266;
}

.text-success {
  color: #2ab85e;
}

.text-error {
  color: #f5222d;
}

.text-secondary-text {
  color: #8c8c8c;
}

.processing-status {
  color: #faad14;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.mr-1 {
  margin-right: 4px;
}

.text-center {
  text-align: center;
}

.text-lg {
  font-size: 18px;
}

.text-2xl {
  font-size: 24px;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.percent-text {
  font-size: 24px;
  font-weight: 700;
  white-space: nowrap;
  line-height: 1.2;
}

.no-wrap {
  white-space: nowrap;
}

.min-h-300px {
  min-height: 300px;
}

.tree-view-container {
  padding: 16px 0;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 16px;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
