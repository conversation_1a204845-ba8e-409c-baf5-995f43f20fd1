<template>
  <div>
    <n-card :bordered="false" class="h-full rounded-8px shadow-sm">
      <div class="flex flex-col space-y-4 mb-4">
        <div class="flex justify-between">
          <h2 class="text-xl font-bold">STRM生成任务管理</h2>
          <n-button type="primary" @click="goToGenerate">新建任务</n-button>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <n-input v-model:value="searchValue" placeholder="搜索任务名称..." clearable style="width: 200px"
            @keyup.enter="handleSearch" />

          <n-date-picker v-model:value="dateRange" type="daterange" clearable style="width: 260px" placeholder="选择时间范围"
            :shortcuts="dateShortcuts" />

          <n-select v-model:value="statusFilter" placeholder="任务状态" clearable style="width: 150px"
            :options="statusOptions" />

          <n-button type="primary" size="medium" style="width: 80px; height: 34px;" @click="handleSearch">
            搜索
          </n-button>

          <n-button size="medium" style="height: 34px;" @click="clearFilters">
            清除筛选
          </n-button>
        </div>
      </div>

      <n-data-table :columns="columns" :data="tasksData" :loading="loading" :pagination="{
        page: pagination.page,
        pageSize: pagination.pageSize,
        showSizePicker: true,
        pageSizes: [10, 20, 50, 100],
        itemCount: pagination.itemCount,
        prefix: ({ itemCount }) => `共 ${itemCount} 条`,
        showQuickJumper: true
      }" remote :row-key="row => row.id" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" />
    </n-card>

    <!-- 任务详情对话框 -->
    <n-modal v-model:show="showTaskDetailModal" preset="card" title="任务详情" :bordered="false" size="huge"
      style="max-width: 960px; width: 90vw">
      <div v-if="currentTask" class="task-detail-container">
        <div class="task-detail-header mb-4">
          <div class="text-2xl font-bold">{{ currentTask.name }}</div>
          <n-space>
            <n-tag size="large">ID: {{ currentTask.id }}</n-tag>
            <TaskStatusDisplay :status="currentTask.status" />
          </n-space>
        </div>

        <n-tabs type="segment" animated>
          <n-tab-pane name="overview" tab="任务概览">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- 基本信息卡片 -->
              <n-card title="基本信息" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
                <n-grid :cols="1" :x-gap="16">
                  <n-gi>
                    <div class="info-item">
                      <span class="label">输出目录：</span>
                      <span class="value">{{ currentTask.output_dir || '默认输出目录' }}</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">开始时间：</span>
                      <span class="value">{{ currentTask.start_time ? formatDate(currentTask.start_time) : '尚未开始'
                      }}</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">完成时间：</span>
                      <span class="value">{{ currentTask.end_time ? formatDate(currentTask.end_time) : '尚未完成'
                      }}</span>
                    </div>
                  </n-gi>
                  <n-gi>
                    <div class="info-item">
                      <span class="label">处理时长：</span>
                      <span class="value">
                        <span :class="{ 'processing-status': isTaskProcessing(currentTask) }">{{
                          getTaskDuration(currentTask) }}</span>
                      </span>
                    </div>
                  </n-gi>
                </n-grid>
              </n-card>

              <!-- 处理进度卡片 -->
              <n-card title="处理进度" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
                <div class="progress-container flex items-center justify-center">
                  <n-progress type="circle" :percentage="getTaskProgressPercentage(currentTask)"
                    :processing="currentTask.status === 'RUNNING'" :status="getTaskProgressStatus(currentTask)"
                    :stroke-width="12">
                    <div class="text-center">
                      <div class="percent-text" v-html="getTaskProgressText(currentTask)"></div>
                      <div>{{ currentTask.processed_files || 0 }}/{{ currentTask.total_files || 0 }}</div>
                    </div>
                  </n-progress>
                </div>
              </n-card>
            </div>

            <!-- 详细统计信息 -->
            <n-card title="文件处理统计" class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
              <n-grid :cols="4" :x-gap="16" class="stat-grid">
                <n-gi>
                  <n-statistic label="总文件数">
                    <div class="stat-value">
                      <n-icon size="24" class="mr-1">
                        <Icon icon="mdi:file-multiple" />
                      </n-icon>
                      {{ currentTask.total_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
                <n-gi>
                  <n-statistic label="已处理文件">
                    <div class="stat-value">
                      <n-icon size="24" class="mr-1">
                        <Icon icon="mdi:file-sync" />
                      </n-icon>
                      {{ currentTask.processed_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
                <n-gi>
                  <n-statistic label="成功文件数">
                    <div class="stat-value">
                      <n-icon size="24" class="text-success mr-1">
                        <Icon icon="mdi:check-circle" />
                      </n-icon>
                      {{ currentTask.success_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
                <n-gi>
                  <n-statistic label="失败文件数">
                    <div class="stat-value">
                      <n-icon size="24" class="text-error mr-1">
                        <Icon icon="mdi:close-circle" />
                      </n-icon>
                      {{ currentTask.failed_files || 0 }}
                    </div>
                  </n-statistic>
                </n-gi>
              </n-grid>
            </n-card>

            <!-- 任务操作 -->
            <n-card title="任务操作" v-if="['SUCCESS', 'COMPLETED'].includes(currentTask.status)" class="mb-4" size="small"
              :bordered="false" :segmented="{ content: true }">
              <n-space justify="center">
                <n-button type="primary" @click="handleDownload(currentTask.id)">
                  <template #icon>
                    <n-icon>
                      <Icon icon="mdi:download" />
                    </n-icon>
                  </template>
                  下载STRM文件
                </n-button>
              </n-space>
            </n-card>
          </n-tab-pane>

          <n-tab-pane name="files" tab="文件列表">
            <!-- 文件列表卡片 -->
            <n-card class="mb-4" size="small" :bordered="false" :segmented="{ content: true }">
              <template #header>
                <div class="flex justify-between items-center">
                  <span class="text-lg font-medium">处理文件列表</span>
                  <n-space>
                    <n-input v-model:value="fileSearchText" placeholder="搜索文件路径..." clearable style="width: 200px" />
                    <n-select v-model:value="fileStatusFilter" placeholder="状态过滤" clearable style="width: 120px"
                      :options="fileStatusOptions" />
                    <n-radio-group v-model:value="fileViewMode" size="small">
                      <n-radio-button value="table">
                        <n-icon>
                          <Icon icon="material-symbols:table" />
                        </n-icon>
                        表格
                      </n-radio-button>
                      <n-radio-button value="tree">
                        <n-icon>
                          <Icon icon="ph:tree-structure" />
                        </n-icon>
                        树形
                      </n-radio-button>
                    </n-radio-group>
                  </n-space>
                </div>
              </template>

              <!-- 表格视图 -->
              <div v-show="fileViewMode === 'table'">
                <n-data-table :columns="fileColumns" :data="filteredFiles" :loading="fileLoading"
                  :pagination="{ pageSize: 15, showSizePicker: true, pageSizes: [15, 30, 50] }"
                  :row-key="row => row.id || row.source_path" />
              </div>

              <!-- 树形视图 (占位符) -->
              <div v-show="fileViewMode === 'tree'" class="min-h-300px">
                <n-empty v-if="filteredFiles.length === 0" description="没有符合条件的文件" />
                <div v-else class="tree-view-container">
                  <!-- 这里将来可以添加文件树形结构的展示 -->
                  <p class="text-center text-secondary-text">文件树形视图正在开发中...</p>
                </div>
              </div>
            </n-card>
          </n-tab-pane>
        </n-tabs>
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTaskDetailModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 添加任务日志对话框 -->
    <n-modal v-model:show="showTaskLogModal" preset="card" title="任务日志" :bordered="false" size="huge"
      style="max-width: 960px; width: 90vw">
      <div class="task-logs-container">
        <div class="flex flex-wrap items-center gap-2 mb-4">
          <n-input v-model:value="logSearch" placeholder="搜索日志内容..." clearable style="width: 200px"
            @keyup.enter="handleLogSearch" />

          <n-select v-model:value="logLevel" placeholder="日志级别" clearable style="width: 150px"
            :options="logLevelOptions" />

          <n-button type="primary" size="medium" @click="handleLogSearch">
            搜索
          </n-button>

          <n-button size="medium" @click="clearLogFilters">
            清除筛选
          </n-button>

          <div class="ml-auto">
            <n-button type="success" @click="exportLogs">
              <template #icon>
                <n-icon>
                  <Icon icon="mdi:file-export" />
                </n-icon>
              </template>
              导出日志
            </n-button>
          </div>
        </div>

        <!-- 替换表格为控制台日志查看器 -->
        <ConsoleLogViewer :logs="parsedLogLines" :height="500" :searchTerm="logSearch" :levelFilter="logLevel" />
      </div>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTaskLogModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { computed, h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { DataTableColumns, PaginationProps, SelectOption } from 'naive-ui';
import {
  NButton,
  NCard,
  NDataTable,
  NDatePicker,
  NEmpty,
  NGi,
  NGrid,
  NIcon,
  NInput,
  NModal,
  NPopconfirm,
  NProgress,
  NRadioButton,
  NRadioGroup,
  NSelect,
  NSpace,
  NStatistic,
  NTabPane,
  NTabs,
  NTag,
  useMessage
} from 'naive-ui';
import { Icon } from '@iconify/vue';
import {
  getTaskList,
  getTaskStatus,
  getStrmDownloadUrl,
  cancelTask,
  deleteTask,
  getTaskLogs
} from '@/service/api/strm';
import { formatDate } from '@/utils/common';
import TaskStatusDisplay from '@/components/custom/task-status-display.vue';
import ConsoleLogViewer from '@/components/custom/console-log-viewer.vue';

defineOptions({
  name: 'StrmTasks'
});

const message = useMessage();
const router = useRouter();
const loading = ref(false);
const tasksData = ref<any[]>([]);
const fileLoading = ref(false);

// 任务详情相关
const showTaskDetailModal = ref(false);
const currentTask = ref<any>(null);

// 分页配置
const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  showQuickJumper: true
});

// 搜索参数
const searchValue = ref('');
const dateRange = ref<[number, number] | null>(null);
const statusFilter = ref<string | null>(null);

// 状态选项
const statusOptions: SelectOption[] = [
  { label: '等待中', value: 'PENDING' },
  { label: '处理中', value: 'RUNNING' },
  { label: '已完成', value: 'SUCCESS' },
  { label: '已取消', value: 'CANCELED' },
  { label: '失败', value: 'FAILED' }
];

// 日期快捷选项
const dateShortcuts = {
  '今天': () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [today.getTime(), end.getTime()] as [number, number];
  },
  '昨天': () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setDate(end.getDate() - 1);
    end.setHours(23, 59, 59, 999);
    return [yesterday.getTime(), end.getTime()] as [number, number];
  },
  '最近7天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 6);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  },
  '最近30天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 29);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()] as [number, number];
  }
};

// 获取任务列表
const fetchTasks = async () => {
  loading.value = true;
  try {
    const params: Record<string, any> = {
      page: pagination.page,
      page_size: pagination.pageSize
    };

    // 添加搜索条件
    if (searchValue.value) {
      params.search = searchValue.value;
    }

    // 添加日期范围
    if (dateRange.value) {
      params.start_date = new Date(dateRange.value[0]).toISOString().split('T')[0];
      params.end_date = new Date(dateRange.value[1]).toISOString().split('T')[0];
    }

    // 添加状态过滤
    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    const { data } = await getTaskList(params);
    if (data) {
      tasksData.value = data.tasks || [];
      pagination.itemCount = data.total || 0;
    }
  } catch (error: any) {
    message.error(`获取任务列表失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 获取任务详情
const fetchTaskDetail = async (taskId: number) => {
  fileLoading.value = true;
  try {
    const { data } = await getTaskStatus(taskId);
    if (data) {
      currentTask.value = data;
      showTaskDetailModal.value = true;
    }
  } catch (error: any) {
    message.error(`获取任务详情失败: ${error.message || '未知错误'}`);
  } finally {
    fileLoading.value = false;
  }
};

// 处理页码变化
const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchTasks();
};

// 处理每页数量变化
const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1;
  fetchTasks();
};

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  fetchTasks();
};

// 清除筛选条件
const clearFilters = () => {
  searchValue.value = '';
  dateRange.value = null;
  statusFilter.value = null;
  pagination.page = 1;
  fetchTasks();
};

// 跳转到生成页面
const goToGenerate = () => {
  router.push('/strm/generate');
};

// 处理下载STRM文件
const handleDownload = (taskId: number) => {
  const url = getStrmDownloadUrl(taskId);
  window.open(url, '_blank');
};

// 处理取消任务
const handleCancelTask = async (taskId: number) => {
  try {
    await cancelTask(taskId);
    message.success('任务已取消');
    fetchTasks();
  } catch (error: any) {
    message.error(`取消任务失败: ${error.message || '未知错误'}`);
  }
};

// 处理删除任务
const handleDeleteTask = async (taskId: number) => {
  try {
    await deleteTask(taskId);
    message.success('任务已删除');
    fetchTasks();
  } catch (error: any) {
    message.error(`删除任务失败: ${error.message || '未知错误'}`);
  }
};

// 获取任务进度百分比
const getTaskProgressPercentage = (task: any) => {
  const total = task.total_files || 0;
  const processed = task.processed_files || 0;
  return total ? Math.round((processed / total) * 100) : 0;
};

// 获取任务进度百分比文本（带%符号）
const getTaskProgressText = (task: any) => {
  // 使用不换行空格(\u00A0)连接数字和百分比符号
  return `${getTaskProgressPercentage(task)}\u00A0%`;
};

// 获取任务进度状态
const getTaskProgressStatus = (task: any): 'default' | 'success' | 'error' | 'warning' | 'info' => {
  const status = (task.status || '').toUpperCase();
  if (status === 'FAILED') return 'error';
  if (status === 'SUCCESS' || status === 'COMPLETED') return 'success';
  if (status === 'RUNNING') return 'warning';
  return 'default';
};

// 文件列表搜索和过滤
const fileSearchText = ref('');
const fileStatusFilter = ref<string | null>(null);
const fileStatusOptions = [
  { label: '成功', value: 'true', type: 'success' as const },
  { label: '失败', value: 'false', type: 'error' as const }
];

// 过滤后的文件列表
const filteredFiles = computed(() => {
  if (!currentTask.value?.files) return [];

  let filtered = [...(currentTask.value.files || [])];

  // 应用搜索过滤
  if (fileSearchText.value) {
    const searchText = fileSearchText.value.toLowerCase();
    filtered = filtered.filter(file =>
      (file.source_path || '').toLowerCase().includes(searchText) ||
      (file.target_path || '').toLowerCase().includes(searchText)
    );
  }

  // 应用状态过滤
  if (fileStatusFilter.value !== null) {
    // 转换为布尔值
    const filterValue = fileStatusFilter.value === 'true';
    filtered = filtered.filter(file => file.is_success === filterValue);
  }

  return filtered;
});

// 表格列定义
const columns: DataTableColumns<any> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '任务名称',
    key: 'name',
    width: 300,
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 120,
    align: 'center',
    render(row) {
      return h(TaskStatusDisplay, { status: row.status });
    }
  },
  {
    title: '处理进度',
    key: 'progress',
    width: 200,
    align: 'center',
    render(row) {
      const total = row.total_files || 0;
      const processed = row.processed_files || 0;
      const percentage = total ? Math.round((processed / total) * 100) : 0;

      let status: 'default' | 'success' | 'error' | 'warning' | 'info' = 'default';
      if (row.status === 'FAILED') {
        status = 'error';
      } else if (row.status === 'SUCCESS') {
        status = 'success';
      }

      return h('div', { class: 'flex flex-col items-center' }, [
        h(
          NProgress,
          {
            percentage,
            showIndicator: false,
            processing: row.status === 'RUNNING',
            type: 'line',
            status,
            style: 'width: 100%;'
          }
        ),
        h('div', { class: 'flex flex-col items-center mt-1' }, [
          h('span', { class: 'text-xs', style: 'white-space: nowrap;' }, `${percentage}%`),
          h('span', { class: 'text-xs text-gray-500' }, `${processed}/${total} 文件`)
        ])
      ]);
    }
  },
  {
    title: '开始时间',
    key: 'create_time',
    width: 180,
    align: 'center',
    render(row) {
      // 使用start_time作为创建时间的数据源
      const timeValue = row.start_time || row.create_time || row.created_at;
      return formatDate(timeValue);
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 190,
    align: 'center',
    fixed: 'right',
    render(row: any) {
      return h(
        NSpace,
        { justify: "center" },
        {
          default: () => [
            // 查看按钮
            h(
              NButton,
              {
                size: 'small',
                onClick: () => fetchTaskDetail(row.id),
              },
              { default: () => '详情' }
            ),
            // 日志按钮
            h(
              NButton,
              {
                size: 'small',
                onClick: () => openLogViewer(row.id),
                type: 'info',
              },
              { default: () => '日志' }
            ),
            row.status === 'SUCCESS' ?
              h(
                NButton,
                {
                  size: 'small',
                  type: 'success',
                  onClick: () => handleDownload(row.id)
                },
                { default: () => '下载' }
              ) : null,
            row.status === 'PENDING' || row.status === 'RUNNING' ?
              h(
                NPopconfirm,
                {
                  onPositiveClick: () => handleCancelTask(row.id)
                },
                {
                  default: () => '确定要取消此任务吗？',
                  trigger: () => h(
                    NButton,
                    {
                      size: 'small',
                      type: 'warning'
                    },
                    { default: () => '取消' }
                  )
                }
              ) : null,
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDeleteTask(row.id)
              },
              {
                default: () => '确定要删除此任务吗？',
                trigger: () => h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error'
                  },
                  { default: () => '删除' }
                )
              }
            )
          ]
        }
      );
    }
  }
];

// 文件列表的列定义
const fileColumns: DataTableColumns<any> = [
  {
    title: '源文件路径',
    key: 'source_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '目标文件路径',
    key: 'target_path',
    ellipsis: {
      tooltip: true
    },
    align: 'center'
  },
  {
    title: '状态',
    key: 'is_success',
    width: 100,
    align: 'center',
    render(row) {
      return h(
        NTag,
        { type: row.is_success ? 'success' : 'error' },
        { default: () => row.is_success ? '成功' : '失败' }
      );
    }
  },
  {
    title: '错误信息',
    key: 'error_message',
    ellipsis: {
      tooltip: true
    },
    align: 'center',
    render(row) {
      return row.error_message || '-';
    }
  }
];

// 文件视图模式
const fileViewMode = ref<'table' | 'tree'>('table');

// 日志查看相关
const showTaskLogModal = ref(false);
const currentTaskId = ref<number | null>(null);
const logLoading = ref(false);
const logData = ref<any[]>([]);
const logSearch = ref('');
const logLevel = ref<string | null>(null);
const logLevelOptions = [
  { label: '信息', value: 'INFO', type: 'info' as const },
  { label: '错误', value: 'ERROR', type: 'error' as const },
  { label: '警告', value: 'WARNING', type: 'warning' as const },
  { label: '调试', value: 'DEBUG', type: 'default' as const }
];

// 解析后的日志行数据 - 用于控制台显示
const parsedLogLines = computed(() => {
  // 如果没有数据，返回空数组
  if (!logData.value || logData.value.length === 0) {
    return [];
  }

  // 将日志记录转换为纯文本行格式
  return logData.value.map(line => {
    // 如果已经是字符串，直接返回
    if (typeof line === 'string') {
      return line;
    }

    // 兼容处理：如果是对象格式，则格式化（保留向后兼容性）
    const timestamp = line.timestamp ? formatDate(line.timestamp, 'YYYY-MM-DD HH:mm:ss') : '';
    const level = line.level || 'INFO';
    const message = line.message || '';

    // 构建日志行
    let logLine = `[${timestamp}] [${level}] ${message}`;

    // 添加文件路径信息（如果有）
    if (line.file_path) {
      logLine += ` | 文件: ${line.file_path}`;
    }

    // 添加目标路径信息（如果有）
    if (line.target_path) {
      logLine += ` | 目标: ${line.target_path}`;
    }

    // 添加错误信息（如果有）
    if (line.error_message) {
      logLine += ` | 错误: ${line.error_message}`;
    }

    // 添加状态信息（如果有）
    if (typeof line.is_success === 'boolean') {
      logLine += ` | 状态: ${line.is_success ? '成功' : '失败'}`;
    }

    // 返回日志行
    return logLine;
  });
});

// 日志分页
const logPagination = reactive({
  page: 1,
  pageSize: 50,
  itemCount: 0
});

// 获取任务日志
const fetchTaskLogs = async () => {
  if (!currentTaskId.value) return;

  logLoading.value = true;
  try {
    const params: Record<string, any> = {
      page: logPagination.page,
      page_size: logPagination.pageSize
    };

    // 添加搜索和日志级别过滤
    if (logSearch.value) {
      params.search = logSearch.value;
    }

    if (logLevel.value) {
      params.level = logLevel.value;
    }

    const { data } = await getTaskLogs(currentTaskId.value, params);
    if (data) {
      // 处理日志原始内容
      if (data.raw_content) {
        // 从raw_content分割日志行
        const logLines = data.raw_content.split('\n')
          .filter((line: string) => line.trim() !== '')
          .map((line: string) => line);

        logData.value = logLines;
      } else {
        logData.value = [];
      }

      logPagination.itemCount = data.total || 0;
    }
  } catch (error: any) {
    message.error(`获取任务日志失败: ${error.message || '未知错误'}`);
  } finally {
    logLoading.value = false;
  }
};

// 处理日志分页变化
const handleLogPageChange = (page: number) => {
  logPagination.page = page;
  fetchTaskLogs();
};

// 处理日志每页数量变化
const handleLogPageSizeChange = (pageSize: number) => {
  logPagination.pageSize = pageSize;
  logPagination.page = 1;
  fetchTaskLogs();
};

// 处理日志搜索
const handleLogSearch = () => {
  logPagination.page = 1;
  fetchTaskLogs();
};

// 清除日志筛选条件
const clearLogFilters = () => {
  logSearch.value = '';
  logLevel.value = null;
  logPagination.page = 1;
  fetchTaskLogs();
};

// 打开日志查看器
const openLogViewer = (taskId: number) => {
  currentTaskId.value = taskId;
  logData.value = [];
  logSearch.value = '';
  logLevel.value = null;
  logPagination.page = 1;
  showTaskLogModal.value = true;
  fetchTaskLogs();
};

// 根据日志级别获取标签类型
const getLogLevelType = (level: string): 'success' | 'error' | 'warning' | 'info' | 'default' => {
  switch (level?.toUpperCase()) {
    case 'INFO':
      return 'info';
    case 'ERROR':
      return 'error';
    case 'WARNING':
      return 'warning';
    case 'DEBUG':
      return 'default';
    default:
      return 'default';
  }
};

// 导出日志
const exportLogs = () => {
  if (!logData.value || logData.value.length === 0) {
    message.warning('暂无日志可导出');
    return;
  }

  const taskName = currentTask.value?.name || `任务${currentTaskId.value}`;
  const fileName = `${taskName}_日志_${formatDate(new Date(), 'YYYY-MM-DD_HH-mm')}.txt`;

  // 将日志数据转换为文本
  const logText = parsedLogLines.value.join('\n');

  // 创建Blob对象
  const blob = new Blob([logText], { type: 'text/plain;charset=utf-8' });

  // 创建下载链接
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = fileName;

  // 点击下载
  document.body.appendChild(link);
  link.click();

  // 清理
  document.body.removeChild(link);
  message.success('日志导出成功');
};

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks();
});

// 添加计算处理时长的辅助函数
const formatDuration = (seconds: number) => {
  if (seconds < 0) return '-'; // 处理异常情况

  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  if (days > 0) {
    return `${days}天${hours}小时`;
  }

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`;
  }

  if (minutes > 0) {
    return `${minutes}分钟${remainingSeconds}秒`;
  }

  // 当时间差小于1秒但大于0时，显示"不到1秒"而不是"0秒"
  if (seconds > 0 && remainingSeconds === 0) {
    return "不到1秒";
  }

  return `${remainingSeconds}秒`;
};

const isTaskProcessing = (task: any) => {
  return task.status === 'RUNNING';
};

const getTaskDuration = (task: any) => {
  if (!task.start_time) return '尚未开始';

  const start = new Date(task.start_time).getTime();

  // 如果任务已完成，计算开始到结束的时长
  if (task.end_time) {
    const end = new Date(task.end_time).getTime();
    const duration = (end - start) / 1000; // 转换为秒
    return formatDuration(duration);
  }

  // 如果任务正在进行中，计算开始到现在的时长，并标记为进行中
  if (isTaskProcessing(task)) {
    const now = new Date().getTime();
    const duration = (now - start) / 1000; // 转换为秒
    return `${formatDuration(duration)} (进行中)`;
  }

  return '尚未完成';
};
</script>

<style scoped>
.task-detail-container {
  padding: 0;
}

.task-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(239, 239, 245, 0.7);
}

.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
  margin-right: 8px;
  flex-shrink: 0;
  width: 80px;
}

.info-item .value {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.stat-time {
  font-size: 14px;
  color: #606266;
}

.text-success {
  color: #2ab85e;
}

.text-error {
  color: #f5222d;
}

.text-secondary-text {
  color: #8c8c8c;
}

.processing-status {
  color: #faad14;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0.6;
  }
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.mr-1 {
  margin-right: 4px;
}

.text-center {
  text-align: center;
}

.text-lg {
  font-size: 18px;
}

.text-2xl {
  font-size: 24px;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.percent-text {
  font-size: 24px;
  font-weight: 700;
  white-space: nowrap;
  line-height: 1.2;
}

.no-wrap {
  white-space: nowrap;
}

.min-h-300px {
  min-height: 300px;
}

.tree-view-container {
  padding: 16px 0;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-4 {
  gap: 16px;
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}
</style>
