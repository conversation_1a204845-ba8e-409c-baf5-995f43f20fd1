<template>
  <div>
    <n-card title="115 目录树文件上传" :bordered="false" class="h-full rounded-8px shadow-sm">
      <n-upload
        multiple
        directory-dnd
        action="/api/v1/strm/upload"
        :max="5"
        accept=".txt"
        @before-upload="handleBeforeUpload"
        @finish="handleFinish"
      >
        <n-upload-dragger>
          <div class="flex-col-center">
            <icon-mdi-upload class="text-68px text-primary" />
            <p class="mt-12px text-16px text-primary">点击或拖拽文件到这里上传</p>
            <p class="mt-8px text-12px text-secondary-text">
              请上传从115网盘导出的 .txt 格式的目录树文件。
            </p>
          </div>
        </n-upload-dragger>
      </n-upload>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import type { UploadFileInfo } from 'naive-ui';

defineOptions({
  name: 'StrmUpload'
});

const handleBeforeUpload = async (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }): Promise<boolean> => {
  if (!data.file.file?.type.includes('text/plain')) {
    window.$message.error('只能上传 .txt 格式的文件，请重新上传');
    return false;
  }
  if (data.file.file.size > 10 * 1024 * 1024) {
    window.$message.error('文件大小不能超过 10MB，请重新上传');
    return false;
  }
  return true;
};

const handleFinish = ({ file, event }: { file: UploadFileInfo; event?: ProgressEvent }) => {
  const res = JSON.parse((event?.target as XMLHttpRequest).response);
  window.$message.success(`'${file.name}' 上传成功, 已保存到: ${res.path}`);
  return file;
};
</script>

<style scoped></style>
