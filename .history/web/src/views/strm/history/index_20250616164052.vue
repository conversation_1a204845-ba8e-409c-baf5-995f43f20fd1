<template>
  <div>
    <n-card title="上传历史" :bordered="false" class="h-full rounded-8px shadow-sm">
      <n-data-table
        :columns="columns"
        :data="historyData"
        :loading="loading"
        :pagination="pagination"
        :row-key="row => row.id"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, h } from 'vue';
import { NDataTable, NCard, NButton, NSpace, NPopconfirm } from 'naive-ui';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { getUploadHistory, deleteUploadRecord, parseDirectoryTree, getDownloadUrl } from '@/service/api/strm';
import { useMessage } from 'naive-ui';
import { localStg } from '@/utils/storage';

defineOptions({
  name: 'StrmHistory'
});

const message = useMessage();
const loading = ref(false);
const historyData = ref<StrmAPI.UploadRecord[]>([]);

const columns: DataTableColumns<StrmAPI.UploadRecord> = [
  { title: 'ID', key: 'id', width: 80 },
  { title: '文件名', key: 'filename', ellipsis: { tooltip: true } },
  {
    title: '文件大小',
    key: 'filesize',
    render(row) {
      return `${(row.filesize / 1024 / 1024).toFixed(2)} MB`;
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row) {
      const statusMap: Record<string, string> = {
        'uploaded': '已上传',
        'parsing': '解析中',
        'parsed': '已解析',
        'failed': '解析失败'
      };
      return statusMap[row.status] || row.status;
    }
  },
  {
    title: '上传时间',
    key: 'create_time',
    render(row) {
      return new Date(row.create_time).toLocaleString();
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          // 下载按钮
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleDownload(row)
          }, { default: () => '下载' }),

          // 解析按钮 - 仅显示在未解析的文件
          row.status === 'uploaded' || row.status === 'failed' ?
            h(NButton, {
              size: 'small',
              type: 'info',
              onClick: () => handleParse(row)
            }, { default: () => '解析' }) : null,

          // 删除按钮 - 使用确认对话框
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row)
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error'
            }, { default: () => '删除' }),
            default: () => '确定要删除此记录吗？'
          })
        ]
      });
    }
  }
];

const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0
});

const fetchHistory = async () => {
  loading.value = true;
  try {
    const { data } = await getUploadHistory({
      page: pagination.page,
      page_size: pagination.pageSize
    });
    if (data) {
      historyData.value = data.records;
      pagination.itemCount = data.total;
    }
  } catch (error) {
    message.error(`获取历史记录失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchHistory();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1; // Reset to first page
  fetchHistory();
};

const handleDelete = async (row: StrmAPI.UploadRecord) => {
  try {
    await deleteUploadRecord(row.id);
    message.success('记录已成功删除');
    fetchHistory(); // 刷新数据
  } catch (error) {
    message.error(`删除失败: ${error.message || '未知错误'}`);
  }
};

const handleDownload = (row: StrmAPI.UploadRecord) => {
  // 显示加载提示
  const loadingMsg = message.loading('正在准备下载...', {
    duration: 10000 // 10秒后自动关闭
  });

  try {
    // 获取认证令牌 - 使用Fast-Soy-Admin的存储工具localStg
    const token = localStg.get('token') || '';
    if (!token) {
      message.error('认证信息不存在，请重新登录');
      loadingMsg?.destroy(); // 关闭加载提示
      return;
    }

        // 构建下载URL，直接在URL中添加token参数
    const baseUrl = getDownloadUrl(row.id);
    const downloadUrl = `${baseUrl}?token=${encodeURIComponent(token)}`;

    // 创建一个隐藏的iframe来触发下载
    const iframe = document.createElement('iframe');
    iframe.src = downloadUrl;
    iframe.style.display = 'none';
    document.body.appendChild(iframe);

    // 设置超时检测下载是否开始
    setTimeout(() => {
      // 清理DOM元素
      if (document.body.contains(form)) {
        document.body.removeChild(form);
      }

      // 不立即移除iframe，给下载一些时间启动
      setTimeout(() => {
        if (document.body.contains(iframe)) {
          document.body.removeChild(iframe);
        }
      }, 5000);

      // 关闭加载提示并显示成功消息
      loadingMsg?.destroy();
      message.success('下载请求已发送');
    }, 1000);
  } catch (error) {
    // 关闭加载提示
    loadingMsg?.destroy();
    message.error(`下载文件失败: ${error.message || '未知错误'}`);
  }
};

const handleParse = async (row: StrmAPI.UploadRecord) => {
  try {
    // 对于已上传但未解析的文件进行解析
    loading.value = true;
    await parseDirectoryTree({
      record_id: row.id,
      file_path: '' // API会通过record_id获取文件路径
    });
    message.success('文件解析成功');
    fetchHistory(); // 刷新数据
  } catch (error) {
    message.error(`解析失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchHistory();
});
</script>

<style scoped></style>
