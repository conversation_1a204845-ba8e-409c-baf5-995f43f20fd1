<template>
  <div>
    <n-card title="上传历史" :bordered="false" class="h-full rounded-8px shadow-sm">
      <n-data-table :columns="columns" :data="historyData" :loading="loading" :pagination="pagination"
        :row-key="row => row.id" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" />
    </n-card>

    <!-- 解析结果对话框 -->
    <n-modal v-model:show="showResultModal" preset="card" title="文件解析结果" :bordered="false" size="huge"
      style="max-width: 900px;">
      <n-spin :show="resultLoading">
        <div v-if="parseResult">
          <!-- 文件信息 -->
          <n-descriptions label-placement="left" bordered>
            <n-descriptions-item label="文件名">{{ parseResult.file_name }}</n-descriptions-item>
            <n-descriptions-item label="总文件数">{{ parseResult.total_files }}</n-descriptions-item>
            <n-descriptions-item label="解析时间">{{ currentRecord && currentRecord.parse_time ? new
              Date(currentRecord.parse_time).toLocaleString() : '未知' }}</n-descriptions-item>
          </n-descriptions>

          <!-- 统计信息 -->
          <div class="mt-4">
            <h3 class="mb-2">文件统计</h3>
            <n-grid :cols="7" :x-gap="8">
              <n-grid-item>
                <n-statistic label="总文件" :value="parseResult.stats.total" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="视频" :value="parseResult.stats.video" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="音频" :value="parseResult.stats.audio" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="图片" :value="parseResult.stats.image" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="字幕" :value="parseResult.stats.subtitle" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="元数据" :value="parseResult.stats.metadata" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="其它" :value="parseResult.stats.other" />
              </n-grid-item>
            </n-grid>
          </div>

          <!-- 文件列表 -->
          <div class="mt-4">
            <h3 class="mb-2">文件列表</h3>

            <!-- 文件类型过滤 -->
            <div class="mb-3">
              <n-radio-group v-model:value="fileTypeFilter" size="small">
                <n-radio-button value="all">全部</n-radio-button>
                <n-radio-button value="video">视频</n-radio-button>
                <n-radio-button value="audio">音频</n-radio-button>
                <n-radio-button value="image">图片</n-radio-button>
                <n-radio-button value="subtitle">字幕</n-radio-button>
                <n-radio-button value="metadata">元数据</n-radio-button>
                <n-radio-button value="other">其它</n-radio-button>
              </n-radio-group>
            </div>

            <n-data-table :columns="fileColumns" :data="filteredFiles" size="small" :pagination="filePagination"
              @update:page="handleFilePageChange" @update:page-size="handleFilePageSizeChange" />
          </div>
        </div>
      </n-spin>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, h, computed } from 'vue';
import { NDataTable, NCard, NButton, NSpace, NPopconfirm, NModal, NSpin, NGrid, NDescriptions, NDescriptionsItem, NStatistic, NRadioGroup, NRadioButton } from 'naive-ui';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import { getUploadHistory, deleteUploadRecord, parseDirectoryTree, getDownloadUrl, getParseResult } from '@/service/api/strm';
import { useMessage } from 'naive-ui';
import { localStg } from '@/utils/storage';

defineOptions({
  name: 'StrmHistory'
});

const message = useMessage();
const loading = ref(false);
const historyData = ref<StrmAPI.UploadRecord[]>([]);

// 解析结果相关
const showResultModal = ref(false);
const resultLoading = ref(false);
const parseResult = ref<StrmAPI.ParseResult | null>(null);
const currentRecord = ref<StrmAPI.UploadRecord | null>(null); // 当前查看的记录
const fileTypeFilter = ref('all');  // 文件类型过滤

// 文件列表列定义
const fileColumns: DataTableColumns<any> = [
  { title: '文件名', key: 'file_name', ellipsis: { tooltip: true } },
  { title: '文件类型', key: 'file_type' },
  { title: '扩展名', key: 'extension' },
  { title: '路径', key: 'path', ellipsis: { tooltip: true } }
];

const columns: DataTableColumns<StrmAPI.UploadRecord> = [
  { title: 'ID', key: 'id', width: 80 },
  { title: '文件名', key: 'filename', ellipsis: { tooltip: true } },
  {
    title: '文件大小',
    key: 'filesize',
    render(row) {
      return `${(row.filesize / 1024 / 1024).toFixed(2)} MB`;
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row) {
      const statusMap: Record<string, string> = {
        'uploaded': '已上传',
        'parsing': '解析中',
        'parsed': '已解析',
        'failed': '解析失败'
      };
      return statusMap[row.status] || row.status;
    }
  },
  {
    title: '上传时间',
    key: 'create_time',
    render(row) {
      return new Date(row.create_time).toLocaleString();
    }
  },
  {
    title: '解析时间',
    key: 'parse_time',
    render(row) {
      return row.parse_time ? new Date(row.parse_time).toLocaleString() : '-';
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          // 下载按钮
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleDownload(row)
          }, { default: () => '下载' }),

          // 查看结果按钮 - 仅显示给已解析的文件
          row.status === 'parsed' ?
            h(NButton, {
              size: 'small',
              type: 'info',
              onClick: () => handleViewResult(row)
            }, { default: () => '查看结果' }) : null,

          // 解析按钮 - 仅显示在未解析的文件
          row.status === 'uploaded' || row.status === 'failed' ?
            h(NButton, {
              size: 'small',
              type: 'info',
              onClick: () => handleParse(row)
            }, { default: () => '解析' }) : null,

          // 删除按钮 - 使用确认对话框
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row)
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error'
            }, { default: () => '删除' }),
            default: () => '确定要删除此记录吗？'
          })
        ]
      });
    }
  }
];

const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0
});

// 文件列表分页配置
const filePagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
});

const fetchHistory = async () => {
  loading.value = true;
  try {
    const { data } = await getUploadHistory({
      page: pagination.page,
      page_size: pagination.pageSize
    });
    if (data) {
      historyData.value = data.records;
      pagination.itemCount = data.total;
    }
  } catch (error) {
    message.error(`获取历史记录失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchHistory();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1; // Reset to first page
  fetchHistory();
};

const handleDelete = async (row: StrmAPI.UploadRecord) => {
  try {
    await deleteUploadRecord(row.id);
    message.success('记录已成功删除');
    fetchHistory(); // 刷新数据
  } catch (error) {
    message.error(`删除失败: ${error.message || '未知错误'}`);
  }
};

const handleDownload = async (row: StrmAPI.UploadRecord) => {
  // 显示加载提示
  const loadingMsg = message.loading('正在准备下载...', {
    duration: 10000 // 10秒后自动关闭
  });

  try {
    // 获取认证令牌 - 使用Fast-Soy-Admin的存储工具localStg
    const token = localStg.get('token') || '';
    if (!token) {
      message.error('认证信息不存在，请重新登录');
      loadingMsg?.destroy(); // 关闭加载提示
      return;
    }

    // 构建下载URL，直接在URL中添加token参数
    const baseUrl = getDownloadUrl(row.id);
    const downloadUrl = `${baseUrl}?token=${encodeURIComponent(token)}`;

    // 方法1：使用fetch + blob方式下载（更加可靠但可能较慢）
    try {
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`);
      }

      // 获取文件名
      let filename = row.filename;
      const contentDisposition = response.headers.get('content-disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // 获取blob
      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
      }, 100);

      loadingMsg?.destroy();
      message.success('文件下载已开始');
    } catch (e) {
      // 如果fetch方法失败，尝试方法2
      console.error('Fetch下载失败，尝试直接打开方式:', e);

      // 方法2：直接在新窗口中打开下载链接
      window.open(downloadUrl, '_blank');

      loadingMsg?.destroy();
      message.success('已在新窗口打开下载链接');
    }
  } catch (error) {
    // 关闭加载提示
    loadingMsg?.destroy();
    message.error(`下载文件失败: ${error.message || '未知错误'}`);
  }
};

const handleParse = async (row: StrmAPI.UploadRecord) => {
  try {
    // 对于已上传但未解析的文件进行解析
    loading.value = true;
    await parseDirectoryTree({
      record_id: row.id
    });
    message.success('文件解析成功');
    fetchHistory(); // 刷新数据
  } catch (error) {
    message.error(`解析失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 所有的解析文件数据(未过滤)
const allParsedFiles = ref<StrmAPI.ParsedFile[]>([]);

// 文件分页处理函数 - 这里只处理前端分页，不再重新调用API
const handleFilePageChange = (page: number) => {
  filePagination.page = page;
};

const handleFilePageSizeChange = (pageSize: number) => {
  filePagination.pageSize = pageSize;
  filePagination.page = 1; // 重置到第一页
};

// 加载解析结果 - 一次性加载所有数据，然后在前端处理过滤和分页
const loadParseResult = async () => {
  if (!currentRecord.value) return;

  try {
    resultLoading.value = true;
    const { data } = await getParseResult(currentRecord.value.id);
    parseResult.value = data;

    // 保存所有文件数据，用于前端过滤
    allParsedFiles.value = data.parsed_files || [];

    // 更新分页总数
    filePagination.itemCount = allParsedFiles.value.length;
  } catch (error: any) {
    message.error(`获取解析结果失败: ${error.message || '未知错误'}`);
  } finally {
    resultLoading.value = false;
  }
};

// 查看解析结果
const handleViewResult = async (row: StrmAPI.UploadRecord) => {
  if (row.status !== 'parsed') {
    message.warning('该文件尚未解析，无法查看结果');
    return;
  }

  try {
    resultLoading.value = true;
    showResultModal.value = true;
    currentRecord.value = row; // 保存当前记录

    // 重置分页
    filePagination.page = 1;
    await loadParseResult();
  } catch (error: any) {
    message.error(`获取解析结果失败: ${error.message || '未知错误'}`);
    showResultModal.value = false;
  } finally {
    resultLoading.value = false;
  }
};

// 根据文件类型过滤文件列表并处理分页
const filteredFiles = computed(() => {
  if (allParsedFiles.value.length === 0) return [];

  // 首先按文件类型过滤
  let result = allParsedFiles.value;
  if (fileTypeFilter.value !== 'all') {
    result = result.filter(file => file.file_type === fileTypeFilter.value);
  }

  // 更新过滤后的总数量
  filePagination.itemCount = result.length;

  // 然后手动分页
  const startIdx = (filePagination.page - 1) * filePagination.pageSize;
  const endIdx = startIdx + filePagination.pageSize;

  return result.slice(startIdx, endIdx);
});

onMounted(() => {
  fetchHistory();
});
</script>

<style scoped></style>
