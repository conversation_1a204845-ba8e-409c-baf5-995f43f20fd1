<template>
  <div>
    <n-card :bordered="false" class="h-full rounded-8px shadow-sm">
      <div class="flex flex-col space-y-4 mb-4">
        <div class="flex justify-between">
          <h2 class="text-xl font-bold">上传历史</h2>
          <n-button type="primary" @click="goToUpload">上传新文件</n-button>
        </div>

        <div class="flex flex-wrap items-center gap-2">
          <n-input v-model:value="historySearchValue" placeholder="搜索文件名..." clearable style="width: 200px"
            @keyup.enter="handleHistorySearch" />

          <n-date-picker v-model:value="dateRange" type="daterange" clearable style="width: 260px" placeholder="选择时间范围"
            :shortcuts="dateShortcuts" />

          <n-button type="primary" size="medium" style="width: 80px; height: 34px;" @click="handleHistorySearch">
            搜索
          </n-button>
        </div>
      </div>

      <n-data-table :columns="columns" :data="historyData" :loading="loading" :pagination="pagination"
        :row-key="row => row.id" @update:page="handlePageChange" @update:page-size="handlePageSizeChange" />
    </n-card>

    <!-- 解析结果对话框 -->
    <n-modal v-model:show="showResultModal" preset="card" title="文件解析结果" :bordered="false" size="huge"
      style="max-width: 900px;">
      <n-spin :show="resultLoading">
        <div v-if="parseResult">
          <!-- 文件信息 -->
          <n-descriptions label-placement="left" bordered>
            <n-descriptions-item label="文件名">{{ parseResult.file_name }}</n-descriptions-item>
            <n-descriptions-item label="总文件数">{{ parseResult.total_files }}</n-descriptions-item>
            <n-descriptions-item label="解析时间">{{ currentRecord && currentRecord.parse_time ? new
              Date(currentRecord.parse_time).toLocaleString() : '未知' }}</n-descriptions-item>
          </n-descriptions>

          <!-- 统计信息 -->
          <div class="mt-4">
            <h3 class="mb-2">文件统计</h3>
            <n-grid :cols="7" :x-gap="8">
              <n-grid-item>
                <n-statistic label="总文件" :value="parseResult.stats.total" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="视频" :value="parseResult.stats.video" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="音频" :value="parseResult.stats.audio" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="图片" :value="parseResult.stats.image" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="字幕" :value="parseResult.stats.subtitle" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="元数据" :value="parseResult.stats.metadata" />
              </n-grid-item>
              <n-grid-item>
                <n-statistic label="其它" :value="parseResult.stats.other" />
              </n-grid-item>
            </n-grid>
          </div>

          <!-- 文件列表 -->
          <div class="mt-4">
            <h3 class="mb-2">文件列表</h3>

            <!-- 调整布局，使用两行排列，并添加底部边距确保与表格有足够间距 -->
            <div class="mb-4">
              <!-- 第一行：文件类型过滤 -->
              <div class="mb-3">
                <n-radio-group v-model:value="fileTypeFilter" size="small" @update:value="handleFileTypeChange">
                  <n-radio-button value="all">全部 ({{ parseResult.stats.total || 0 }})</n-radio-button>
                  <n-radio-button value="video">视频 ({{ parseResult.stats.video || 0 }})</n-radio-button>
                  <n-radio-button value="audio">音频 ({{ parseResult.stats.audio || 0 }})</n-radio-button>
                  <n-radio-button value="image">图片 ({{ parseResult.stats.image || 0 }})</n-radio-button>
                  <n-radio-button value="subtitle">字幕 ({{ parseResult.stats.subtitle || 0 }})</n-radio-button>
                  <n-radio-button value="metadata">元数据 ({{ parseResult.stats.metadata || 0 }})</n-radio-button>
                  <n-radio-button value="other">其它 ({{ parseResult.stats.other || 0 }})</n-radio-button>
                </n-radio-group>
                <div v-if="parseResult.pagination" class="text-xs text-gray-500 mt-1">
                  当前显示: {{ parsedFiles.length }}条，总计: {{ parseResult.pagination.total }}条
                </div>
              </div>

              <!-- 第二行：视图切换与搜索 -->
              <div class="flex justify-end items-center">
                <n-input v-model:value="fileSearchValue" placeholder="搜索文件名..." clearable style="width: 200px"
                  class="mr-2" @keyup.enter="handleSearch" />
                <n-button type="primary" class="mr-2" size="medium" style="width: 80px; height: 34px;"
                  @click="handleSearch">
                  搜索
                </n-button>
                <n-button-group size="medium">
                  <n-button :type="viewMode === 'table' ? 'primary' : 'default'" @click="handleViewModeChange('table')"
                    style="width: 50px; height: 34px;">
                    表格
                  </n-button>
                  <n-button :type="viewMode === 'tree' ? 'primary' : 'default'" @click="handleViewModeChange('tree')"
                    style="width: 50px; height: 34px;">
                    树形
                  </n-button>
                </n-button-group>
              </div>
            </div>

            <!-- 文件内容区域：添加固定高度和边框 -->
            <div class="file-content-area border border-gray-200 rounded-md"
              style="height: 400px; max-height: 60vh; overflow: auto;">
              <!-- 表格视图 -->
              <n-data-table v-if="viewMode === 'table'" :columns="fileColumns" :data="parsedFiles" size="small"
                :pagination="filePagination" :loading="resultLoading" remote :row-key="row => row.file_name + row.path"
                @update:page="handleFilePageChange" @update:page-size="handleFilePageSizeChange" class="w-full" />

              <!-- 树形视图 -->
              <div v-else class="h-full">
                <file-tree-view :record-id="currentRecord?.id || ''" :file-type-filter="fileTypeFilter"
                  :search-value="fileSearchValue" :root-directories="rootDirectories" :root-files="rootFiles"
                  class="p-2 h-full" ref="fileTreeViewRef" />
              </div>
            </div>
          </div>
        </div>
      </n-spin>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import type { DataTableColumns, PaginationProps } from 'naive-ui';
import {
  NButton,
  NCard,
  NDataTable,
  NDatePicker,
  NDescriptions,
  NDescriptionsItem,
  NGrid,
  NGridItem,
  NInput,
  NModal,
  NPopconfirm,
  NRadioButton,
  NRadioGroup,
  NSpace,
  NSpin,
  NStatistic,
  useMessage
} from 'naive-ui';
import FileTreeView from '@/components/custom/file-tree-view.vue';
import { deleteUploadRecord, getDirectoryContent, getDownloadUrl, getParseResult, getUploadHistory, parseDirectoryTree, searchFiles } from '@/service/api/strm';
import { localStg } from '@/utils/storage';

defineOptions({
  name: 'StrmHistory'
});

const message = useMessage();
const loading = ref(false);
const historyData = ref<StrmAPI.UploadRecord[]>([]);

// 解析结果相关
const showResultModal = ref(false);
const resultLoading = ref(false);
const parseResult = ref<StrmAPI.ParseResult | null>(null);
const currentRecord = ref<StrmAPI.UploadRecord | null>(null); // 当前查看的记录
const fileTypeFilter = ref('all');  // 文件类型过滤
const parsedFiles = ref<StrmAPI.ParsedFile[]>([]);  // 当前显示的解析文件
// const treeViewFiles = ref<StrmAPI.ParsedFile[]>([]); // 树形视图专用文件数据（全部）
const fileSearchValue = ref(''); // 文件搜索关键词
const viewMode = ref<'table' | 'tree'>('table'); // 视图模式：表格或树形
// 根目录下的内容（用于懒加载）
const rootDirectories = ref<string[]>([]);
const rootFiles = ref<StrmAPI.ParsedFile[]>([]);

// 分页配置
const pagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0
});

// 文件列表分页配置 - 使用服务端分页
const filePagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  showQuickJumper: true,
});

// 文件列表列定义
const fileColumns: DataTableColumns<StrmAPI.ParsedFile> = [
  { title: '文件名', key: 'file_name', ellipsis: { tooltip: true } },
  { title: '文件类型', key: 'file_type' },
  { title: '扩展名', key: 'extension' },
  { title: '路径', key: 'path', ellipsis: { tooltip: true } }
];

// 历史记录搜索值
const historySearchValue = ref('');

// 日期范围
const dateRange = ref<[number, number] | null>(null);

// 日期快捷选项
const dateShortcuts = {
  '今天': () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [today.getTime(), end.getTime()];
  },
  '昨天': () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setDate(end.getDate() - 1);
    end.setHours(23, 59, 59, 999);
    return [yesterday.getTime(), end.getTime()];
  },
  '最近7天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 6);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()];
  },
  '最近30天': () => {
    const start = new Date();
    start.setDate(start.getDate() - 29);
    start.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()];
  }
};

// 原始历史数据
const originalHistoryData = ref<StrmAPI.UploadRecord[]>([]);

// 获取路由器
const router = useRouter();

// 获取历史数据
const fetchHistory = async () => {
  loading.value = true;
  try {
    const { data } = await getUploadHistory({
      page: pagination.page,
      page_size: pagination.pageSize
    });
    if (data) {
      historyData.value = data.records;
      originalHistoryData.value = data.records; // 保存原始数据
      pagination.itemCount = data.total;
    }
  } catch (error: any) {
    message.error(`获取历史记录失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

const handlePageChange = (page: number) => {
  pagination.page = page;
  fetchHistory();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1; // Reset to first page
  fetchHistory();
};

// 删除记录操作
const deleteRecord = async (row: StrmAPI.UploadRecord) => {
  try {
    await deleteUploadRecord(row.id);
    message.success('记录已成功删除');
    fetchHistory(); // 刷新数据
  } catch (error: any) {
    message.error(`删除失败: ${error.message || '未知错误'}`);
  }
};

// 下载文件操作
const downloadFile = async (row: StrmAPI.UploadRecord) => {
  // 显示加载提示
  const loadingMsg = message.loading('正在准备下载...', {
    duration: 10000 // 10秒后自动关闭
  });

  try {
    // 获取认证令牌 - 使用Fast-Soy-Admin的存储工具localStg
    const token = localStg.get('token') || '';
    if (!token) {
      message.error('认证信息不存在，请重新登录');
      loadingMsg?.destroy(); // 关闭加载提示
      return;
    }

    // 构建下载URL，直接在URL中添加token参数
    const baseUrl = getDownloadUrl(row.id);
    const downloadUrl = `${baseUrl}?token=${encodeURIComponent(token)}`;

    // 方法1：使用fetch + blob方式下载（更加可靠但可能较慢）
    try {
      const response = await fetch(downloadUrl);

      if (!response.ok) {
        throw new Error(`下载失败: ${response.statusText}`);
      }

      // 获取文件名
      let filename = row.filename;
      const contentDisposition = response.headers.get('content-disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1];
        }
      }

      // 获取blob
      const blob = await response.blob();

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;

      // 触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
      }, 100);

      loadingMsg?.destroy();
      message.success('文件下载已开始');
    } catch (e) {
      // 如果fetch方法失败，尝试方法2
      console.error('Fetch下载失败，尝试直接打开方式:', e);

      // 方法2：直接在新窗口中打开下载链接
      window.open(downloadUrl, '_blank');

      loadingMsg?.destroy();
      message.success('已在新窗口打开下载链接');
    }
  } catch (error: any) {
    // 关闭加载提示
    loadingMsg?.destroy();
    message.error(`下载文件失败: ${error.message || '未知错误'}`);
  }
};

// 解析文件操作
const parseFile = async (row: StrmAPI.UploadRecord) => {
  try {
    // 对于已上传但未解析的文件进行解析
    loading.value = true;
    await parseDirectoryTree({
      record_id: row.id
    });
    message.success('文件解析成功');
    fetchHistory(); // 刷新数据
  } catch (error: any) {
    message.error(`解析失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 加载解析结果 - 根据视图模式决定是否请求全部数据
const loadParseResult = async () => {
  if (!currentRecord.value) return;

  try {
    resultLoading.value = true;

    if (viewMode.value === 'tree') {
      // 树形视图模式 - 只加载根目录内容
      // console.log('加载树形视图数据', {
      //   recordId: currentRecord.value.id,
      //   fileType: fileTypeFilter.value
      // });

      const { data } = await getDirectoryContent(currentRecord.value.id, {
        directoryPath: '/',
        fileType: fileTypeFilter.value
      });

      // console.log('收到目录数据', data);

      if (data) {
        // 确保subdirectories是数组
        if (!Array.isArray(data.subdirectories)) {
          console.warn('后端返回的subdirectories不是数组', data.subdirectories);
          rootDirectories.value = [];
        } else {
          rootDirectories.value = data.subdirectories || [];
        }

        // 确保files是数组
        if (!Array.isArray(data.files)) {
          console.warn('后端返回的files不是数组', data.files);
          rootFiles.value = [];
        } else {
          rootFiles.value = data.files || [];
        }

        // console.log('设置树形视图数据', {
        //   rootDirectories: rootDirectories.value,
        //   rootFiles: rootFiles.value
        // });

        // 更新stats等信息到parseResult，以便显示在界面上
        if (!parseResult.value) {
          parseResult.value = {
            file_name: currentRecord.value.filename,
            parsed_files: [],
            total_files: 0,
            stats: {
              total: 0,
              video: 0,
              audio: 0,
              image: 0,
              subtitle: 0,
              metadata: 0,
              other: 0
            }
          };
        }
      } else {
        console.warn('获取目录内容返回空数据');
        rootDirectories.value = [];
        rootFiles.value = [];
      }
    } else {
      // 表格视图模式 - 使用分页加载
      const { data } = await getParseResult(currentRecord.value.id, {
        fileType: fileTypeFilter.value,
        page: filePagination.page,
        pageSize: filePagination.pageSize
      });

      if (data) {
        parseResult.value = data;
        parsedFiles.value = data.parsed_files || [];

        // 使用后端返回的分页信息更新分页控件
        if (data.pagination) {
          filePagination.page = data.pagination.page;
          filePagination.pageSize = data.pagination.page_size;
          filePagination.itemCount = data.pagination.total;
        }
      }
    }
  } catch (error: any) {
    message.error(`获取解析结果失败: ${error.message || '未知错误'}`);
  } finally {
    resultLoading.value = false;
  }
};

// 查看解析结果
const viewParseResult = async (row: StrmAPI.UploadRecord) => {
  if (row.status !== 'parsed') {
    message.warning('该文件尚未解析，无法查看结果');
    return;
  }

  try {
    resultLoading.value = true;

    // 先保存当前记录，方便loadParseResult使用
    currentRecord.value = row;

    // 重置分页和过滤器
    filePagination.page = 1;
    fileTypeFilter.value = 'all'; // 重置文件类型过滤

    // 清空旧数据
    rootDirectories.value = [];
    rootFiles.value = [];

    // 显示对话框 - 将其移到加载数据之后，确保数据加载完成后再显示UI
    await loadParseResult();

    // 数据加载完成后再显示对话框
    showResultModal.value = true;

    // console.log('模态框已显示，树形数据状态', {
    //   rootDirectories: rootDirectories.value,
    //   rootFiles: rootFiles.value,
    //   recordId: currentRecord.value?.id
    // });
  } catch (error: any) {
    message.error(`获取解析结果失败: ${error.message || '未知错误'}`);
    showResultModal.value = false;
  } finally {
    resultLoading.value = false;
  }
};

// 视图模式变化处理
const handleViewModeChange = (mode: 'table' | 'tree') => {
  // 记录旧模式
  // const oldMode = viewMode.value;
  // 设置新模式
  viewMode.value = mode;

  // console.log('视图模式变更', { oldMode, newMode: mode });

  // 切换视图模式，重新加载数据
  loadParseResult();
};

// 文件类型变化处理
const handleFileTypeChange = () => {
  // 当文件类型过滤器变化时，重置为第一页并重新加载数据
  filePagination.page = 1;
  loadParseResult();
};

// 文件分页处理函数 - 重新请求数据
const handleFilePageChange = (page: number) => {
  filePagination.page = page;
  loadParseResult();
};

// 文件分页大小变化处理函数
const handleFilePageSizeChange = (pageSize: number) => {
  filePagination.pageSize = pageSize;
  filePagination.page = 1; // 重置到第一页
  loadParseResult();
};

// 引用树形视图组件
const fileTreeViewRef = ref<InstanceType<typeof FileTreeView> | null>(null);

// 搜索按钮点击处理函数
const handleSearch = () => {
  if (viewMode.value === 'tree') {
    // 树形视图模式下执行搜索
    if (fileTreeViewRef.value) {
      fileTreeViewRef.value.performSearch(fileSearchValue.value);
    }
  } else {
    // 表格视图模式下的搜索逻辑
    if (!fileSearchValue.value.trim()) {
      // 如果搜索框为空，重新加载所有数据
      loadParseResult();
      return;
    }

    // 显示加载状态
    resultLoading.value = true;

    // 调用后端API进行搜索
    searchFiles(currentRecord.value?.id || '', fileSearchValue.value.trim(), {
      fileType: fileTypeFilter.value,
      ignoreCase: true
    })
      .then(({ data }) => {
        if (data && data.matches) {
          // 更新表格数据为搜索结果
          parsedFiles.value = data.matches;
          // 更新分页信息
          filePagination.itemCount = data.total_matches;
          filePagination.page = 1;

          message.success(`找到 ${data.total_matches} 个匹配项`);
        } else {
          parsedFiles.value = [];
          filePagination.itemCount = 0;
          message.info('未找到匹配项');
        }
      })
      .catch(error => {
        message.error(`搜索失败: ${error.message || '未知错误'}`);
      })
      .finally(() => {
        resultLoading.value = false;
      });
  }
};

// 定义处理函数引用
const handleDelete = (row: StrmAPI.UploadRecord) => deleteRecord(row);
const handleDownload = (row: StrmAPI.UploadRecord) => downloadFile(row);
const handleParse = (row: StrmAPI.UploadRecord) => parseFile(row);
const handleViewResult = (row: StrmAPI.UploadRecord) => viewParseResult(row);

// 表格列定义
const columns: DataTableColumns<StrmAPI.UploadRecord> = [
  { title: 'ID', key: 'id', width: 80 },
  { title: '文件名', key: 'filename', ellipsis: { tooltip: true } },
  {
    title: '文件大小',
    key: 'filesize',
    render(row) {
      return `${(row.filesize / 1024 / 1024).toFixed(2)} MB`;
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row) {
      const statusMap: Record<string, string> = {
        'uploaded': '已上传',
        'parsing': '解析中',
        'parsed': '已解析',
        'failed': '解析失败'
      };
      return statusMap[row.status] || row.status;
    }
  },
  {
    title: '上传时间',
    key: 'create_time',
    render(row) {
      return new Date(row.create_time).toLocaleString();
    }
  },
  {
    title: '解析时间',
    key: 'parse_time',
    render(row) {
      return row.parse_time ? new Date(row.parse_time).toLocaleString() : '-';
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h(NSpace, { justify: 'center' }, {
        default: () => [
          // 下载按钮
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleDownload(row)
          }, { default: () => '下载' }),

          // 查看结果按钮 - 仅显示给已解析的文件
          row.status === 'parsed' ?
            h(NButton, {
              size: 'small',
              type: 'info',
              onClick: () => handleViewResult(row)
            }, { default: () => '查看结果' }) : null,

          // 解析按钮 - 仅显示在未解析的文件
          row.status === 'uploaded' || row.status === 'failed' ?
            h(NButton, {
              size: 'small',
              type: 'info',
              onClick: () => handleParse(row)
            }, { default: () => '解析' }) : null,

          // 删除按钮 - 使用确认对话框
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row)
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error'
            }, { default: () => '删除' }),
            default: () => '确定要删除此记录吗？'
          })
        ]
      });
    }
  }
];

// 处理历史记录搜索
const handleHistorySearch = () => {
  const searchText = historySearchValue.value.trim().toLowerCase();

  // 复制原始数据进行筛选
  let filteredData = [...originalHistoryData.value];

  // 按文件名筛选
  if (searchText) {
    filteredData = filteredData.filter(record =>
      record.filename.toLowerCase().includes(searchText)
    );
  }

  // 按日期范围筛选
  if (dateRange.value && dateRange.value[0] && dateRange.value[1]) {
    const [startTime, endTime] = dateRange.value;
    filteredData = filteredData.filter(record => {
      const recordTime = new Date(record.create_time).getTime();
      return recordTime >= startTime && recordTime <= endTime;
    });
  }

  // 更新显示的数据
  historyData.value = filteredData;

  // 显示搜索结果数量
  message.info(`找到 ${filteredData.length} 条匹配记录`);
};

// 导航到上传页面
const goToUpload = () => {
  router.push('/strm/upload');
};

onMounted(() => {
  fetchHistory();
});
</script>

<style scoped>
.file-content-area {
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

/* 自定义对话框样式，确保它有足够的高度 */
:deep(.n-modal.n-modal--card-huge) {
  max-width: 80vw !important;
}

:deep(.n-modal-body) {
  padding: 16px;
  max-height: 80vh;
  overflow: auto;
}
</style>
