<script setup lang="ts">
import { h, onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { getSystemSettings, updateSystemSettings, getMediaServers } from '@/service/api/strm';
import { $t } from '@/locales';
import ServerManagement from '@/components/custom/server-management.vue';

defineOptions({ name: 'StrmSystemSettings' });

const message = useMessage();

// 页面状态
const loading = ref(false);
const submitting = ref(false);
const activeTab = ref('basic'); // 'basic' 或 'servers'

// 数据模型
const settings = ref({
    defaultServerId: null as number | null,
    defaultDownloadServerId: null as number | null,
    defaultMediaServerId: null as number | null,
    enablePathReplacement: true,
    outputDirectory: ''
});

// 服务器列表
const allServers = ref<Array<{ id: number; name: string; serverType: string }>>([]);
const downloadServers = ref<Array<{ label: string; value: number }>>([]);
const mediaServers = ref<Array<{ label: string; value: number }>>([]);

// 获取系统设置
async function fetchSettings() {
    try {
        loading.value = true;
        const res = await getSystemSettings();
        if (res.data) {
            settings.value = {
                defaultServerId: res.data.defaultServerId ?? null,
                defaultDownloadServerId: res.data.defaultDownloadServerId ?? null,
                defaultMediaServerId: res.data.defaultMediaServerId ?? null,
                enablePathReplacement: res.data.enablePathReplacement ?? true,
                outputDirectory: res.data.outputDirectory ?? ''
            };
        }
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        loading.value = false;
    }
}

// 获取服务器列表
async function fetchServers() {
    try {
        const res = await getMediaServers();
        if (res.data && Array.isArray(res.data)) {
            allServers.value = res.data.map(server => ({
                id: server.id,
                name: server.name,
                serverType: server.server_type
            }));

            // 筛选下载服务器和媒体服务器
            downloadServers.value = allServers.value
                .filter(server => server.serverType === 'cd2host')
                .map(server => ({ label: server.name, value: server.id }));

            mediaServers.value = allServers.value
                .filter(server => server.serverType === 'xiaoyahost')
                .map(server => ({ label: server.name, value: server.id }));
        }
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    }
}

// 保存设置
async function saveSettings() {
    try {
        submitting.value = true;
        await updateSystemSettings({
            default_server_id: settings.value.defaultServerId,
            default_download_server_id: settings.value.defaultDownloadServerId,
            default_media_server_id: settings.value.defaultMediaServerId,
            enable_path_replacement: settings.value.enablePathReplacement,
            output_directory: settings.value.outputDirectory
        });
        message.success($t('strm.settings.saveSuccess'));
    } catch (error: any) {
        message.error(error.message || $t('strm.settings.saveFail'));
    } finally {
        submitting.value = false;
    }
}

// 切换标签页
function handleTabChange(name: string) {
    activeTab.value = name;
}

// 服务器列表发生变化时更新
function handleServersUpdate() {
    fetchServers();
}

// 初始化
onMounted(async () => {
    await Promise.all([fetchSettings(), fetchServers()]);
});
</script>

<template>
    <n-card :title="$t('strm.settings.title')" :bordered="false">
        <!-- 标签页选择 -->
        <n-tabs v-model:value="activeTab" type="line" @update:value="handleTabChange">
            <n-tab-pane name="basic" :tab="$t('strm.settings.basicSettings')">
                <n-spin :show="loading">
                    <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging"
                        :disabled="submitting" class="pt-20px">

                        <!-- 默认下载服务器 -->
                        <n-form-item :label="$t('strm.settings.defaultDownloadServer')">
                            <n-select v-model:value="settings.defaultDownloadServerId"
                                :options="downloadServers"
                                :placeholder="$t('strm.settings.defaultDownloadServerPlaceholder')" clearable />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultDownloadServerHelp') }}</span>
                            </template>
                        </n-form-item>

                        <!-- 默认媒体服务器 -->
                        <n-form-item :label="$t('strm.settings.defaultMediaServer')">
                            <n-select v-model:value="settings.defaultMediaServerId"
                                :options="mediaServers"
                                :placeholder="$t('strm.settings.defaultMediaServerPlaceholder')" clearable />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.defaultMediaServerHelp') }}</span>
                            </template>
                        </n-form-item>

                        <n-form-item :label="$t('strm.settings.enablePathReplacement')">
                            <n-switch v-model:value="settings.enablePathReplacement" />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.enablePathReplacementHelp') }}</span>
                            </template>
                        </n-form-item>

                        <n-form-item :label="$t('strm.settings.outputDirectory')">
                            <n-input v-model:value="settings.outputDirectory"
                                :placeholder="$t('strm.settings.outputDirectoryPlaceholder')" />
                            <template #help>
                                <span class="text-xs text-gray-400">{{ $t('strm.settings.outputDirectoryHelp') }}</span>
                            </template>
                        </n-form-item>

                        <n-divider />

                        <div class="flex justify-center">
                            <n-button type="primary" :loading="submitting" @click="saveSettings">
                                {{ $t('strm.settings.saveSettings') }}
                            </n-button>
                        </div>
                    </n-form>
                </n-spin>
            </n-tab-pane>
            <n-tab-pane name="servers" :tab="$t('strm.settings.serverManagement')">
                <server-management @update:servers="handleServersUpdate" />
            </n-tab-pane>
        </n-tabs>
    </n-card>
</template>

<style scoped>
.pt-20px {
    padding-top: 20px;
}
</style>
