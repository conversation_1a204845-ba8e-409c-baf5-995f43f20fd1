<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { createStrmTask, getStrmTasks, uploadTreeFile } from '@/service/api';
import { useThemeStore } from '@/store/modules/theme';
import { useLoadingEmpty } from '@/hooks/common/loading';

const { loading, startLoading, endLoading } = useLoadingEmpty();
const fileLoading = ref(false);
const themeStore = useThemeStore();

interface TaskInfo {
  id: number;
  name: string;
  server_id: number;
  output_dir: string;
}

// 当前选中的任务
const currentTask = ref<TaskInfo | null>(null);
// 任务列表
const tasks = ref<StrmAPI.StrmTaskResponse[]>([]);
// 上传结果
const uploadResult = ref<StrmAPI.UploadResult | null>(null);
// 选择任务对话框
const taskDrawerVisible = ref(false);
// 创建任务对话框
const createTaskModalVisible = ref(false);

// 新任务表单
const newTaskForm = reactive({
  name: '',
  server_id: 1, // 默认服务器ID
  output_dir: ''
});

// 文件上传区域相关
const dragOver = ref(false);
const uploadRef = ref<HTMLInputElement | null>(null);
const fileList = ref<File[]>([]);

// 获取任务列表
async function fetchTasks() {
  startLoading();
  try {
    const res = await getStrmTasks();
    tasks.value = res.data || [];
  } catch (error) {
    window.$message?.error('获取任务列表失败');
    console.error(error);
  } finally {
    endLoading();
  }
}

// 选择任务
function selectTask(task: StrmAPI.StrmTaskResponse) {
  currentTask.value = {
    id: task.id,
    name: task.name,
    server_id: task.server_id,
    output_dir: task.output_dir
  };
  taskDrawerVisible.value = false;
}

// 创建新任务
async function createTask() {
  try {
    const res = await createStrmTask(newTaskForm);
    if (res.data) {
      window.$message?.success('创建任务成功');
      tasks.value.unshift(res.data);
      currentTask.value = {
        id: res.data.id,
        name: res.data.name,
        server_id: res.data.server_id,
        output_dir: res.data.output_dir
      };
      createTaskModalVisible.value = false;
    }
  } catch (error) {
    window.$message?.error('创建任务失败');
    console.error(error);
  }
}

// 处理文件拖拽
function handleDragOver(e: DragEvent) {
  e.preventDefault();
  dragOver.value = true;
}

function handleDragLeave(e: DragEvent) {
  e.preventDefault();
  dragOver.value = false;
}

function handleDrop(e: DragEvent) {
  e.preventDefault();
  dragOver.value = false;

  if (!e.dataTransfer?.files.length) return;

  // 获取拖拽的文件
  const files = Array.from(e.dataTransfer.files);
  processFiles(files);
}

// 处理文件选择
function handleFileSelect(e: Event) {
  const target = e.target as HTMLInputElement;
  if (!target.files?.length) return;

  const files = Array.from(target.files);
  processFiles(files);
}

// 处理文件
function processFiles(files: File[]) {
  // 过滤非txt文件
  const txtFiles = files.filter(file => file.name.endsWith('.txt'));

  if (txtFiles.length === 0) {
    window.$message?.error('只支持上传.txt格式的文件');
    return;
  }

  fileList.value = txtFiles;

  // 如果只有一个文件且有当前任务，直接上传
  if (txtFiles.length === 1 && currentTask.value) {
    uploadFile(txtFiles[0]);
  }
}

// 上传文件
async function uploadFile(file: File) {
  if (!currentTask.value) {
    window.$message?.error('请先选择或创建任务');
    return;
  }

  fileLoading.value = true;
  uploadResult.value = null;

  try {
    const res = await uploadTreeFile(currentTask.value.id, file);
    if (res.data.success) {
      window.$message?.success('文件上传并解析成功');
      uploadResult.value = res.data;
    } else {
      window.$message?.error(`文件解析失败: ${res.data.error || '未知错误'}`);
    }
  } catch (error: any) {
    window.$message?.error(`上传失败: ${error.message || '未知错误'}`);
    console.error(error);
  } finally {
    fileLoading.value = false;
  }
}

// 点击上传区域触发文件选择
function triggerFileSelect() {
  uploadRef.value?.click();
}

onMounted(() => {
  fetchTasks();
});
</script>

<template>
  <div class="h-full">
    <NCard title="文件上传" :bordered="false" class="h-full upload-card">
      <NSpin :show="loading">
        <template #description>加载中...</template>

        <div class="flex flex-col gap-16px">
          <!-- 任务选择 -->
          <div class="flex justify-between items-center">
            <div class="flex items-center gap-8px">
              <span class="text-primary font-bold">当前任务:</span>
              <span v-if="currentTask">{{ currentTask.name }}</span>
              <NEmpty v-else description="暂无任务" class="inline-flex" />
            </div>

            <div class="flex gap-8px">
              <NButton type="primary" @click="taskDrawerVisible = true">选择任务</NButton>
              <NButton @click="createTaskModalVisible = true">创建任务</NButton>
            </div>
          </div>

          <!-- 文件上传区域 -->
          <div
            class="upload-area flex-center flex-col border-dashed border-2 border-gray-300 rounded-8px p-32px cursor-pointer transition-all duration-300"
            :class="[
              dragOver ? 'border-primary bg-primary/5' : '',
              themeStore.darkMode ? 'hover:border-primary/70 hover:bg-black/30' : 'hover:border-primary/70 hover:bg-gray-100'
            ]"
            @dragover="handleDragOver"
            @dragleave="handleDragLeave"
            @drop="handleDrop"
            @click="triggerFileSelect"
          >
            <input
              ref="uploadRef"
              type="file"
              accept=".txt"
              class="hidden"
              @change="handleFileSelect"
            >

            <NSpin :show="fileLoading">
              <div class="flex-center flex-col gap-16px">
                <div class="text-48px text-primary">
                  <SvgIcon icon="mdi:cloud-upload-outline" />
                </div>
                <div class="text-16px">
                  <span v-if="fileList.length">已选择 {{ fileList.length }} 个文件</span>
                  <span v-else>将文件拖到此处，或<NButton text type="primary">点击上传</NButton></span>
                </div>
                <div class="text-gray-400 text-14px">
                  只支持上传115导出的目录树文件(.txt格式)
                </div>
                <NButton
                  v-if="fileList.length && currentTask"
                  type="primary"
                  :disabled="fileLoading"
                  @click.stop="uploadFile(fileList[0])"
                >
                  上传文件
                </NButton>
              </div>
            </NSpin>
          </div>

          <!-- 上传结果 -->
          <div v-if="uploadResult" class="mt-16px">
            <NCard title="解析结果" size="small">
              <div class="flex gap-16px">
                <NStatistic
                  v-for="(value, key) in uploadResult.stats"
                  :key="key"
                  :label="key === 'total' ? '总文件数' : key === 'video' ? '视频文件' : key === 'audio' ? '音频文件' : key === 'image' ? '图片文件' : key === 'subtitle' ? '字幕文件' : '其他文件'"
                  :value="value"
                  :class="key === 'video' ? 'text-success' : ''"
                />
              </div>

              <div class="mt-16px">
                <NButton type="primary" :to="`/strm/tasks?id=${currentTask?.id}`">
                  查看详情并处理
                </NButton>
              </div>
            </NCard>
          </div>
        </div>
      </NSpin>
    </NCard>

    <!-- 选择任务抽屉 -->
    <NDrawer v-model:show="taskDrawerVisible" width="500" placement="right">
      <NDrawerContent title="选择任务">
        <NSpin :show="loading">
          <NList v-if="tasks.length">
            <NListItem v-for="task in tasks" :key="task.id">
              <div class="flex justify-between items-center w-full cursor-pointer p-8px hover:bg-gray-100 dark:hover:bg-gray-800 rounded-4px" @click="selectTask(task)">
                <div>
                  <div class="font-bold">{{ task.name }}</div>
                  <div class="text-gray-500 text-12px">输出目录: {{ task.output_dir }}</div>
                </div>
                <NButton type="primary" size="small" @click.stop="selectTask(task)">
                  选择
                </NButton>
              </div>
            </NListItem>
          </NList>
          <NEmpty v-else description="暂无任务" />
        </NSpin>
      </NDrawerContent>
    </NDrawer>

    <!-- 创建任务对话框 -->
    <NModal v-model:show="createTaskModalVisible" preset="dialog" title="创建任务">
      <NForm :model="newTaskForm" label-placement="left" label-width="auto">
        <NFormItem label="任务名称" path="name">
          <NInput v-model:value="newTaskForm.name" placeholder="请输入任务名称" />
        </NFormItem>
        <NFormItem label="服务器" path="server_id">
          <NSelect v-model:value="newTaskForm.server_id" :options="[{ label: '默认服务器', value: 1 }]" />
        </NFormItem>
        <NFormItem label="输出目录" path="output_dir">
          <NInput v-model:value="newTaskForm.output_dir" placeholder="请输入输出目录" />
        </NFormItem>
      </NForm>

      <template #action>
        <NButton @click="createTaskModalVisible = false">取消</NButton>
        <NButton type="primary" @click="createTask">创建</NButton>
      </template>
    </NModal>
  </div>
</template>

<style scoped>
.upload-area {
  min-height: 240px;
}

.upload-card {
  display: flex;
  flex-direction: column;
}

.upload-card :deep(.n-card__content) {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
