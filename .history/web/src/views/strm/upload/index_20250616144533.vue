<template>
  <div>
    <n-card title="115 目录树文件上传" :bordered="false" class="h-full rounded-8px shadow-sm">
      <n-upload multiple directory-dnd :max="5" accept=".txt" :custom-request="customRequest"
        @before-upload="handleBeforeUpload">
        <n-upload-dragger>
          <div class="flex-col-center">
            <icon-mdi-upload class="text-68px text-primary" />
            <p class="mt-12px text-16px text-primary">点击或拖拽文件到这里上传</p>
            <p class="mt-8px text-12px text-secondary-text">
              请上传从115网盘导出的 .txt 格式的目录树文件。
            </p>
          </div>
        </n-upload-dragger>
      </n-upload>

      <!-- 解析结果展示 -->
      <div v-if="uploadedFiles.length > 0" class="mt-24px">
        <n-divider />
        <h3 class="mb-12px">已上传文件</h3>
        <n-space vertical>
          <n-card v-for="file in uploadedFiles" :key="file.path" size="small" class="mb-12px">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-16px font-medium">{{ file.filename }}</div>
                <div class="text-12px text-secondary-text">{{ file.path }}</div>
              </div>
              <div>
                <n-button :loading="file.parsing" :disabled="file.parsed" type="primary" @click="handleParseFile(file)">
                  {{ file.parsed ? '已解析' : '解析文件' }}
                </n-button>
              </div>
            </div>

            <!-- 解析结果 -->
            <div v-if="file.parseResult" class="mt-16px">
              <n-divider />
              <h4 class="mb-8px">文件统计</h4>
              <n-grid :cols="5" :x-gap="8">
                <n-grid-item>
                  <n-statistic label="总文件" :value="file.parseResult.stats.total" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="视频" :value="file.parseResult.stats.video" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="音频" :value="file.parseResult.stats.audio" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="图片" :value="file.parseResult.stats.image" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="字幕" :value="file.parseResult.stats.subtitle" />
                </n-grid-item>
              </n-grid>

              <div class="mt-16px">
                <h4 class="mb-8px">文件列表 (仅显示前100个)</h4>

                <!-- 文件类型过滤 -->
                <div class="mb-12px">
                  <n-radio-group v-model:value="fileTypeFilter" size="small">
                    <n-radio-button value="all">全部</n-radio-button>
                    <n-radio-button value="video">视频</n-radio-button>
                    <n-radio-button value="audio">音频</n-radio-button>
                    <n-radio-button value="image">图片</n-radio-button>
                    <n-radio-button value="subtitle">字幕</n-radio-button>
                  </n-radio-group>
                </div>

                <n-data-table :columns="columns" :data="filteredFiles(file.parseResult.parsed_files)"
                  :pagination="pagination" :row-key="row => row.path" />
              </div>
            </div>
          </n-card>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import type { UploadCustomRequestOptions, UploadFileInfo, DataTableColumns } from 'naive-ui';
import { useMessage } from 'naive-ui';
import { uploadDirectoryTree, parseDirectoryTree } from '@/service/api/strm';

defineOptions({
  name: 'StrmUpload'
});

const message = useMessage();

// 上传文件列表
const uploadedFiles = ref<Array<{
  filename: string;
  path: string;
  parsing: boolean;
  parsed: boolean;
  parseResult?: StrmAPI.ParseResult;
}>>([]);

// 文件类型过滤
const fileTypeFilter = ref('all');

// 数据表格配置
const columns: DataTableColumns<StrmAPI.ParsedFile> = [
  {
    title: '文件名',
    key: 'file_name',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '类型',
    key: 'file_type',
    width: 80,
    render(row) {
      const typeMap = {
        video: '视频',
        audio: '音频',
        image: '图片',
        subtitle: '字幕',
        other: '其他'
      };
      return typeMap[row.file_type as keyof typeof typeMap] || row.file_type;
    }
  },
  {
    title: '扩展名',
    key: 'extension',
    width: 80
  },
  {
    title: '目录',
    key: 'directory',
    ellipsis: {
      tooltip: true
    }
  }
];

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  }
});

// 根据文件类型过滤文件列表
const filteredFiles = (files: StrmAPI.ParsedFile[] | undefined) => {
  if (!files) return [];
  if (fileTypeFilter.value === 'all') return files;
  return files.filter(file => file.file_type === fileTypeFilter.value);
};

const handleBeforeUpload = async (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }): Promise<boolean> => {
  if (!data.file.file?.type.includes('text/plain')) {
    message.error('只能上传 .txt 格式的文件，请重新上传');
    return false;
  }
  if (data.file.file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过 10MB，请重新上传');
    return false;
  }
  return true;
};

const customRequest = ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  uploadDirectoryTree(file.file as File)
    .then(response => {
      // 处理可能的类型问题，确保安全访问
      if (response?.data && typeof response.data === 'object') {
        const uploadResult = response.data as StrmAPI.UploadResult;
        message.success(`'${file.name}' 上传成功, 已保存到: ${uploadResult.path}`);

        // 添加到已上传文件列表
        uploadedFiles.value.push({
          filename: uploadResult.filename,
          path: uploadResult.path,
          parsing: false,
          parsed: false
        });

        onFinish();
      } else {
        message.error(`'${file.name}' 上传失败: 服务器响应格式不正确`);
        onError();
      }
    })
    .catch(error => {
      message.error(`'${file.name}' 上传失败: ${error.message || '未知错误'}`);
      onError();
    });
};

// 解析文件
const handleParseFile = (file: typeof uploadedFiles.value[0]) => {
  // 设置解析状态
  file.parsing = true;

  parseDirectoryTree(file.path)
    .then(response => {
      if (response?.data) {
        file.parseResult = response.data;
        file.parsed = true;
        message.success(`'${file.filename}' 解析成功, 共${file.parseResult.total_files}个文件`);
      } else {
        message.error(`'${file.filename}' 解析失败: 服务器响应格式不正确`);
      }
    })
    .catch(error => {
      message.error(`'${file.filename}' 解析失败: ${error.message || '未知错误'}`);
    })
    .finally(() => {
      file.parsing = false;
    });
};
</script>

<style scoped></style>
