<template>
  <div>
    <n-card title="115 目录树文件上传" :bordered="false" class="h-full rounded-8px shadow-sm">
      <n-upload multiple directory-dnd :max="5" accept=".txt" :custom-request="customRequest"
        @before-upload="handleBeforeUpload">
        <n-upload-dragger>
          <div class="flex-col-center">
            <icon-mdi-upload class="text-68px text-primary" />
            <p class="mt-12px text-16px text-primary">点击或拖拽文件到这里上传</p>
            <p class="mt-8px text-12px text-secondary-text">
              请上传从115网盘导出的 .txt 格式的目录树文件。
            </p>
          </div>
        </n-upload-dragger>
      </n-upload>

      <!-- URL上传区域 -->
      <div class="mt-16px">
        <n-divider>或者通过URL上传</n-divider>
        <n-input-group>
          <n-input v-model:value="uploadUrl" placeholder="输入115目录树文件URL" />
          <n-button type="primary" :disabled="!uploadUrl.trim() || urlUploading" :loading="urlUploading"
            @click="handleUrlUpload">
            上传
          </n-button>
        </n-input-group>
        <p class="mt-4px text-12px text-secondary-text">
          请输入指向115网盘导出的 .txt 格式目录树文件的URL地址。
        </p>

        <!-- URL上传进度条 -->
        <div v-if="urlUploading && uploadProgress > 0" class="mt-10px">
          <n-progress
            type="line"
            :percentage="uploadProgress"
            :height="12"
            :border-radius="4"
            :processing="uploadProgress < 100"
            :indicator-text-color="uploadProgress === 100 ? '#18a058' : undefined"
            :status="uploadProgress === 100 ? 'success' : 'default'"
          >
            <template #default="{ percentage }">
              {{ uploadStatus }} {{ percentage }}%
            </template>
          </n-progress>
        </div>
      </div>

      <!-- 解析结果展示 -->
      <div v-if="uploadedFiles.length > 0" class="mt-24px">
        <n-divider />
        <h3 class="mb-12px">已上传文件</h3>
        <n-space vertical>
          <n-card v-for="(file, index) in uploadedFiles" :key="file.path" size="small" class="mb-12px">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-16px font-medium">{{ file.filename }}</div>
                <div class="text-12px text-secondary-text">{{ file.path }}</div>
              </div>
              <div>
                <n-button :loading="file.parsing" :disabled="file.parsed" type="primary"
                  @click="handleParseFile(file, index)">
                  {{ file.parsed ? '已解析' : '解析文件' }}
                </n-button>
              </div>
            </div>

            <!-- 解析结果 -->
            <div v-if="file.parseResult && currentFileIndex === index" class="mt-16px">
              <n-divider />
              <h4 class="mb-8px">文件统计</h4>
              <n-grid :cols="7" :x-gap="8">
                <n-grid-item>
                  <n-statistic label="总文件" :value="file.parseResult.stats.total" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="视频" :value="file.parseResult.stats.video" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="音频" :value="file.parseResult.stats.audio" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="图片" :value="file.parseResult.stats.image" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="字幕" :value="file.parseResult.stats.subtitle" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="元数据" :value="file.parseResult.stats.metadata" />
                </n-grid-item>
                <n-grid-item>
                  <n-statistic label="其它" :value="file.parseResult.stats.other" />
                </n-grid-item>
              </n-grid>

              <div class="mt-16px">
                <h4 class="mb-8px">文件列表</h4>

                <!-- 文件类型过滤 -->
                <div class="mb-12px">
                  <n-radio-group v-model:value="fileTypeFilter" size="small" @update:value="handleFileTypeChange">
                    <n-radio-button value="all">全部 ({{ file.parseResult.stats.total || 0 }})</n-radio-button>
                    <n-radio-button value="video">视频 ({{ file.parseResult.stats.video || 0 }})</n-radio-button>
                    <n-radio-button value="audio">音频 ({{ file.parseResult.stats.audio || 0 }})</n-radio-button>
                    <n-radio-button value="image">图片 ({{ file.parseResult.stats.image || 0 }})</n-radio-button>
                    <n-radio-button value="subtitle">字幕 ({{ file.parseResult.stats.subtitle || 0 }})</n-radio-button>
                    <n-radio-button value="metadata">元数据 ({{ file.parseResult.stats.metadata || 0 }})</n-radio-button>
                    <n-radio-button value="other">其它 ({{ file.parseResult.stats.other || 0 }})</n-radio-button>
                  </n-radio-group>
                  <div v-if="file.parseResult.pagination" class="text-xs text-gray-500 mt-1">
                    当前显示: {{ parsedFiles.length }}条，总计: {{ file.parseResult.pagination.total }}条
                  </div>
                </div>

                <n-data-table :columns="columns" :data="parsedFiles" :pagination="filePagination"
                  :loading="resultLoading" remote :row-key="row => row.file_name + row.path"
                  @update:page="handleFilePageChange" @update:page-size="handleFilePageSizeChange" />
              </div>
            </div>
          </n-card>
        </n-space>
      </div>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useMessage } from 'naive-ui';
import type {
  DataTableColumns,
  PaginationProps,
  UploadCustomRequestOptions,
  UploadFileInfo
} from 'naive-ui';
import {
  getParseResult,
  parseDirectoryTree,
  uploadDirectoryTree,
  uploadDirectoryTreeFromUrl
} from '@/service/api/strm';

defineOptions({
  name: 'StrmUpload'
});

const message = useMessage();

// URL上传相关
const uploadUrl = ref('');
const urlUploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref('下载中');

// 上传文件列表
const uploadedFiles = ref<Array<{
  filename: string;
  path: string;
  recordId: string | number;
  parsing: boolean;
  parsed: boolean;
  parseResult?: StrmAPI.ParseResult;
}>>([]);

// 文件类型过滤
const fileTypeFilter = ref('all');

// 当前查看的文件索引
const currentFileIndex = ref(-1);

// 加载状态
const resultLoading = ref(false);

// 解析后的文件数据（当前显示）
const parsedFiles = ref<StrmAPI.ParsedFile[]>([]);

// 数据表格配置
const columns: DataTableColumns<StrmAPI.ParsedFile> = [
  {
    title: '文件名',
    key: 'file_name',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '类型',
    key: 'file_type',
    width: 80,
    render(row) {
      const typeMap = {
        video: '视频',
        audio: '音频',
        image: '图片',
        subtitle: '字幕',
        metadata: '元数据',
        other: '其他'
      };
      return typeMap[row.file_type as keyof typeof typeMap] || row.file_type;
    }
  },
  {
    title: '扩展名',
    key: 'extension',
    width: 80
  },
  {
    title: '目录',
    key: 'directory',
    ellipsis: {
      tooltip: true
    }
  }
];

// 文件列表分页配置 - 使用服务端分页
const filePagination = reactive<PaginationProps>({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  itemCount: 0,
  prefix: ({ itemCount }) => `共 ${itemCount} 条`,
  showQuickJumper: true,
});

const handleBeforeUpload = async (data: { file: UploadFileInfo; fileList: UploadFileInfo[] }): Promise<boolean> => {
  if (!data.file.file?.type.includes('text/plain')) {
    message.error('只能上传 .txt 格式的文件，请重新上传');
    return false;
  }
  if (data.file.file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过 10MB，请重新上传');
    return false;
  }
  return true;
};

const customRequest = ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  uploadDirectoryTree(file.file as File)
    .then(response => {
      // 处理可能的类型问题，确保安全访问
      if (response?.data && typeof response.data === 'object') {
        const uploadResult = response.data as StrmAPI.UploadResult;
        message.success(`'${file.name}' 上传成功, 已保存到: ${uploadResult.path}`);

        // 添加到已上传文件列表
        uploadedFiles.value.push({
          filename: uploadResult.filename,
          path: uploadResult.path,
          recordId: uploadResult.record_id,
          parsing: false,
          parsed: false
        });

        onFinish();
      } else {
        message.error(`'${file.name}' 上传失败: 服务器响应格式不正确`);
        onError();
      }
    })
    .catch(error => {
      message.error(`'${file.name}' 上传失败: ${error.message || '未知错误'}`);
      onError();
    });
};

// 加载解析结果 - 使用后端API进行文件类型过滤和分页
const loadParseResult = async (file: typeof uploadedFiles.value[0]) => {
  if (!file) return;

  resultLoading.value = true;
  try {
    // 使用修改后的API，传递文件类型和分页参数
    const { data } = await getParseResult(file.recordId, {
      fileType: fileTypeFilter.value,
      page: filePagination.page,
      pageSize: filePagination.pageSize
    });

    if (data) {
      file.parseResult = data;
      parsedFiles.value = data.parsed_files || [];

      // 使用后端返回的分页信息更新分页控件
      if (data.pagination) {
        filePagination.itemCount = data.pagination.total;
      } else {
        // 兼容旧版API响应，使用stats中的数据
        filePagination.itemCount = fileTypeFilter.value === 'all'
          ? (data.stats?.total || 0)
          : (data.stats?.[fileTypeFilter.value as keyof typeof data.stats] || 0);
      }
    }
  } catch (error: any) {
    message.error(`加载解析结果失败: ${error.message || '未知错误'}`);
  } finally {
    resultLoading.value = false;
  }
};

// 文件类型变化处理
const handleFileTypeChange = () => {
  // 当文件类型过滤器变化时，重置为第一页并重新加载数据
  filePagination.page = 1;
  const currentFile = currentFileIndex.value >= 0 ? uploadedFiles.value[currentFileIndex.value] : null;
  if (currentFile) {
    loadParseResult(currentFile);
  }
};

// 文件分页处理函数 - 重新请求数据
const handleFilePageChange = (page: number) => {
  filePagination.page = page;
  const currentFile = currentFileIndex.value >= 0 ? uploadedFiles.value[currentFileIndex.value] : null;
  if (currentFile) {
    loadParseResult(currentFile);
  }
};

// 文件分页大小变化处理函数
const handleFilePageSizeChange = (pageSize: number) => {
  filePagination.pageSize = pageSize;
  filePagination.page = 1; // 重置到第一页
  const currentFile = currentFileIndex.value >= 0 ? uploadedFiles.value[currentFileIndex.value] : null;
  if (currentFile) {
    loadParseResult(currentFile);
  }
};

// 解析文件
const handleParseFile = (file: typeof uploadedFiles.value[0], index: number) => {
  // 设置当前查看的文件索引
  currentFileIndex.value = index;

  // 设置解析状态
  file.parsing = true;

  parseDirectoryTree({ record_id: file.recordId, file_path: file.path })
    .then(response => {
      if (response?.data) {
        file.parseResult = response.data;
        file.parsed = true;
        message.success(`'${file.filename}' 解析成功, 共${file.parseResult.total_files}个文件`);

        // 重置分页和过滤器，并加载解析结果
        filePagination.page = 1;
        fileTypeFilter.value = 'all';
        loadParseResult(file);
      } else {
        message.error(`'${file.filename}' 解析失败: 服务器响应格式不正确`);
      }
    })
    .catch(error => {
      message.error(`'${file.filename}' 解析失败: ${error.message || '未知错误'}`);
    })
    .finally(() => {
      file.parsing = false;
    });
};

// URL上传处理函数
const handleUrlUpload = async () => {
  const url = uploadUrl.value.trim();
  if (!url) {
    message.warning('请输入有效的URL');
    return;
  }

  try {
    // 重置进度状态
    uploadProgress.value = 0;
    uploadStatus.value = '准备下载';
    urlUploading.value = true;

    // 创建进度回调函数
    const handleProgress = (percent: number) => {
      uploadProgress.value = percent;

      // 根据进度更新状态文本
      if (percent < 50) {
        uploadStatus.value = '下载中';
      } else if (percent < 90) {
        uploadStatus.value = '处理中';
      } else if (percent < 100) {
        uploadStatus.value = '完成处理';
      } else {
        uploadStatus.value = '上传成功';
      }
    };

    // 设置120秒超时时间（2分钟），足够处理大多数文件
    const { data } = await uploadDirectoryTreeFromUrl(url, handleProgress, 120000);

    // 无论服务器是否提供了进度，确保在成功时显示100%
    uploadProgress.value = 100;
    uploadStatus.value = '上传成功';

    if (data) {
      message.success(`URL文件上传成功, 已保存到: ${data.path}`);

      // 添加到已上传文件列表
      uploadedFiles.value.push({
        filename: data.filename,
        path: data.path,
        recordId: data.record_id,
        parsing: false,
        parsed: false
      });

      // 清空输入框
      uploadUrl.value = '';

      // 延迟1秒后重置进度显示（让用户有时间看到100%完成状态）
      setTimeout(() => {
        uploadProgress.value = 0;
      }, 1000);
    }
  } catch (error: any) {
    uploadStatus.value = '上传失败';
    uploadProgress.value = 0; // 重置进度

    // 判断错误类型，提供更详细的错误信息
    if (error.message?.includes('timeout')) {
      // 提供超时错误的详细信息和建议
      message.error('URL上传超时，可能是文件太大或网络问题。建议：1.选择更小的文件；2.检查URL是否有效；3.确保文件所在服务器响应迅速。');
    } else if (error.response?.status === 404) {
      message.error('文件不存在或无法访问，请检查URL是否正确。');
    } else if (error.message?.includes('network')) {
      message.error('网络连接异常，请检查您的网络连接并重试。');
    } else {
      message.error(`URL上传失败: ${error.message || '未知错误'}`);
    }

    // 在控制台记录详细错误，便于调试
    console.error('URL上传错误详情:', error);
  } finally {
    // 保持进度条显示一会儿，然后再重置urlUploading
    setTimeout(() => {
      urlUploading.value = false;
    }, 1500);
  }
};
</script>

<style scoped></style>
