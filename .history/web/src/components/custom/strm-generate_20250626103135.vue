<template>
    <div class="strm-generate">
        <n-spin :show="loading">
            <!-- 服务器选择表单 -->
            <n-card v-if="!taskStarted" title="生成STRM文件" :bordered="false" size="small">
                <n-alert v-if="fileCount === 0" type="warning" class="mb-16px">
                    <template #icon>
                        <icon-mdi-alert-circle />
                    </template>
                    <span>当前解析结果中没有视频文件，无法生成STRM文件</span>
                </n-alert>
                <div v-else>
                    <n-form ref="formRef" :model="formModel" label-placement="left" label-width="120px">
                        <n-form-item label="媒体服务器" path="serverId">
                            <n-select v-model:value="formModel.serverId" :options="serverOptions" placeholder="请选择媒体服务器"
                                :loading="serversLoading" filterable clearable />
                        </n-form-item>
                        <n-form-item label="任务名称" path="name">
                            <n-input v-model:value="formModel.name" placeholder="自定义任务名称（可选）" />
                        </n-form-item>

                        <n-space justify="end" class="mt-16px">
                            <n-button type="primary" :loading="generating"
                                :disabled="!formModel.serverId || fileCount === 0" @click="handleGenerateStrm">
                                生成STRM文件
                            </n-button>
                        </n-space>
                    </n-form>
                </div>
            </n-card>

            <!-- 生成任务进度 -->
            <n-card v-if="taskStarted" title="STRM文件生成进度" :bordered="false" size="small">
                <n-space vertical>
                    <div class="flex justify-between">
                        <div>
                            <div class="text-16px font-medium">{{ taskInfo.name }}</div>
                            <div class="text-12px text-secondary-text">{{ getStatusText(taskInfo.status) }}</div>
                        </div>
                        <n-button :disabled="taskInfo.status !== 'running' && taskInfo.status !== 'pending'"
                            @click="handleCancelTask">
                            取消任务
                        </n-button>
                    </div>

                    <n-progress type="line" :percentage="taskInfo.progress || 0"
                        :processing="taskInfo.status === 'running'" :status="getProgressStatus(taskInfo.status)" />

                    <n-descriptions label-placement="left" :column="1" size="small" class="mt-16px">
                        <n-descriptions-item label="总文件数">{{ taskInfo.total_files }}</n-descriptions-item>
                        <n-descriptions-item label="已处理文件">{{ taskInfo.processed_files }}</n-descriptions-item>
                        <n-descriptions-item label="成功生成">{{ taskInfo.success_files }}</n-descriptions-item>
                        <n-descriptions-item label="失败文件">{{ taskInfo.failed_files }}</n-descriptions-item>
                        <n-descriptions-item label="开始时间">{{ taskInfo.start_time || '未开始' }}</n-descriptions-item>
                        <n-descriptions-item label="完成时间">{{ taskInfo.end_time || '未完成' }}</n-descriptions-item>
                    </n-descriptions>

                    <!-- 下载按钮 -->
                    <n-space v-if="taskInfo.status === 'completed'" justify="center" class="mt-16px">
                        <n-button type="success" @click="handleDownloadStrm">
                            <template #icon>
                                <icon-mdi-download />
                            </template>
                            下载STRM文件
                        </n-button>
                    </n-space>
                </n-space>
            </n-card>
        </n-spin>
    </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, onUnmounted } from 'vue';
import { useMessage } from 'naive-ui';
import type { FormInst, SelectOption } from 'naive-ui';
import { cancelTask, generateStrm, getMediaServers, getStrmDownloadUrl, getTaskStatus, getTaskStatusEventSource } from '@/service/api/strm';

// 常量
const STATUS_TEXT = {
    pending: '等待开始',
    running: '正在生成',
    completed: '生成完成',
    failed: '生成失败',
    canceled: '已取消'
};

// 组件的Props
const props = defineProps({
    recordId: {
        type: Number,
        required: true
    },
    fileCount: {
        type: Number,
        default: 0
    },
    videoCount: {
        type: Number,
        default: 0
    }
});

// 接受的事件
const emit = defineEmits(['generate-success', 'generate-error']);

// 响应式状态
const loading = ref(false);
const serversLoading = ref(false);
const generating = ref(false);
const taskStarted = ref(false);
const taskPolling = ref(false);
const serverOptions = ref<SelectOption[]>([]);
const formRef = ref<FormInst | null>(null);
const formModel = ref({
    serverId: null as number | null,
    name: ''
});
const taskInfo = ref<StrmAPI.StrmTaskDetail>({
    id: 0,
    name: '',
    status: 'pending',
    total_files: 0,
    processed_files: 0,
    success_files: 0,
    failed_files: 0,
    progress: 0,
    output_dir: '',
    files: [],
    file_count: 0,
    resource_files_count: 0
});

// 新增的变量
const eventSource = ref<EventSource | null>(null);
const connected = ref(false);

// 消息组件
const message = useMessage();

// 获取媒体服务器列表
const fetchServers = async () => {
    serversLoading.value = true;
    try {
        const res = await getMediaServers();
        const servers: StrmAPI.MediaServer[] = res.data || [];

        // 转换为Select选项
        serverOptions.value = servers.map(server => ({
            label: `${server.name} (${server.server_type})`,
            value: server.id,
            disabled: false
        }));

        // 自动选择默认服务器
        const defaultServer = servers.find(s => s.is_default);
        if (defaultServer) {
            formModel.value.serverId = defaultServer.id;
        }
    } catch (error: any) {
        message.error(error.message || '获取媒体服务器失败');
    } finally {
        serversLoading.value = false;
    }
};

// 设置任务状态事件流
const setupTaskStatusEvents = () => {
    if (!taskInfo.value.id) return;

    // 关闭之前的连接（如果有）
    closeEventSource();

    // 创建新的EventSource连接
    try {
        eventSource.value = getTaskStatusEventSource(taskInfo.value.id);

        // 连接打开事件
        eventSource.value.onopen = () => {
            connected.value = true;
            console.log(`SSE连接已打开: 任务 ${taskInfo.value.id}`);
        };

        // 接收初始数据
        eventSource.value.addEventListener('initial', (event) => {
            const data = JSON.parse(event.data);
            taskInfo.value = data;
            console.log('收到初始任务状态:', data);
        });

        // 状态更新
        eventSource.value.addEventListener('update', (event) => {
            const data = JSON.parse(event.data);
            taskInfo.value = data;
            console.log('任务状态已更新:', data);
        });

        // 任务完成
        eventSource.value.addEventListener('complete', (event) => {
            const data = JSON.parse(event.data);
            taskInfo.value = data;
            console.log('任务已完成:', data);

            // 触发对应的事件
            if (data.status === 'completed') {
                message.success('STRM文件生成完成');
                emit('generate-success', data);
            } else if (data.status === 'failed') {
                message.error('STRM文件生成失败');
                emit('generate-error', data);
            } else if (data.status === 'canceled') {
                message.warning('STRM文件生成已取消');
                emit('generate-error', data);
            }

            // 关闭连接
            closeEventSource();
        });

        // 错误处理
        eventSource.value.addEventListener('error', (event) => {
            console.error('SSE连接错误:', event);

            // 如果连接已关闭，尝试回退到轮询方式
            if (!connected.value && taskInfo.value.id) {
                message.warning('实时连接失败，将使用轮询获取任务状态');
                pollTaskStatus();
            }
        });

    } catch (error) {
        console.error('创建SSE连接失败:', error);
        // 回退到轮询方式
        message.warning('无法建立实时连接，将使用轮询获取任务状态');
        pollTaskStatus();
    }
};

// 关闭EventSource连接
const closeEventSource = () => {
    if (eventSource.value) {
        eventSource.value.close();
        eventSource.value = null;
        connected.value = false;
    }
};

// 获取任务状态（保留轮询作为备选方案）
const pollTaskStatus = async () => {
    if (!taskInfo.value.id || taskPolling.value) return;

    let retryCount = 0;
    const maxRetries = 3;
    const pollInterval = 2000; // 轮询间隔：2秒

    const doPoll = async () => {
        if (!taskInfo.value.id) return;

        taskPolling.value = true;
        try {
            const res = await getTaskStatus(taskInfo.value.id);

            // 重置重试计数
            retryCount = 0;

            // 更新任务信息
            taskInfo.value = res.data;

            // 如果任务还在运行，继续轮询
            if (taskInfo.value.status === 'running' || taskInfo.value.status === 'pending') {
                setTimeout(doPoll, pollInterval);
            } else {
                // 任务完成，停止轮询
                taskPolling.value = false;

                if (taskInfo.value.status === 'completed') {
                    message.success('STRM文件生成完成');
                    emit('generate-success', taskInfo.value);
                } else if (taskInfo.value.status === 'failed') {
                    message.error('STRM文件生成失败');
                    emit('generate-error', taskInfo.value);
                } else if (taskInfo.value.status === 'canceled') {
                    message.warning('STRM文件生成已取消');
                    emit('generate-error', taskInfo.value);
                }
            }
        } catch (error: any) {
            console.error('获取任务状态失败:', error);

            // 如果重试次数未达到最大值，则重试
            if (retryCount < maxRetries) {
                retryCount = retryCount + 1;
                message.warning(`获取任务状态失败，正在重试(${retryCount}/${maxRetries})...`);
                setTimeout(doPoll, pollInterval);
            } else {
                message.error(error.message || '获取任务状态失败，已停止轮询');
                taskPolling.value = false;
                emit('generate-error', {
                    ...taskInfo.value,
                    status: 'failed',
                    error: error.message
                });
            }
        }
    };

    // 开始轮询
    doPoll();
};

// 处理生成STRM文件
const handleGenerateStrm = async () => {
    if (!formModel.value.serverId) {
        message.warning('请选择媒体服务器');
        return;
    }

    generating.value = true;
    try {
        const res = await generateStrm({
            record_id: props.recordId,
            server_id: formModel.value.serverId,
            name: formModel.value.name || undefined
        });

        if (!res.data || !res.data.task_id) {
            // 处理任务创建失败但API返回成功的情况
            const errorMsg = res.data?.error || '创建任务失败，未获取到任务ID';
            message.error(errorMsg);
            generating.value = false;
            return;
        }

        // 更新任务信息
        taskInfo.value = {
            id: res.data.task_id,
            name: res.data.name || formModel.value.name || '未命名任务',
            status: res.data.status || 'pending',
            total_files: 0, // 初始值设为0，稍后通过轮询获取实际值
            processed_files: 0,
            success_files: 0,
            failed_files: 0,
            progress: 0,
            output_dir: '',
            files: [],
            file_count: 0,
            resource_files_count: 0
        };

        // 标记任务已开始
        taskStarted.value = true;

        // 使用SSE设置实时状态更新
        setupTaskStatusEvents();

        message.success(res.data.message || '任务创建成功，开始生成STRM文件');
    } catch (error: any) {
        console.error('STRM生成失败:', error);
        message.error(error.message || 'STRM文件生成失败，请检查服务器连接');
    } finally {
        generating.value = false;
    }
};

// 取消任务
const handleCancelTask = async () => {
    if (!taskInfo.value.id) return;

    try {
        await cancelTask(taskInfo.value.id);
        message.success('任务已取消');

        // 更新任务状态
        taskInfo.value.status = 'canceled';
    } catch (error: any) {
        message.error(error.message || '取消任务失败');
    }
};

// 下载STRM文件
const handleDownloadStrm = () => {
    if (!taskInfo.value.id) return;

    // 获取下载链接并打开新窗口
    const downloadUrl = getStrmDownloadUrl(taskInfo.value.id);
    window.open(downloadUrl, '_blank');
};

// 获取状态文本
const getStatusText = (status: string) => {
    return STATUS_TEXT[status as keyof typeof STATUS_TEXT] || status;
};

// 获取进度条状态
const getProgressStatus = (status: string) => {
    if (status === 'completed') return 'success';
    if (status === 'failed' || status === 'canceled') return 'error';
    return 'info';
};

// 组件挂载时获取服务器列表
onMounted(() => {
    fetchServers();
});

// 组件卸载时清理资源
onUnmounted(() => {
    closeEventSource();
});

// 向外部暴露方法
defineExpose({
    resetTask() {
        // 确保关闭任何已有的SSE连接
        closeEventSource();

        taskStarted.value = false;
        taskInfo.value = {
            id: 0,
            name: '',
            status: 'pending',
            total_files: 0,
            processed_files: 0,
            success_files: 0,
            failed_files: 0,
            progress: 0,
            output_dir: '',
            files: [],
            file_count: 0,
            resource_files_count: 0
        };
    }
});
</script>

<style lang="scss" scoped>
.strm-generate {
    width: 100%;
}
</style>
