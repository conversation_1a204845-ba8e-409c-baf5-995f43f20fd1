<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { Icon } from '@iconify/vue';
import {
  NButton,
  NDataTable,
  NForm,
  NFormItem,
  NInput,
  NModal,
  NPopconfirm,
  NSelect,
  NTag,
  useMessage
} from 'naive-ui';
import {
  createMediaServer,
  deleteMediaServer,
  getMediaServers,
  testServerConnection,
  updateMediaServer
} from '@/service/api/strm';
import { $t } from '@/locales';

// 组件属性定义
defineOptions({ name: 'ServerManagement' });

// 定义事件
const emit = defineEmits(['update:servers']);

// 定义服务器类型接口
interface ServerItem {
  id: number;
  name: string;
  server_type: string;
  base_url: string;
  description?: string;
  auth_required: boolean;
  username?: string;
  password?: string;
  create_time: string;
  status: 'unknown' | 'success' | 'error' | 'warning';
}

// 服务器类型选项
const serverTypeOptions = [
  { label: $t('strm.settings.http' as any), value: 'http' },
  { label: $t('strm.settings.https' as any), value: 'https' },
  { label: $t('strm.settings.local' as any), value: 'local' },
  { label: $t('strm.settings.ftp' as any), value: 'ftp' },
  { label: $t('strm.settings.webdav' as any), value: 'webdav' },
  { label: $t('strm.settings.cd2host' as any), value: 'cd2host' },
  { label: $t('strm.settings.xiaoyahost' as any), value: 'xiaoyahost' }
];

// 消息服务
const message = useMessage();

// 加载状态
const loading = ref(false);
const submitting = ref(false);
const testing = ref(false);

// 表格列定义
const columns = [
  {
    title: $t('strm.settings.serverName' as any),
    key: 'name',
    width: 180
  },
  {
    title: $t('strm.settings.serverType' as any),
    key: 'server_type',
    width: 160,
    render: (row: ServerItem) => {
      const serverTypes: Record<string, string> = {
        http: 'HTTP',
        https: 'HTTPS',
        local: $t('strm.settings.local' as any),
        ftp: 'FTP',
        webdav: 'WebDAV',
        cd2host: $t('strm.settings.cd2host' as any),
        xiaoyahost: $t('strm.settings.xiaoyahost' as any)
      };
      const type = serverTypes[row.server_type.toLowerCase()] || row.server_type;

      let tagType: 'info' | 'warning' | 'success' = 'info';
      if (row.server_type.toLowerCase() === 'cd2host') {
        tagType = 'warning';
      } else if (row.server_type.toLowerCase() === 'xiaoyahost') {
        tagType = 'success';
      }

      return h(NTag, { type: tagType, round: true, size: 'medium' }, { default: () => type });
    }
  },
  {
    title: 'URL',
    key: 'base_url',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('strm.settings.status' as any),
    key: 'status',
    width: 120,
    align: 'center' as const,
    render: (row: ServerItem) => {
      const status = row.status || 'unknown';
      const statusConfig: Record<string, { type: 'default' | 'success' | 'error' | 'warning'; text: string; icon: any }> = {
        unknown: {
          type: 'default',
          text: $t('strm.settings.serverStatusUnknown' as any) as string,
          icon: 'lucide:help-circle'
        },
        success: {
          type: 'success',
          text: $t('strm.settings.serverStatusActive' as any) as string,
          icon: 'lucide:check-circle'
        },
        error: {
          type: 'error',
          text: $t('strm.settings.serverStatusInactive' as any) as string,
          icon: 'lucide:x-circle'
        },
        warning: {
          type: 'warning',
          text: $t('strm.settings.serverStatusWarning' as any) as string,
          icon: 'lucide:alert-circle'
        }
      };

      const tagConfig = statusConfig[status] || statusConfig.unknown;

      // 创建带有图标的标签
      return h(
        NTag,
        {
          type: tagConfig.type,
          round: true,
          size: 'medium',
          bordered: false
        },
        {
          default: () => [
            h(Icon, { icon: tagConfig.icon, class: 'mr-1' }),
            tagConfig.text
          ]
        }
      );
    }
  },
  {
    title: $t('common.action' as any),
    key: 'actions',
    width: 280,
    fixed: 'right' as const,
    align: 'center' as const,
    render: (row: ServerItem) => {
      return h(
        'div',
        {
          class: 'flex-center gap-8px'
        },
        [
          h(
            NButton,
            {
              size: 'small',
              onClick: () => handleTestConnection(row.id),
              type: 'info',
              ghost: true
            },
            { default: () => $t('strm.settings.test' as any) }
          ),
          h(
            NButton,
            {
              size: 'small',
              onClick: () => handleEdit(row),
              type: 'primary',
              ghost: true
            },
            { default: () => $t('common.edit' as any) }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id),
              negativeText: $t('strm.settings.cancel' as any) as string,
              positiveText: $t('strm.settings.confirm' as any) as string
            },
            {
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    ghost: true
                  },
                  { default: () => $t('common.delete' as any) }
                ),
              default: () => $t('strm.settings.confirmDelete' as any)
            }
          )
        ]
      );
    }
  }
];

// 数据
const servers = ref<ServerItem[]>([]);
const showAddModal = ref(false);
const showEditModal = ref(false);

// 行属性
const rowProps = (_row: ServerItem) => {
  return {
    style: 'cursor: pointer;'
  };
};

// 表单数据
const formData = reactive({
  id: null as number | null,
  name: '',
  server_type: 'HTTP',
  base_url: '',
  description: '',
  auth_required: false,
  username: '',
  password: ''
});

// 获取服务器列表
async function fetchServers() {
  try {
    loading.value = true;
    const res = await getMediaServers();

    if (res.data && Array.isArray(res.data)) {
      servers.value = res.data.map((server: any) => ({
        id: server.id,
        name: server.name,
        server_type: server.server_type,
        base_url: server.base_url,
        description: server.description,
        auth_required: server.auth_required,
        username: server.username,
        password: server.password,
        create_time: server.create_time,
        status: 'unknown' as const
      }));

      // 向父组件发送服务器列表更新事件
      emit('update:servers');
    }
  } catch (error: any) {
    message.error(error.message || '获取服务器列表失败');
  } finally {
    loading.value = false;
  }
}

// 添加服务器
function handleAdd() {
  // 重置表单
  Object.assign(formData, {
    id: null,
    name: '',
    server_type: 'HTTP',
    base_url: '',
    description: '',
    auth_required: false,
    username: '',
    password: ''
  });
  showAddModal.value = true;
}

// 编辑服务器
function handleEdit(row: ServerItem) {
  // 填充表单
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    server_type: row.server_type,
    base_url: row.base_url,
    description: row.description || '',
    auth_required: row.auth_required,
    username: row.username || '',
    password: ''  // 不显示密码
  });
  showEditModal.value = true;
}

// 测试服务器连接
async function handleTestConnection(serverId: number) {
  try {
    testing.value = true;

    // 查找服务器索引
    const serverIndex = servers.value.findIndex(s => s.id === serverId);
    if (serverIndex === -1) {
      message.error('找不到指定的服务器');
      return;
    }

    // 更新状态为测试中
    servers.value[serverIndex].status = 'unknown';

    // 发送测试请求
    const res = await testServerConnection(serverId);

    // 更新服务器状态
    if (res.data && res.data.status) {
      servers.value[serverIndex].status = res.data.status as 'success' | 'error' | 'warning';
      message.success(res.data.message || '连接测试完成');
    } else {
      servers.value[serverIndex].status = 'error';
      message.error(res.data?.message || '连接测试失败');
    }
  } catch (error: any) {
    message.error(error.message || '测试连接失败');
  } finally {
    testing.value = false;
  }
}

// 测试添加的服务器连接
async function handleTestNewServer() {
  try {
    testing.value = true;

    // 构造临时服务器对象用于测试
    const tempServer = {
      name: formData.name,
      server_type: formData.server_type,
      base_url: formData.base_url,
      description: formData.description || null
    };

    // 发送测试请求
    const res = await testServerConnection(0, tempServer);

    if (res.data && res.data.status === 'success') {
      message.success(res.data.message || '连接测试成功');
    } else {
      message.error(res.data?.message || '连接测试失败');
    }
  } catch (error: any) {
    message.error(error.message || '测试连接失败');
  } finally {
    testing.value = false;
  }
}

// 删除服务器
async function handleDelete(id: number) {
  try {
    await deleteMediaServer(id);
    message.success('服务器删除成功');
    // 刷新列表
    await fetchServers();
  } catch (error: any) {
    message.error(error.message || '删除服务器失败');
  }
}

// 保存服务器
async function handleSave(isNew = false) {
  try {
    submitting.value = true;

    // 构造请求数据
    const data = {
      name: formData.name,
      server_type: formData.server_type,
      base_url: formData.base_url,
      description: formData.description || null,
      auth_required: false,  // 固定为false，移除认证需求
      username: null,
      password: null
    };

    if (isNew) {
      // 新增
      await createMediaServer(data);
      message.success('服务器添加成功');
      showAddModal.value = false;
    } else if (formData.id !== null) {
      // 更新
      await updateMediaServer(formData.id, data);
      message.success('服务器更新成功');
      showEditModal.value = false;
    }

    // 刷新列表
    await fetchServers();
  } catch (error: any) {
    message.error(error.message || (isNew ? '添加服务器失败' : '更新服务器失败'));
  } finally {
    submitting.value = false;
  }
}

// 生命周期
onMounted(() => {
  fetchServers();
});
</script>

<template>
  <div>
    <div class="flex justify-between mb-4">
      <h3 class="text-lg font-bold">{{ $t('strm.settings.serverManagement') }}</h3>
      <n-button type="primary" size="small" @click="handleAdd">
        <template #icon>
          <Icon icon="lucide:plus" />
        </template>
        {{ $t('strm.settings.addServer') }}
      </n-button>
    </div>

    <div class="server-table-container">
      <n-data-table :columns="columns" :data="servers" :loading="loading" :bordered="false" :single-line="false"
        :striped="true" size="small" :row-props="rowProps" remote flex-height style="width: 100%;" />
    </div>

    <!-- 添加服务器 -->
    <n-modal v-model:show="showAddModal" :title="$t('strm.settings.addServer')" preset="card" :mask-closable="false"
      style="width: 500px">
      <n-form :model="formData" label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item :label="$t('strm.settings.serverName')" required>
          <n-input v-model:value="formData.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverType')" required>
          <n-select v-model:value="formData.server_type" :options="serverTypeOptions" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.baseUrl')" required>
          <n-input v-model:value="formData.base_url" :placeholder="$t('strm.settings.baseUrlPlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.description')">
          <n-input v-model:value="formData.description" type="textarea"
            :placeholder="$t('strm.settings.descriptionPlaceholder')" />
        </n-form-item>
      </n-form>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <n-button @click="showAddModal = false">{{ $t('strm.settings.cancel') }}</n-button>
          <n-button type="info" :loading="testing" @click="handleTestNewServer">
            <template #icon>
              <Icon icon="lucide:activity" />
            </template>
            {{ $t('strm.settings.test') }}
          </n-button>
          <n-button type="primary" :loading="submitting" @click="handleSave(true)">
            {{ $t('strm.settings.save') }}
          </n-button>
        </div>
      </template>
    </n-modal>

    <!-- 编辑服务器 -->
    <n-modal v-model:show="showEditModal" :title="$t('strm.settings.editServer')" preset="card" :mask-closable="false"
      style="width: 500px">
      <n-form :model="formData" label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item :label="$t('strm.settings.serverName')" required>
          <n-input v-model:value="formData.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverType')" required>
          <n-select v-model:value="formData.server_type" :options="serverTypeOptions" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.baseUrl')" required>
          <n-input v-model:value="formData.base_url" :placeholder="$t('strm.settings.baseUrlPlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.description')">
          <n-input v-model:value="formData.description" type="textarea"
            :placeholder="$t('strm.settings.descriptionPlaceholder')" />
        </n-form-item>
      </n-form>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <n-button @click="showEditModal = false">{{ $t('strm.settings.cancel') }}</n-button>
          <n-button type="primary" :loading="submitting" @click="handleSave(false)">
            {{ $t('strm.settings.save') }}
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<style scoped>
.server-management {
  width: 100%;
}

.server-table-container {
  width: 100%;
  margin-top: 1rem;
  overflow-x: auto !important;
}

/* 使用更强的CSS选择器确保样式覆盖 */
.server-table-container :deep(.n-data-table) {
  width: 100% !important;
}

.server-table-container :deep(.n-data-table-th) {
  font-weight: 600 !important;
}

.server-table-container :deep(.n-data-table-tr:hover) {
  background-color: rgba(0, 128, 255, 0.05) !important;
}

.server-table-container :deep(.n-button) {
  margin: 4px !important;
}

.server-table-container :deep(.n-tag) {
  padding: 0 10px !important;
}

/* 新增样式以确保操作列不被截断 */
.server-table-container :deep(.n-data-table-td) {
  white-space: nowrap !important;
  overflow: visible !important;
}

.server-table-container :deep(.n-data-table-td:last-child) {
  padding: 8px 16px !important;
  min-width: 250px !important;
}
</style>
