<template>
  <div class="file-tree-view">
    <div v-if="isLoading" class="loading-state">
      <n-spin size="medium" />
      <p class="mt-2">加载目录结构中...</p>
    </div>
    <div v-else-if="treeData.length === 0" class="empty-state">
      <n-empty description="暂无目录数据" />
      <p class="mt-2 text-secondary-text">选定的目录为空或未找到符合条件的文件</p>
    </div>
    <n-tree v-else block-line :data="treeData" :default-expanded-keys="defaultExpandedKeys"
      :expanded-keys="expandedKeys" :pattern="searchPattern" :selectable="false" :on-load="handleLoad"
      @update:expanded-keys="handleExpandedKeys" />
  </div>
</template>

<script setup lang="ts">
import { computed, h, onMounted, ref, watch, type PropType } from 'vue';
import { NEmpty, NSpin, NTree, useMessage, type TreeOption } from 'naive-ui';
import { getDirectoryContent } from '@/service/api/strm';

const message = useMessage();

const props = defineProps({
  recordId: {
    type: [String, Number],
    required: true
  },
  fileTypeFilter: {
    type: String,
    default: 'all'
  },
  searchValue: {
    type: String,
    default: ''
  },
  rootDirectories: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  rootFiles: {
    type: Array as PropType<StrmAPI.ParsedFile[]>,
    default: () => []
  }
});

// 搜索模式字符串
const searchPattern = computed(() => props.searchValue);

// 节点加载状态
const loadingKeys = ref<Set<string>>(new Set());

// 默认展开的节点和当前展开的节点
const defaultExpandedKeys = ref<string[]>([]);
const expandedKeys = ref<string[]>([]);

// 树形数据
const treeData = ref<TreeOption[]>([]);

// 节点键值到路径的映射
const keyToPathMap = ref<Map<string, string>>(new Map());

// 更新展开的节点
const handleExpandedKeys = (keys: string[]) => {
  expandedKeys.value = keys;
};

// 初始化树结构
const initTreeData = () => {
  // 创建根目录节点
  const rootNodes: TreeOption[] = [];

  // 添加根目录
  props.rootDirectories.forEach(dir => {
    const key = `dir:${dir}`;
    keyToPathMap.value.set(key, `/${dir}`);

    rootNodes.push({
      key,
      label: dir,
      prefix: () => h('span', { class: 'mr-1' }, '📁'),
      isLeaf: false,
      children: []
    });
  });

  // 添加根目录中的文件
  props.rootFiles.forEach(file => {
    if (props.fileTypeFilter !== 'all' && file.file_type !== props.fileTypeFilter) {
      return;
    }

    rootNodes.push({
      key: `file:${file.file_name}`,
      label: file.file_name,
      isLeaf: true,
      prefix: () => h('span', { class: 'mr-1' }, getFileEmoji(file.file_type)),
      suffix: () => h('span', { class: 'ml-2 text-xs text-secondary-text' }, `(${file.extension})`)
    });
  });

  treeData.value = rootNodes;

  // 默认展开根节点
  if (rootNodes.length > 0) {
    const firstLevelKeys = rootNodes
      .filter(node => !node.isLeaf)
      .map(node => node.key as string);

    defaultExpandedKeys.value = firstLevelKeys;
    expandedKeys.value = firstLevelKeys;
  }
};

// 异步加载子节点
const handleLoad = async (node: TreeOption) => {
  // 检查是否已经在加载
  if (loadingKeys.value.has(node.key as string)) {
    return;
  }

  // 标记为正在加载
  loadingKeys.value.add(node.key as string);

  try {
    // 获取该节点对应的路径
    const path = keyToPathMap.value.get(node.key as string);
    if (!path) {
      throw new Error(`未找到节点 ${node.key} 对应的路径`);
    }

    // 加载该目录下的内容
    const { data } = await getDirectoryContent(props.recordId, {
      directoryPath: path,
      fileType: props.fileTypeFilter
    });

    // 处理获取到的子目录和文件
    const children: TreeOption[] = [];

    // 添加子目录
    data.subdirectories.forEach(dir => {
      const dirKey = `dir:${path}/${dir}`;
      keyToPathMap.value.set(dirKey, `${path}/${dir}`);

      children.push({
        key: dirKey,
        label: dir,
        prefix: () => h('span', { class: 'mr-1' }, '📁'),
        isLeaf: false,
        children: []
      });
    });

    // 添加文件
    data.files.forEach(file => {
      children.push({
        key: `file:${path}/${file.file_name}`,
        label: file.file_name,
        isLeaf: true,
        prefix: () => h('span', { class: 'mr-1' }, getFileEmoji(file.file_type)),
        suffix: () => h('span', { class: 'ml-2 text-xs text-secondary-text' }, `(${file.extension})`)
      });
    });

    // 更新节点的子节点
    if (node.children) {
      node.children = children;
    } else {
      node.children = children;
    }
  } catch (error: any) {
    message.error(`加载目录内容失败: ${error.message || '未知错误'}`);
  } finally {
    // 移除加载状态
    loadingKeys.value.delete(node.key as string);
  }
};

// 获取文件图标emoji
function getFileEmoji(fileType: string): string {
  const iconMap: Record<string, string> = {
    'video': '🎬',
    'audio': '🎵',
    'image': '🖼️',
    'subtitle': '📃',
    'metadata': '📋',
    'other': '📄'
  };

  return iconMap[fileType] || '📄';
}

// 初始化
onMounted(() => {
  console.log('树形视图组件已挂载', {
    recordId: props.recordId,
    rootDirectories: props.rootDirectories,
    rootFiles: props.rootFiles.length
  });
  initTreeData();
});

// 添加对根目录和根文件的监听，确保它们变化时更新树形视图
watch([() => props.rootDirectories, () => props.rootFiles], ([newDirs, newFiles], [oldDirs, oldFiles]) => {
  console.log('树形视图根数据变化', {
    newDirectories: newDirs.length,
    oldDirectories: oldDirs ? oldDirs.length : 0,
    newFiles: newFiles.length,
    oldFiles: oldFiles ? oldFiles.length : 0
  });

  // 只有当数据实际发生变化时才重新初始化
  if (newDirs.length > 0 || newFiles.length > 0) {
    initTreeData();
  }
}, { deep: true });

// 当文件类型过滤器变化时，重新初始化树结构
watch(() => props.fileTypeFilter, (newType, oldType) => {
  console.log('文件类型过滤器变化', { newType, oldType });
  initTreeData();
});

// 当搜索值变化时，展开相关节点
watch(() => props.searchValue, (value) => {
  if (value) {
    // TODO: 搜索功能需要适配懒加载模式，
    // 目前的实现可能无法正常工作，需要进一步改进
    // 可能需要全量加载数据或者服务端搜索
  } else {
    // 搜索清空时只展开第一级
    const firstLevelKeys = treeData.value
      .filter(node => !node.isLeaf)
      .map(node => node.key as string);
    expandedKeys.value = firstLevelKeys;
  }
});
</script>

<style scoped>
.file-tree-view {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
