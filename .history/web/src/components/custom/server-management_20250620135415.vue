<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { Icon } from '@iconify/vue';
import {
  NButton,
  NDataTable,
  NForm,
  NFormItem,
  NInput,
  NModal,
  NPopconfirm,
  NRadioButton,
  NRadioGroup,
  NTag,
  useMessage
} from 'naive-ui';
import {
  createMediaServer,
  deleteMediaServer,
  getMediaServers,
  testServerConnection,
  updateMediaServer
} from '@/service/api/strm';
import { $t } from '@/locales';

// 组件属性定义
defineOptions({ name: 'ServerManagement' });

// 定义事件
const emit = defineEmits(['update:servers']);

// 定义服务器类型接口
interface ServerItem {
  id: number;
  name: string;
  server_type: string;
  base_url: string;
  description?: string;
  auth_required: boolean;
  username?: string;
  password?: string;
  create_time: string;
  status: 'unknown' | 'success' | 'error' | 'warning';
}

// 消息服务
const message = useMessage();

// 加载状态
const loading = ref(false);
const submitting = ref(false);
const testing = ref(false);

// 表格列定义
const columns = [
  {
    title: $t('strm.settings.serverName' as any),
    key: 'name',
    width: 180
  },
  {
    title: $t('strm.settings.serverType' as any),
    key: 'server_type',
    width: 160,
    render: (row: ServerItem) => {
      const serverTypes: Record<string, string> = {
        http: 'HTTP',
        https: 'HTTPS',
        local: $t('strm.settings.local' as any),
        ftp: 'FTP',
        webdav: 'WebDAV',
        cd2host: $t('strm.settings.cd2host' as any),
        xiaoyahost: $t('strm.settings.xiaoyahost' as any)
      };
      const type = serverTypes[row.server_type.toLowerCase()] || row.server_type;

      let tagType: 'info' | 'warning' | 'success' = 'info';
      if (row.server_type.toLowerCase() === 'cd2host') {
        tagType = 'warning';
      } else if (row.server_type.toLowerCase() === 'xiaoyahost') {
        tagType = 'success';
      }

      return h(NTag, { type: tagType, round: true, size: 'medium' }, { default: () => type });
    }
  },
  {
    title: 'URL',
    key: 'base_url',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: $t('strm.settings.status' as any),
    key: 'status',
    width: 120,
    align: 'center' as const,
    render: (row: ServerItem) => {
      const status = row.status || 'unknown';
      const statusConfig: Record<string, { type: 'default' | 'success' | 'error' | 'warning'; text: string; icon: any }> = {
        unknown: {
          type: 'default',
          text: $t('strm.settings.serverStatusUnknown' as any) as string,
          icon: 'lucide:help-circle'
        },
        success: {
          type: 'success',
          text: $t('strm.settings.serverStatusActive' as any) as string,
          icon: 'lucide:check-circle'
        },
        error: {
          type: 'error',
          text: $t('strm.settings.serverStatusInactive' as any) as string,
          icon: 'lucide:x-circle'
        },
        warning: {
          type: 'warning',
          text: $t('strm.settings.serverStatusWarning' as any) as string,
          icon: 'lucide:alert-circle'
        }
      };

      const tagConfig = statusConfig[status] || statusConfig.unknown;

      // 创建带有图标的标签
      return h(
        NTag,
        {
          type: tagConfig.type,
          round: true,
          size: 'medium',
          bordered: false
        },
        {
          default: () => [
            h(Icon, { icon: tagConfig.icon, class: 'mr-1' }),
            tagConfig.text
          ]
        }
      );
    }
  },
  {
    title: $t('common.action' as any),
    key: 'actions',
    width: 300,
    fixed: 'right' as const,
    align: 'center' as const,
    render: (row: ServerItem) => {
      return h(
        'div',
        {
          class: 'flex items-center justify-center gap-2'
        },
        [
          h(
            NButton,
            {
              size: 'small',
              onClick: () => handleTestConnection(row.id),
              type: 'info',
              ghost: true
            },
            { default: () => $t('strm.settings.test' as any) }
          ),
          h(
            NButton,
            {
              size: 'small',
              onClick: () => handleEdit(row),
              type: 'primary',
              ghost: true
            },
            { default: () => $t('common.edit' as any) }
          ),
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete(row.id),
              negativeText: $t('strm.settings.cancel' as any) as string,
              positiveText: $t('strm.settings.confirm' as any) as string
            },
            {
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    ghost: true
                  },
                  { default: () => $t('common.delete' as any) }
                ),
              default: () => $t('strm.settings.confirmDelete' as any)
            }
          )
        ]
      );
    }
  }
];

// 数据
const servers = ref<ServerItem[]>([]);
const showAddModal = ref(false);
const showEditModal = ref(false);

// 表单验证结果
const urlValid = ref(true);

// 行属性
const rowProps = (_row: ServerItem) => {
  return {
    style: 'cursor: pointer;'
  };
};

// 表单数据
const formData = reactive({
  id: null as number | null,
  name: '',
  server_type: 'cd2host', // 默认为下载服务器
  base_url: '',
  description: '',
  auth_required: false,
  username: '',
  password: ''
});

// 验证服务器URL
function validateServerUrl(url: string): boolean {
  if (!url || url.trim() === '') {
    return false;
  }

  // 简单URL验证
  try {
    // 尝试构建URL对象，如果失败则返回false
    // 如果不是完整URL，添加协议前缀再尝试
    let testUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      // 添加http前缀以便URL构造函数可以正确解析
      testUrl = `http://${url}`;
    }

    // 尝试创建URL对象以验证URL格式
    const urlObj = new URL(testUrl);
    // 确保使用URL对象，避免副作用警告
    return Boolean(urlObj.hostname);
  } catch {
    // 捕获任何解析错误并返回false
    return false;
  }
}

// 监视URL输入，实时验证
function handleUrlInput() {
  // 实时验证URL
  urlValid.value = validateServerUrl(formData.base_url);
  console.log('URL验证结果:', urlValid.value, '对于URL:', formData.base_url);
}

// 获取服务器列表
async function fetchServers() {
  try {
    loading.value = true;
    console.log('开始获取服务器列表...');
    const res = await getMediaServers();

    // 调试日志，查看API返回数据
    console.log('获取服务器数据成功:', res);

    if (res.data && Array.isArray(res.data)) {
      console.log('服务器数据类型是数组，长度:', res.data.length);

      // 转换数据
      servers.value = res.data.map((server: any) => {
        console.log('服务器数据项:', server);
        return {
          id: server.id,
          name: server.name,
          server_type: server.server_type,
          base_url: server.base_url,
          description: server.description,
          auth_required: server.auth_required,
          username: server.username,
          password: server.password,
          create_time: server.create_time,
          status: 'unknown' as const
        };
      });

      // 检查转换后的数据
      console.log('处理后的服务器列表:', servers.value);

      // 检查数组长度和内容
      console.log('servers.value类型:', Object.prototype.toString.call(servers.value));
      console.log('servers.value长度:', servers.value.length);
      if (servers.value.length > 0) {
        console.log('servers.value第一项:', servers.value[0]);
      }

      // 向父组件发送服务器列表更新事件
      emit('update:servers');
    } else {
      console.warn('服务器数据格式不是数组:', res.data);
      message.error($t('strm.settings.invalidServerData'));
    }
  } catch (error: any) {
    console.error('获取服务器列表失败:', error);
    message.error(error.message || $t('strm.settings.getServerListFailed'));
  } finally {
    loading.value = false;
    console.log('获取服务器列表完成，loading状态:', loading.value);
  }
}

// 添加服务器
function handleAdd() {
  // 重置表单
  Object.assign(formData, {
    id: null,
    name: '',
    server_type: 'cd2host', // 默认为下载服务器
    base_url: '',
    description: '',
    auth_required: false,
    username: '',
    password: ''
  });
  urlValid.value = true;  // 重置验证状态
  showAddModal.value = true;
}

// 编辑服务器
function handleEdit(row: ServerItem) {
  // 填充表单
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    server_type: row.server_type,
    base_url: row.base_url,
    description: row.description || '',
    auth_required: row.auth_required,
    username: row.username || '',
    password: ''  // 不显示密码
  });
  urlValid.value = validateServerUrl(row.base_url);  // 初始验证
  showEditModal.value = true;
}

// 测试服务器连接
async function handleTestConnection(serverId: number) {
  try {
    testing.value = true;

    // 查找服务器索引
    const serverIndex = servers.value.findIndex(s => s.id === serverId);
    if (serverIndex === -1) {
      message.error('找不到指定的服务器');
      return;
    }

    // 更新状态为测试中
    servers.value[serverIndex].status = 'unknown';

    // 发送测试请求
    const res = await testServerConnection(serverId);

    // 更新服务器状态
    if (res.data && res.data.status) {
      servers.value[serverIndex].status = res.data.status as 'success' | 'error' | 'warning';
      message.success(res.data.message || '连接测试完成');
    } else {
      servers.value[serverIndex].status = 'error';
      message.error(res.data?.message || '连接测试失败');
    }
  } catch (error: any) {
    message.error(error.message || '测试连接失败');
  } finally {
    testing.value = false;
  }
}

// 测试添加的服务器连接
async function handleTestNewServer() {
  // 验证URL
  if (!urlValid.value || !formData.base_url) {
    message.error($t('strm.settings.invalidUrl'));
    return;
  }

  try {
    testing.value = true;

    // 构造临时服务器对象用于测试
    const tempServer = {
      name: formData.name || $t('strm.settings.unnamedServer'), // 确保至少有个名称
      server_type: formData.server_type,
      base_url: formData.base_url,
      description: formData.description || null
    };

    // 发送测试请求
    const res = await testServerConnection(0, tempServer);

    if (res.data && res.data.status === 'success') {
      message.success(res.data.message || $t('strm.settings.testSuccess'));
    } else {
      message.error(res.data?.message || $t('strm.settings.testError'));
    }
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.testError'));
  } finally {
    testing.value = false;
  }
}

// 删除服务器
async function handleDelete(id: number) {
  try {
    await deleteMediaServer(id);
    message.success($t('strm.settings.deleteSuccess'));
    // 刷新列表
    await fetchServers();
  } catch (error: any) {
    message.error(error.message || $t('strm.settings.deleteError'));
  }
}

// 保存服务器
async function handleSave(isNew = false) {
  try {
    submitting.value = true;

    // 构造请求数据
    const data = {
      name: formData.name,
      server_type: formData.server_type,
      base_url: formData.base_url,
      description: formData.description || null,
      auth_required: false,  // 固定为false，移除认证需求
      username: null,
      password: null
    };

    if (isNew) {
      // 新增
      await createMediaServer(data);
      message.success($t('strm.settings.addSuccess'));
      showAddModal.value = false;
    } else if (formData.id !== null) {
      // 更新
      await updateMediaServer(formData.id, data);
      message.success($t('strm.settings.updateSuccess'));
      showEditModal.value = false;
    }

    // 刷新列表
    await fetchServers();
  } catch (error: any) {
    message.error(error.message || (isNew ? $t('strm.settings.addError') : $t('strm.settings.updateError')));
  } finally {
    submitting.value = false;
  }
}

// 生命周期
onMounted(() => {
  fetchServers();
});
</script>

<template>
  <div>
    <div class="flex justify-between mb-4">
      <h3 class="text-lg font-bold">{{ $t('strm.settings.serverManagement') }}</h3>
      <n-button type="primary" size="small" @click="handleAdd">
        <template #icon>
          <Icon icon="lucide:plus" />
        </template>
        {{ $t('strm.settings.addServer') }}
      </n-button>
    </div>

    <div class="server-table-container">
      <n-data-table :columns="columns" :data="servers" :loading="loading" :bordered="false" :single-line="false"
        :striped="true" size="small" :row-props="rowProps" remote flex-height style="width: 100%;" />

      <div v-if="servers.length === 0 && !loading" class="empty-state">
        <Icon icon="lucide:database" class="empty-icon" />
        <p>{{ $t('strm.settings.noServers') }}</p>
      </div>
    </div>

    <!-- 添加服务器 -->
    <n-modal v-model:show="showAddModal" :title="$t('strm.settings.addServer')" preset="card" :mask-closable="false"
      style="width: 500px">
      <n-form :model="formData" label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item :label="$t('strm.settings.serverName')" required>
          <n-input v-model:value="formData.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverType')" required>
          <div class="server-type-container">
            <n-radio-group v-model:value="formData.server_type" class="server-type-radio-group">
              <n-radio-button value="cd2host" class="server-type-button">
                <div class="server-type-content download-server">
                  <div class="icon-container">
                    <Icon icon="ic:round-cloud-download" class="server-type-icon" />
                  </div>
                  <span>{{ $t('strm.settings.downloadServer') }}</span>
                </div>
              </n-radio-button>
              <n-radio-button value="xiaoyahost" class="server-type-button">
                <div class="server-type-content media-server">
                  <div class="icon-container">
                    <Icon icon="ic:round-smart-display" class="server-type-icon" />
                  </div>
                  <span>{{ $t('strm.settings.mediaServer') }}</span>
                </div>
              </n-radio-button>
            </n-radio-group>
          </div>
        </n-form-item>

        <n-form-item :label="$t('strm.settings.baseUrl')" required>
          <n-input v-model:value="formData.base_url" @input="handleUrlInput"
            :placeholder="$t('strm.settings.baseUrlPlaceholder')" />
          <template #feedback v-if="!urlValid">
            <span class="text-error text-sm">请输入有效的服务器地址</span>
          </template>
        </n-form-item>

        <n-form-item :label="$t('strm.settings.description')">
          <n-input v-model:value="formData.description" type="textarea"
            :placeholder="$t('strm.settings.descriptionPlaceholder')" />
        </n-form-item>
      </n-form>

      <template #footer>
        <div class="flex justify-between items-center">
          <n-button type="info" :loading="testing" @click="handleTestNewServer"
            :disabled="!urlValid || !formData.base_url">
            <template #icon>
              <Icon icon="lucide:activity" />
            </template>
            {{ $t('strm.settings.test') }}
          </n-button>

          <div class="space-x-2">
            <n-button @click="showAddModal = false">{{ $t('strm.settings.cancel') }}</n-button>
            <n-button type="primary" :loading="submitting" @click="handleSave(true)">
              {{ $t('strm.settings.save') }}
            </n-button>
          </div>
        </div>
      </template>
    </n-modal>

    <!-- 编辑服务器 -->
    <n-modal v-model:show="showEditModal" :title="$t('strm.settings.editServer')" preset="card" :mask-closable="false"
      style="width: 500px">
      <n-form :model="formData" label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item :label="$t('strm.settings.serverName')" required>
          <n-input v-model:value="formData.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverType')" required>
          <div class="server-type-container">
            <n-radio-group v-model:value="formData.server_type" class="server-type-radio-group">
              <n-radio-button value="cd2host" class="server-type-button">
                <div class="server-type-content download-server">
                  <div class="icon-container">
                    <Icon icon="ic:round-cloud-download" class="server-type-icon" />
                  </div>
                  <span>{{ $t('strm.settings.downloadServer') }}</span>
                </div>
              </n-radio-button>
              <n-radio-button value="xiaoyahost" class="server-type-button">
                <div class="server-type-content media-server">
                  <div class="icon-container">
                    <Icon icon="ic:round-smart-display" class="server-type-icon" />
                  </div>
                  <span>{{ $t('strm.settings.mediaServer') }}</span>
                </div>
              </n-radio-button>
            </n-radio-group>
          </div>
        </n-form-item>

        <n-form-item :label="$t('strm.settings.baseUrl')" required>
          <n-input v-model:value="formData.base_url" @input="handleUrlInput"
            :placeholder="$t('strm.settings.baseUrlPlaceholder')" />
          <template #feedback v-if="!urlValid">
            <span class="text-error text-sm">请输入有效的服务器地址</span>
          </template>
        </n-form-item>

        <n-form-item :label="$t('strm.settings.description')">
          <n-input v-model:value="formData.description" type="textarea"
            :placeholder="$t('strm.settings.descriptionPlaceholder')" />
        </n-form-item>
      </n-form>

      <template #footer>
        <div class="flex justify-between items-center">
          <n-button type="info" :loading="testing" @click="handleTestNewServer"
            :disabled="!urlValid || !formData.base_url">
            <template #icon>
              <Icon icon="lucide:activity" />
            </template>
            {{ $t('strm.settings.test') }}
          </n-button>

          <div class="space-x-2">
            <n-button @click="showEditModal = false">{{ $t('strm.settings.cancel') }}</n-button>
            <n-button type="primary" :loading="submitting" @click="handleSave(false)">
              {{ $t('strm.settings.save') }}
            </n-button>
          </div>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<style scoped>
.server-management {
  width: 100%;
}

.server-table-container {
  width: 100%;
  margin-top: 1rem;
  overflow-x: auto !important;
  min-height: 200px;
  position: relative;
}

/* 使用更强的CSS选择器确保样式覆盖 */
.server-table-container :deep(.n-data-table) {
  width: 100% !important;
  max-height: none !important;
  /* 确保表格没有被限制高度 */
}

.server-table-container :deep(.n-data-table-th) {
  font-weight: 600 !important;
}

.server-table-container :deep(.n-data-table-tr:hover) {
  background-color: rgba(0, 128, 255, 0.05) !important;
}

.server-table-container :deep(.n-button) {
  margin: 2px !important;
  /* 减小按钮间距 */
}

.server-table-container :deep(.n-tag) {
  padding: 0 10px !important;
}

/* 新增样式以确保操作列不被截断 */
.server-table-container :deep(.n-data-table-td) {
  white-space: nowrap !important;
  overflow: visible !important;
}

.server-table-container :deep(.n-data-table-td:last-child) {
  padding: 8px 16px !important;
  min-width: 280px !important;
  /* 确保列宽足够 */
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
  /* 确保空状态在适当层级显示 */
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 美化服务器类型选择 */
.server-type-container {
  width: 100%;
  padding: 16px 0;
}

.server-type-radio-group {
  width: 100%;
  display: flex;
}

.server-type-radio-group :deep(.n-radio-group) {
  display: flex;
  width: 100%;
  gap: 16px;
}

.server-type-button {
  flex: 1;
  height: auto;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.server-type-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.server-type-button :deep(.n-radio-button__state-border) {
  border-radius: 8px;
  border-width: 2px;
  z-index: 1;
}

.server-type-button :deep(.n-radio-button__input:checked + .n-radio-button__state-border) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.server-type-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 8px;
  gap: 12px;
  min-height: 120px;
  width: 100%;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
}

.server-type-icon {
  font-size: 48px;
  line-height: 1;
}

.download-server .server-type-icon {
  color: #1890ff;
}

.media-server .server-type-icon {
  color: #52b54b;
}

.text-error {
  color: var(--error-color, #f56c6c);
}
</style>
