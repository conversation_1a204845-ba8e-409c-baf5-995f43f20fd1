<template>
  <div class="file-tree-view">
    <n-tree
      block-line
      :data="treeData"
      :default-expanded-keys="defaultExpandedKeys"
      :expanded-keys="expandedKeys"
      :pattern="searchPattern"
      :filter="handleTreeFilter"
      :selectable="false"
      :render-label="renderTreeLabel"
      @update:expanded-keys="handleUpdateExpandedKeys"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, h, ref, watch, type PropType } from 'vue';
import { NTree, type TreeOption } from 'naive-ui';
import { renderIcon } from '@/utils/common';

// 定义图标组件
const FolderIcon = () => h('span', { class: 'i-mdi:folder text-16px' });
const FolderOpenIcon = () => h('span', { class: 'i-mdi:folder-open text-16px' });

// 当前展开的节点
const expandedKeys = ref<string[]>([]);

const props = defineProps({
  files: {
    type: Array as PropType<StrmAPI.ParsedFile[]>,
    required: true
  },
  fileTypeFilter: {
    type: String,
    default: 'all'
  },
  searchValue: {
    type: String,
    default: ''
  }
});

// 搜索模式字符串
const searchPattern = computed(() => props.searchValue);

// 扁平文件列表转为树结构
const treeData = computed<TreeOption[]>(() => {
  // 跟踪所有已处理的目录
  const dirMap = new Map<string, TreeOption>();
  const root: TreeOption = { key: 'root', label: '根目录', children: [] };
  dirMap.set('root', root);

  // 过滤文件类型
  const filteredFiles = props.fileTypeFilter === 'all'
    ? props.files
    : props.files.filter(file => file.file_type === props.fileTypeFilter);

  // 处理每个文件，构建树结构
  filteredFiles.forEach(file => {
    // 获取文件目录路径
    const dirPath = file.directory || '/';

    // 将路径拆分为多个部分
    const pathParts = dirPath.split('/').filter(Boolean);

    // 跟踪当前路径，用于构建完整路径
    let currentPath = 'root';
    let parentNode = root;

    // 为每一级路径创建树节点
    for (const part of pathParts) {
      // 构建当前完整路径
      currentPath = currentPath === 'root' ? part : `${currentPath}/${part}`;

      // 如果此目录节点尚未创建，创建它
      if (!dirMap.has(currentPath)) {
        const newNode: TreeOption = {
          key: currentPath,
          label: part,
          type: 'directory',
          children: []
        };

        // 将新节点添加到父节点的子节点列表中
        if (!parentNode.children) {
          parentNode.children = [];
        }
        parentNode.children.push(newNode);

        // 将新节点添加到目录映射中
        dirMap.set(currentPath, newNode);
      }

      // 更新父节点为当前节点，为下一级路径做准备
      parentNode = dirMap.get(currentPath)!;
    }

    // 添加文件节点
    if (!parentNode.children) {
      parentNode.children = [];
    }

    const fileNode: TreeOption = {
      key: `${currentPath}/${file.file_name}`,
      label: file.file_name,
      type: 'file',
      fileType: file.file_type,
      extension: file.extension,
      isLeaf: true,
      prefix: () => getFileIcon(file.file_type, file.extension)
    };

    parentNode.children.push(fileNode);
  });

  // 获取一级子节点作为树根
  return root.children || [];
});

// 获取文件图标
function getFileIcon(fileType: string, extension: string) {
  const iconMap: Record<string, string> = {
    'video': 'mdi:file-video',
    'audio': 'mdi:file-music',
    'image': 'mdi:file-image',
    'subtitle': 'mdi:file-document',
    'metadata': 'mdi:file-code',
    'other': 'mdi:file'
  };

  return renderIcon(iconMap[fileType] || 'mdi:file');
}

// 渲染树节点标签
function renderTreeLabel({ option }: { option: TreeOption }) {
  if (option.type === 'directory') {
    return h(
      'div',
      { class: 'flex items-center' },
      [
        expandedKeys.value.includes(option.key as string)
          ? h(FolderOpenIcon, { class: 'mr-1' })
          : h(FolderIcon, { class: 'mr-1' }),
        h('span', null, option.label as string)
      ]
    );
  }

  return h(
    'div',
    { class: 'flex items-center' },
    [
      option.prefix ? option.prefix() : null,
      h('span', { class: 'ml-1' }, option.label as string),
      option.fileType ? h('span', { class: 'ml-2 text-xs text-secondary-text' }, `(${option.extension})`) : null
    ]
  );
}

// 默认展开的节点
const defaultExpandedKeys = ref<string[]>([]);
// 当前展开的节点
const expandedKeys = ref<string[]>([]);
// 更新展开的节点
const handleUpdateExpandedKeys = (keys: string[]) => {
  expandedKeys.value = keys;
};

// 文件筛选函数
const handleTreeFilter = (pattern: string, option: TreeOption) => {
  if (option.type === 'directory') {
    // 如果是目录，检查是否有匹配的子节点
    return true;
  }

  // 文件节点按名称匹配
  return (option.label as string).toLowerCase().includes(pattern.toLowerCase());
};

// 当文件列表或筛选条件变化时，重置展开节点
watch([() => props.files, () => props.fileTypeFilter], () => {
  // 默认展开第一级目录
  if (treeData.value.length > 0) {
    const firstLevelKeys = treeData.value.map(node => node.key as string);
    defaultExpandedKeys.value = firstLevelKeys;
    expandedKeys.value = firstLevelKeys;
  }
}, { immediate: true });

// 当搜索值变化时，自动展开所有节点以显示匹配结果
watch(() => props.searchValue, (value) => {
  if (value) {
    // 搜索时展开所有节点
    const getAllKeys = (nodes: TreeOption[]): string[] => {
      let keys: string[] = [];
      nodes.forEach(node => {
        if (node.key) keys.push(node.key as string);
        if (node.children && node.children.length > 0) {
          keys = [...keys, ...getAllKeys(node.children)];
        }
      });
      return keys;
    };
    expandedKeys.value = getAllKeys(treeData.value);
  } else {
    // 搜索清空时只展开第一级
    const firstLevelKeys = treeData.value.map(node => node.key as string);
    expandedKeys.value = firstLevelKeys;
  }
});
</script>

<style scoped>
.file-tree-view {
  width: 100%;
  height: 100%;
  overflow: auto;
}
</style>
