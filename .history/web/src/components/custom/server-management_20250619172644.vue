<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue';
import {
  NButton,
  NTable,
  NTag,
  NPopconfirm,
  NPopover,
  NForm,
  NFormItem,
  NInput,
  NSwitch,
  NSelect,
  NModal,
  useMessage,
  NSpace,
  NDataTable
} from 'naive-ui';
import {
  getMediaServers,
  createMediaServer,
  updateMediaServer,
  deleteMediaServer,
  testServerConnection
} from '@/service/api/strm';
import { $t } from '@/locales';

// 组件属性定义
defineOptions({ name: 'ServerManagement' });

// 服务器类型选项
const serverTypeOptions = [
  { label: $t('strm.settings.http' as any), value: 'http' },
  { label: $t('strm.settings.https' as any), value: 'https' },
  { label: $t('strm.settings.local' as any), value: 'local' },
  { label: $t('strm.settings.ftp' as any), value: 'ftp' },
  { label: $t('strm.settings.webdav' as any), value: 'webdav' },
  { label: $t('strm.settings.cd2host' as any), value: 'cd2host' },
  { label: $t('strm.settings.xiaoyahost' as any), value: 'xiaoyahost' }
];

// 消息服务
const message = useMessage();

// 加载状态
const loading = ref(false);
const submitting = ref(false);
const testing = ref(false);

// 表格列定义
const columns = [
  { title: $t('strm.settings.serverName' as any), key: 'name' },
  {
    title: $t('strm.settings.serverType' as any),
    key: 'server_type',
    render: (row: Record<string, any>) => {
      const serverTypes: Record<string, string> = {
        http: 'HTTP',
        https: 'HTTPS',
        local: $t('strm.settings.local' as any),
        ftp: 'FTP',
        webdav: 'WebDAV',
        cd2host: $t('strm.settings.cd2host' as any),
        xiaoyahost: $t('strm.settings.xiaoyahost' as any)
      };
      const type = serverTypes[row.server_type.toLowerCase()] || row.server_type;

      let tagType: 'info' | 'warning' | 'success' = 'info';
      if (row.server_type.toLowerCase() === 'cd2host') {
        tagType = 'warning';
      } else if (row.server_type.toLowerCase() === 'xiaoyahost') {
        tagType = 'success';
      }

      return h(NTag, { type: tagType }, { default: () => type });
    }
  },
  { title: 'URL', key: 'base_url' },
  {
    title: '状态',
    key: 'status',
    render: (row: Record<string, any>) => {
      const status = row.status || 'unknown';
      const tagProps: Record<string, { type: 'default' | 'success' | 'error' | 'warning'; text: string }> = {
        unknown: { type: 'default', text: '未测试' },
        success: { type: 'success', text: '正常' },
        error: { type: 'error', text: '异常' },
        warning: { type: 'warning', text: '警告' }
      };

      const tagConfig = tagProps[status] || tagProps.unknown;
      return h(NTag, { type: tagConfig.type }, { default: () => tagConfig.text });
    }
  },
  {
    title: '操作',
    key: 'actions',
    render: (row: Record<string, any>) => {
      return h(
        NSpace,
        { align: 'center' },
        {
          default: () => [
            h(
              NButton,
              {
                size: 'small',
                onClick: () => handleEdit(row)
              },
              { default: () => '编辑' }
            ),
            h(
              NButton,
              {
                size: 'small',
                type: 'primary',
                onClick: () => handleTestConnection(row.id)
              },
              { default: () => '测试连接' }
            ),
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDelete(row.id),
                negativeText: '取消',
                positiveText: '确认'
              },
              {
                trigger: () =>
                  h(
                    NButton,
                    {
                      size: 'small',
                      type: 'error'
                    },
                    { default: () => '删除' }
                  ),
                default: () => '确认删除此服务器?'
              }
            )
          ]
        }
      );
    }
  }
];

// 定义服务器类型接口
interface ServerItem {
  id: number;
  name: string;
  server_type: string;
  base_url: string;
  description?: string;
  auth_required: boolean;
  username?: string;
  password?: string;
  create_time: string;
  status: 'unknown' | 'success' | 'error' | 'warning';
}

// 数据
const servers = ref<ServerItem[]>([]);
const showAddModal = ref(false);
const showEditModal = ref(false);

// 表单数据
const formData = reactive({
  id: null as number | null,
  name: '',
  server_type: 'HTTP',
  base_url: '',
  description: '',
  auth_required: false,
  username: '',
  password: ''
});

// 获取服务器列表
async function fetchServers() {
  try {
    loading.value = true;
    const res = await getMediaServers();
    if (res.data && Array.isArray(res.data)) {
      servers.value = res.data.map((server: Record<string, any>) => ({
        ...server,
        status: 'unknown' // 初始状态为未测试
      }));
    }
  } catch (error: any) {
    message.error(error.message || '获取服务器列表失败');
  } finally {
    loading.value = false;
  }
}

// 添加服务器
function handleAdd() {
  // 重置表单
  Object.assign(formData, {
    id: null,
    name: '',
    server_type: 'HTTP',
    base_url: '',
    description: '',
    auth_required: false,
    username: '',
    password: ''
  });
  showAddModal.value = true;
}

// 编辑服务器
function handleEdit(row: ServerItem) {
  // 填充表单
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    server_type: row.server_type,
    base_url: row.base_url,
    description: row.description || '',
    auth_required: row.auth_required,
    username: row.username || '',
    password: ''  // 不显示密码
  });
  showEditModal.value = true;
}

// 测试服务器连接
async function handleTestConnection(serverId: number) {
  try {
    testing.value = true;

    // 查找服务器索引
    const serverIndex = servers.value.findIndex(s => s.id === serverId);
    if (serverIndex === -1) {
      message.error('找不到指定的服务器');
      return;
    }

    // 更新状态为测试中
    servers.value[serverIndex].status = 'unknown';

    // 发送测试请求
    const res = await testServerConnection(serverId);

    // 更新服务器状态
    if (res.data && res.data.status) {
      servers.value[serverIndex].status = res.data.status as 'success' | 'error' | 'warning';
      message.success(res.data.message || '连接测试完成');
    } else {
      servers.value[serverIndex].status = 'error';
      message.error(res.data?.message || '连接测试失败');
    }
  } catch (error: any) {
    message.error(error.message || '测试连接失败');
  } finally {
    testing.value = false;
  }
}

// 保存服务器
async function handleSave(isNew = false) {
  try {
    submitting.value = true;

    // 构造请求数据
    const data = {
      name: formData.name,
      server_type: formData.server_type,
      base_url: formData.base_url,
      description: formData.description || null,
      auth_required: formData.auth_required,
      username: formData.auth_required ? formData.username : null,
      password: formData.auth_required && formData.password ? formData.password : null
    };

    if (isNew) {
      // 新增
      await createMediaServer(data);
      message.success('服务器添加成功');
      showAddModal.value = false;
    } else {
      // 更新
      await updateMediaServer(formData.id, data);
      message.success('服务器更新成功');
      showEditModal.value = false;
    }

    // 刷新列表
    await fetchServers();
  } catch (error: any) {
    message.error(error.message || (isNew ? '添加服务器失败' : '更新服务器失败'));
  } finally {
    submitting.value = false;
  }
}

// 删除服务器
async function handleDelete(id: number) {
  try {
    await deleteMediaServer(id);
    message.success('服务器删除成功');
    await fetchServers();
  } catch (error: any) {
    message.error(error.message || '删除服务器失败');
  }
}

// 生命周期
onMounted(() => {
  fetchServers();
});
</script>

<template>
  <div class="server-management">
    <div class="flex justify-between mb-4">
      <h3>{{ $t('strm.settings.serverManagement') }}</h3>
      <n-button type="primary" @click="handleAdd">
        {{ $t('strm.settings.addServer') }}
      </n-button>
    </div>

    <n-data-table :loading="loading" :columns="columns" :data="servers" :pagination="{ pageSize: 10 }" />

    <!-- 添加服务器对话框 -->
    <n-modal v-model:show="showAddModal" :title="$t('strm.settings.addServer')" preset="card" style="width: 600px">
      <n-form label-placement="left" label-width="auto" :model="formData" :disabled="submitting">
        <n-form-item :label="$t('strm.settings.serverName')" required>
          <n-input v-model:value="formData.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverType')" required>
          <n-select v-model:value="formData.server_type" :options="serverTypeOptions"
            :placeholder="$t('strm.settings.serverType')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverUrl')" required>
          <n-input v-model:value="formData.base_url" :placeholder="$t('strm.settings.serverUrlPlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.description')">
          <n-input v-model:value="formData.description" type="textarea"
            :placeholder="$t('strm.settings.descriptionPlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.authRequired')">
          <n-switch v-model:value="formData.auth_required" />
        </n-form-item>

        <template v-if="formData.auth_required">
          <n-form-item :label="$t('strm.settings.username')">
            <n-input v-model:value="formData.username" :placeholder="$t('strm.settings.usernamePlaceholder')" />
          </n-form-item>

          <n-form-item :label="$t('strm.settings.password')">
            <n-input v-model:value="formData.password" type="password"
              :placeholder="$t('strm.settings.passwordPlaceholder')" show-password-on="click" />
          </n-form-item>
        </template>

        <div class="flex justify-end gap-2 mt-4">
          <n-button @click="showAddModal = false">{{ $t('strm.settings.cancel') }}</n-button>
          <n-button type="primary" :loading="submitting" @click="handleSave(true)">
            {{ $t('strm.settings.confirm') }}
          </n-button>
        </div>
      </n-form>
    </n-modal>

    <!-- 编辑服务器对话框 -->
    <n-modal v-model:show="showEditModal" :title="$t('strm.settings.editServer')" preset="card" style="width: 600px">
      <n-form label-placement="left" label-width="auto" :model="formData" :disabled="submitting">
        <n-form-item :label="$t('strm.settings.serverName')" required>
          <n-input v-model:value="formData.name" :placeholder="$t('strm.settings.serverNamePlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverType')" required>
          <n-select v-model:value="formData.server_type" :options="serverTypeOptions"
            :placeholder="$t('strm.settings.serverType')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.serverUrl')" required>
          <n-input v-model:value="formData.base_url" :placeholder="$t('strm.settings.serverUrlPlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.description')">
          <n-input v-model:value="formData.description" type="textarea"
            :placeholder="$t('strm.settings.descriptionPlaceholder')" />
        </n-form-item>

        <n-form-item :label="$t('strm.settings.authRequired')">
          <n-switch v-model:value="formData.auth_required" />
        </n-form-item>

        <template v-if="formData.auth_required">
          <n-form-item :label="$t('strm.settings.username')">
            <n-input v-model:value="formData.username" :placeholder="$t('strm.settings.usernamePlaceholder')" />
          </n-form-item>

          <n-form-item :label="$t('strm.settings.password')">
            <n-input v-model:value="formData.password" type="password"
              :placeholder="$t('strm.settings.passwordPlaceholder')" show-password-on="click" />
          </n-form-item>
        </template>

        <div class="flex justify-end gap-2 mt-4">
          <n-button @click="showEditModal = false">{{ $t('strm.settings.cancel') }}</n-button>
          <n-button type="primary" :loading="submitting" @click="handleSave(false)">
            {{ $t('strm.settings.confirm') }}
          </n-button>
        </div>
      </n-form>
    </n-modal>
  </div>
</template>

<style scoped>
.server-management {
  width: 100%;
}
</style>
