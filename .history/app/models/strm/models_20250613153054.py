from enum import Enum
from tortoise import fields

from app.models.system.utils import BaseModel, TimestampMixin, StrEnum


class ServerType(StrEnum):
    HTTP = "http"
    HTTPS = "https"
    LOCAL = "local"
    FTP = "ftp"
    WEBDAV = "webdav"


class TaskStatus(StrEnum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"


class FileType(StrEnum):
    VIDEO = "video"
    AUDIO = "audio"
    IMAGE = "image"
    SUBTITLE = "subtitle"
    OTHER = "other"


class MediaServer(BaseModel, TimestampMixin):
    """媒体服务器配置"""
    id = fields.IntField(pk=True, description="服务器ID")
    name = fields.CharField(max_length=100, description="服务器名称")
    server_type = fields.CharEnumField(ServerType, default=ServerType.HTTP, description="服务器类型")
    base_url = fields.CharField(max_length=500, description="服务器基础URL")
    is_default = fields.BooleanField(default=False, description="是否为默认服务器")
    description = fields.TextField(null=True, description="服务器描述")
    auth_required = fields.BooleanField(default=False, description="是否需要认证")
    username = fields.CharField(max_length=100, null=True, description="认证用户名")
    password = fields.CharField(max_length=100, null=True, description="认证密码")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_servers", description="创建者")

    class Meta:
        table = "strm_media_servers"
        table_description = "媒体服务器配置表"
        default_connection = "conn_system"


class PathMapping(BaseModel, TimestampMixin):
    """路径映射规则"""
    id = fields.IntField(pk=True, description="映射规则ID")
    name = fields.CharField(max_length=100, description="规则名称")
    source_path = fields.CharField(max_length=500, description="源路径")
    target_path = fields.CharField(max_length=500, description="目标路径")
    server = fields.ForeignKeyField("app_system.MediaServer", related_name="path_mappings", description="所属服务器")
    is_regex = fields.BooleanField(default=False, description="是否使用正则表达式")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_path_mappings", description="创建者")

    class Meta:
        table = "strm_path_mappings"
        table_description = "路径映射规则表"
        default_connection = "conn_system"


class FilterRule(BaseModel, TimestampMixin):
    """文件过滤规则"""
    id = fields.IntField(pk=True, description="过滤规则ID")
    name = fields.CharField(max_length=100, description="规则名称")
    file_type = fields.CharEnumField(FileType, null=True, description="文件类型")
    keyword = fields.CharField(max_length=200, null=True, description="关键词")
    path_pattern = fields.CharField(max_length=500, null=True, description="路径模式")
    is_include = fields.BooleanField(default=True, description="是包含规则还是排除规则")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_filter_rules", description="创建者")

    class Meta:
        table = "strm_filter_rules"
        table_description = "文件过滤规则表"
        default_connection = "conn_system"


class StrmTask(BaseModel, TimestampMixin):
    """STRM生成任务"""
    id = fields.IntField(pk=True, description="任务ID")
    name = fields.CharField(max_length=200, description="任务名称")
    status = fields.CharEnumField(TaskStatus, default=TaskStatus.PENDING, description="任务状态")
    server = fields.ForeignKeyField("app_system.MediaServer", related_name="tasks", description="使用的服务器")
    source_file = fields.CharField(max_length=500, description="源文件路径")
    output_dir = fields.CharField(max_length=500, description="输出目录")
    total_files = fields.IntField(default=0, description="总文件数")
    processed_files = fields.IntField(default=0, description="已处理文件数")
    success_files = fields.IntField(default=0, description="成功生成文件数")
    failed_files = fields.IntField(default=0, description="失败文件数")
    start_time = fields.DatetimeField(null=True, description="开始时间")
    end_time = fields.DatetimeField(null=True, description="结束时间")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_tasks", description="创建者")
    log_file = fields.CharField(max_length=500, null=True, description="日志文件路径")
    
    class Meta:
        table = "strm_tasks"
        table_description = "STRM生成任务表"
        default_connection = "conn_system"


class StrmFile(BaseModel, TimestampMixin):
    """生成的STRM文件记录"""
    id = fields.IntField(pk=True, description="文件ID")
    task = fields.ForeignKeyField("app_system.StrmTask", related_name="strm_files", description="所属任务")
    source_path = fields.CharField(max_length=1000, description="源文件路径")
    target_path = fields.CharField(max_length=1000, description="生成的STRM文件路径")
    file_type = fields.CharEnumField(FileType, description="文件类型")
    file_size = fields.BigIntField(null=True, description="文件大小(字节)")
    is_success = fields.BooleanField(default=True, description="是否生成成功")
    error_message = fields.TextField(null=True, description="错误信息")
    
    class Meta:
        table = "strm_files"
        table_description = "STRM文件记录表"
        default_connection = "conn_system"


__all__ = [
    "ServerType", 
    "TaskStatus", 
    "FileType", 
    "MediaServer", 
    "PathMapping", 
    "FilterRule", 
    "StrmTask", 
    "StrmFile"
] 