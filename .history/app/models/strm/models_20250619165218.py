from enum import Enum
from tortoise import fields

from app.models.system.utils import BaseModel, TimestampMixin, StrEnum


class ServerType(StrEnum):
    HTTP = "http"
    HTTPS = "https"
    LOCAL = "local"
    FTP = "ftp"
    WEBDAV = "webdav"
    CD2HOST = "cd2host"  # 下载服务器类型
    XIAOYAHOST = "xiaoyahost"  # 媒体服务器类型


class TaskStatus(StrEnum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELED = "canceled"


class FileType(StrEnum):
    VIDEO = "video"
    AUDIO = "audio"
    IMAGE = "image"
    SUBTITLE = "subtitle"
    METADATA = "metadata"
    OTHER = "other"


class MediaServer(BaseModel, TimestampMixin):
    """媒体服务器配置"""

    id = fields.IntField(pk=True, description="服务器ID")
    name = fields.CharField(max_length=100, description="服务器名称")
    server_type = fields.CharEnumField(ServerType, default=ServerType.HTTP, description="服务器类型")
    base_url = fields.CharField(max_length=500, description="服务器基础URL")
    is_default = fields.BooleanField(default=False, description="是否为默认服务器")
    description = fields.TextField(null=True, description="服务器描述")
    auth_required = fields.BooleanField(default=False, description="是否需要认证")
    username = fields.CharField(max_length=100, null=True, description="认证用户名")
    password = fields.CharField(max_length=100, null=True, description="认证密码")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_servers", description="创建者")

    class Meta:
        table = "strm_media_servers"
        table_description = "媒体服务器配置表"
        default_connection = "conn_system"


class PathMapping(BaseModel, TimestampMixin):
    """路径映射规则"""

    id = fields.IntField(pk=True, description="映射规则ID")
    name = fields.CharField(max_length=100, description="规则名称")
    source_path = fields.CharField(max_length=500, description="源路径")
    target_path = fields.CharField(max_length=500, description="目标路径")
    server = fields.ForeignKeyField("app_system.MediaServer", related_name="path_mappings", description="所属服务器")
    is_regex = fields.BooleanField(default=False, description="是否使用正则表达式")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_path_mappings", description="创建者")

    class Meta:
        table = "strm_path_mappings"
        table_description = "路径映射规则表"
        default_connection = "conn_system"


class FilterRule(BaseModel, TimestampMixin):
    """文件过滤规则"""

    id = fields.IntField(pk=True, description="过滤规则ID")
    name = fields.CharField(max_length=100, description="规则名称")
    file_type = fields.CharEnumField(FileType, null=True, description="文件类型")
    keyword = fields.CharField(max_length=200, null=True, description="关键词")
    path_pattern = fields.CharField(max_length=500, null=True, description="路径模式")
    is_include = fields.BooleanField(default=True, description="是包含规则还是排除规则")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_filter_rules", description="创建者")

    class Meta:
        table = "strm_filter_rules"
        table_description = "文件过滤规则表"
        default_connection = "conn_system"


class SystemSettings(BaseModel, TimestampMixin):
    """系统设置"""

    id = fields.IntField(pk=True, description="设置ID")
    default_server = fields.ForeignKeyField(
        "app_system.MediaServer", related_name="default_in_settings", null=True, description="默认服务器（旧版本兼容）"
    )
    default_download_server = fields.ForeignKeyField(
        "app_system.MediaServer", related_name="default_download_in_settings", null=True, description="默认下载服务器"
    )
    default_media_server = fields.ForeignKeyField(
        "app_system.MediaServer", related_name="default_media_in_settings", null=True, description="默认媒体服务器"
    )
    enable_path_replacement = fields.BooleanField(default=True, description="启用路径替换")
    replacement_path = fields.CharField(max_length=500, null=True, default="/nas", description="路径替换值")
    download_threads = fields.IntField(default=1, description="默认下载线程数")
    output_directory = fields.CharField(max_length=500, null=True, description="默认输出目录")
    updated_by = fields.ForeignKeyField("app_system.User", related_name="updated_settings", description="更新人")

    class Meta:
        table = "strm_system_settings"
        table_description = "系统设置表"
        default_connection = "conn_system"


__all__ = ["ServerType", "TaskStatus", "FileType", "MediaServer", "PathMapping", "FilterRule", "SystemSettings"]
