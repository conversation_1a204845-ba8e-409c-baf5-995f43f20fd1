from tortoise import fields

from app.models.system.utils import BaseModel, TimestampMixin
from .models import TaskStatus


class StrmTask(BaseModel, TimestampMixin):
    """STRM生成任务"""
    id = fields.IntField(pk=True, description="任务ID")
    name = fields.CharField(max_length=200, description="任务名称")
    status = fields.CharEnumField(TaskStatus, default=TaskStatus.PENDING, description="任务状态")
    server = fields.ForeignKeyField("app_system.MediaServer", related_name="tasks", description="使用的服务器")
    source_file = fields.CharField(max_length=500, description="源文件路径")
    output_dir = fields.CharField(max_length=500, description="输出目录")
    total_files = fields.IntField(default=0, description="总文件数")
    processed_files = fields.IntField(default=0, description="已处理文件数")
    success_files = fields.IntField(default=0, description="成功生成文件数")
    failed_files = fields.IntField(default=0, description="失败文件数")
    start_time = fields.DatetimeField(null=True, description="开始时间")
    end_time = fields.DatetimeField(null=True, description="结束时间")
    created_by = fields.ForeignKeyField("app_system.User", related_name="created_tasks", description="创建者")
    log_file = fields.CharField(max_length=500, null=True, description="日志文件路径")
    
    class Meta:
        table = "strm_tasks"
        table_description = "STRM生成任务表"


__all__ = ["StrmTask"] 