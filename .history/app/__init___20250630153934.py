from contextlib import asynccontextmanager
from datetime import datetime
import asyncio

from fastapi import <PERSON><PERSON><PERSON>
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from redis import asyncio as aioredis
from starlette.staticfiles import StaticFiles

from app.api.v1.utils import refresh_api_list
from app.core.exceptions import SettingNotFound
from app.core.init_app import (
    init_menus,
    init_users,
    make_middlewares,
    modify_db,
    register_db,
    register_exceptions,
    register_routers,
    init_system_settings,
)
from app.log import log
from app.models.system import Log
from app.models.system import LogType, LogDetailType

try:
    from app.settings import APP_SETTINGS
except ImportError:
    raise SettingNotFound("Can not import settings")


def create_app() -> FastAPI:
    if APP_SETTINGS.DEBUG:
        _app = FastAPI(
            title=APP_SETTINGS.APP_TITLE,
            description=APP_SETTINGS.APP_DESCRIPTION,
            version=APP_SETTINGS.VERSION,
            openapi_url="/openapi.json",
            middleware=make_middlewares(),
            lifespan=lifespan,
        )
    else:
        _app = FastAPI(
            title=APP_SETTINGS.APP_TITLE,
            description=APP_SETTINGS.APP_DESCRIPTION,
            version=APP_SETTINGS.VERSION,
            openapi_url=None,
            middleware=make_middlewares(),
            lifespan=lifespan,
        )
    register_db(_app)
    register_exceptions(_app)
    register_routers(_app, prefix="/api")

    redis = aioredis.from_url(url=APP_SETTINGS.REDIS_URL)
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    return _app


@asynccontextmanager
async def lifespan(_app: FastAPI):
    start_time = datetime.now()
    try:
        from app.core.bgtask import BgTasks
        from app.core.task_monitor import setup_task_monitor

        log.info("开始初始化应用...")
        await modify_db()
        log.info("数据库迁移和升级已完成")

        await init_menus()
        log.info("菜单初始化完成")

        await refresh_api_list()
        log.info("API列表刷新完成")

        await init_users()
        log.info("用户初始化完成")

        await init_system_settings()
        log.info("系统设置初始化完成")

        # 初始化后台任务系统
        log.info("正在初始化后台任务系统...")
        await BgTasks.init_bg_tasks_obj()

        # 验证后台任务系统是否正常运行
        try:
            health_status = await BgTasks.get_health_status()
            log.info(f"后台任务系统健康状态: {health_status}")

            # 检查系统状态
            if health_status["status"] != "healthy":
                log.warning(f"后台任务系统状态不健康: {health_status['status']}，尝试重新初始化...")
                await BgTasks.init_bg_tasks_obj()

                # 再次检查状态
                health_status = await BgTasks.get_health_status()
                if health_status["status"] == "healthy":
                    log.info("后台任务系统重新初始化成功")
                else:
                    log.error(f"后台任务系统重新初始化后仍不健康: {health_status['status']}")
            else:
                log.info("后台任务系统运行正常")
        except Exception as e:
            import traceback

            error_detail = traceback.format_exc()
            log.error(f"验证后台任务系统时发生错误: {str(e)}\n{error_detail}")
            # 尝试重新初始化
            log.info("尝试重新初始化后台任务系统...")
            await BgTasks.init_bg_tasks_obj()

        # 启动任务监控系统
        log.info("正在启动任务监控系统...")
        await setup_task_monitor()

        log.info("应用初始化完成，等待请求...")
        await Log.create(log_type=LogType.SystemLog, log_detail_type=LogDetailType.SystemStart)
        yield

    finally:
        end_time = datetime.now()
        runtime = (end_time - start_time).total_seconds() / 60
        log.info(f"App {_app.title} runtime: {runtime} min")  # noqa
        await Log.create(log_type=LogType.SystemLog, log_detail_type=LogDetailType.SystemStop)


app = create_app()

app.mount("/static", StaticFiles(directory=APP_SETTINGS.STATIC_ROOT), name="static")
