from starlette.background import BackgroundTasks
import asyncio
import traceback
from .ctx import CTX_BG_TASKS
from app.log.log import log


class BgTasks:
    """后台任务统一管理"""

    @classmethod
    async def init_bg_tasks_obj(cls):
        """实例化后台任务，并设置到上下文"""
        bg_tasks = BackgroundTasks()
        CTX_BG_TASKS.set(bg_tasks)

    @classmethod
    async def get_bg_tasks_obj(cls):
        """从上下文中获取后台任务实例"""
        return CTX_BG_TASKS.get()

    @classmethod
    async def add_task(cls, func, *args, **kwargs):
        """添加后台任务"""
        bg_tasks = await cls.get_bg_tasks_obj()
        if not bg_tasks:
            log.error("无法添加后台任务: 未找到背景任务对象")
            return False

        # 封装任务以捕获所有错误
        async def safe_task_wrapper(*task_args, **task_kwargs):
            try:
                return await func(*task_args, **task_kwargs)
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"后台任务执行失败: {str(e)}\n{error_detail}")
                return None

        bg_tasks.add_task(safe_task_wrapper, *args, **kwargs)
        return True

    @classmethod
    async def execute_tasks(cls):
        """执行后台任务，一般是请求结果返回之后执行"""
        bg_tasks = await cls.get_bg_tasks_obj()
        if not bg_tasks:
            return

        if not bg_tasks.tasks:
            return

        try:
            # 对于时间敏感的任务，确保它们会在响应后立即执行
            await bg_tasks()
        except Exception as e:
            error_detail = traceback.format_exc()
            log.error(f"执行后台任务队列时发生错误: {str(e)}\n{error_detail}")
