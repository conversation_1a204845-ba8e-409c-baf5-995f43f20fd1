"""
后台任务调度模块，使用schedule库实现

使用示例:
```python
# 添加一次性任务
task_id = await BgTasks.add_task(my_async_function, arg1, arg2, kwarg1=value1)

# 添加定期执行的任务（每10分钟执行一次）
interval_task_id = await BgTasks.schedule_interval(10, 'minutes', my_function)

# 添加每天特定时间执行的任务
daily_task_id = await BgTasks.schedule_daily("10:30", my_function)

# 添加每周特定日期和时间执行的任务
weekly_task_id = await BgTasks.schedule_weekly("monday", "09:00", my_function)

# 取消任务
await BgTasks.cancel_task(task_id)
```
"""

import asyncio
import inspect
import threading
import time
import traceback
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List, Union, Tuple

# 优雅地处理schedule导入，如果不存在则告知用户
try:
    import schedule
except ImportError:
    raise ImportError(
        "schedule库未安装，请使用 'pip install schedule>=1.2.0' 安装。"
        "然后在pyproject.toml的依赖项中添加 'schedule>=1.2.0'"
    )

from starlette.background import BackgroundTasks

from .ctx import CTX_BG_TASKS
from app.log.log import log


class BgTasks:
    """后台任务统一管理，使用schedule库实现调度"""

    _instance = None  # 用于保存全局后台任务对象，解决上下文丢失问题
    _jobs = {}  # 保存所有任务的映射表，键为任务ID，值为任务信息
    _lock = threading.RLock()  # 线程锁，确保线程安全
    _schedule_thread = None  # schedule运行线程
    _running = False  # 运行状态标记
    _last_activity = time.time()  # 最后活动时间
    _health_check_interval = 60  # 健康检查间隔（秒）
    _health_check_task = None  # 健康检查任务
    _job_counter = 0  # 任务计数器，用于生成唯一ID
    _scheduler = schedule.Scheduler()  # schedule调度器实例

    @classmethod
    async def init_bg_tasks_obj(cls):
        """实例化后台任务，并设置到上下文"""
        with cls._lock:
            bg_tasks = BackgroundTasks()
            CTX_BG_TASKS.set(bg_tasks)
            if cls._instance is None:
                cls._instance = bg_tasks
                # 启动调度线程
                cls._start_schedule_thread()
                # 启动健康检查任务
                cls._health_check_task = asyncio.create_task(cls._health_checker())
                log.info("后台任务系统已初始化，使用schedule库，包含健康检查机制")

    @classmethod
    def _start_schedule_thread(cls):
        """启动schedule调度线程"""
        if cls._schedule_thread is None or not cls._schedule_thread.is_alive():
            cls._running = True
            cls._schedule_thread = threading.Thread(target=cls._run_schedule, daemon=True)
            cls._schedule_thread.start()
            log.info("后台任务调度线程已启动")

    @classmethod
    def _run_schedule(cls):
        """运行schedule调度循环的线程函数"""
        while cls._running:
            try:
                # 更新活动时间
                cls._last_activity = time.time()
                # 运行待执行的任务
                cls._scheduler.run_pending()
                # 短暂休眠，避免CPU占用过高
                time.sleep(1)
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"调度线程运行时发生错误: {str(e)}\n{error_detail}")
                time.sleep(5)  # 出错后等待较长时间再继续

    @classmethod
    async def _health_checker(cls):
        """定期检查后台任务系统的健康状态"""
        log.info("后台任务健康检查器已启动")
        while True:
            try:
                await asyncio.sleep(cls._health_check_interval)

                # 检查调度线程是否运行
                if not cls._running or cls._schedule_thread is None or not cls._schedule_thread.is_alive():
                    log.warning("健康检查：后台调度线程未运行或已停止，正在尝试重启...")
                    cls._start_schedule_thread()
                    log.info("健康检查：后台调度线程已重启")

                # 检查活动状态
                inactivity_time = time.time() - cls._last_activity

                # 记录健康状态
                health_status = {
                    "running": cls._running,
                    "schedule_thread_alive": cls._schedule_thread.is_alive() if cls._schedule_thread else False,
                    "last_activity_seconds_ago": inactivity_time,
                    "jobs_count": len(cls._jobs),
                    "schedule_jobs_count": len(cls._scheduler.jobs),
                }

                log.info(f"后台任务系统健康状态: {health_status}")

                # 如果长时间无活动，可能需要干预
                if inactivity_time > 300:  # 5分钟无活动
                    log.warning(f"警告：调度线程已 {inactivity_time:.2f} 秒无活动，正在重启...")
                    with cls._lock:
                        cls._running = False
                        if cls._schedule_thread:
                            cls._schedule_thread.join(timeout=1.0)
                        cls._start_schedule_thread()
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"任务健康检查器发生错误: {str(e)}\n{error_detail}")
                await asyncio.sleep(10)  # 错误后短暂等待，避免过快重试

    @classmethod
    async def get_bg_tasks_obj(cls):
        """从上下文中获取后台任务实例，如果不存在则使用全局实例"""
        bg_tasks = CTX_BG_TASKS.get()
        if not bg_tasks:
            bg_tasks = cls._instance
        return bg_tasks

    @classmethod
    async def get_health_status(cls) -> Dict[str, Any]:
        """获取后台任务系统的健康状态"""
        with cls._lock:
            inactivity_time = time.time() - cls._last_activity
            jobs_info = []

            for job_id, job_info in cls._jobs.items():
                schedule_job = job_info.get("job")
                jobs_info.append(
                    {
                        "id": job_id,
                        "name": job_info.get("name", "unnamed_task"),
                        "type": job_info.get("type", "unknown"),
                        "next_run": schedule_job.next_run.strftime("%Y-%m-%d %H:%M:%S")
                        if hasattr(schedule_job, "next_run") and schedule_job.next_run
                        else None,
                        "last_run": job_info.get("last_run"),
                        "runs_count": job_info.get("runs_count", 0),
                    }
                )

            return {
                "status": "healthy"
                if cls._running and cls._schedule_thread and cls._schedule_thread.is_alive()
                else "unhealthy",
                "schedule_thread_alive": cls._schedule_thread.is_alive() if cls._schedule_thread else False,
                "running": cls._running,
                "jobs_count": len(cls._jobs),
                "schedule_jobs_count": len(cls._scheduler.jobs),
                "last_activity_seconds_ago": inactivity_time,
                "last_activity_time": datetime.fromtimestamp(cls._last_activity).isoformat(),
                "check_time": datetime.now().isoformat(),
                "jobs": jobs_info[:10],  # 仅返回前10个任务信息，避免响应过大
            }

    @classmethod
    async def add_task(cls, func, *args, **kwargs):
        """添加后台任务（立即执行一次）
        与原API兼容，将任务添加到调度器并立即执行一次
        """
        from app.log.log import log

        log.info(f"添加后台任务: {func.__name__}")

        # 为异步函数创建包装器
        wrapped_func = cls._wrap_async_function(func, *args, **kwargs)

        # 生成唯一任务ID
        with cls._lock:
            cls._job_counter += 1
            job_id = f"task_{cls._job_counter}_{int(time.time())}"

        # 创建立即执行的一次性任务
        def one_time_job():
            try:
                with cls._lock:
                    if job_id in cls._jobs:
                        job_info = cls._jobs[job_id]
                        job_info["last_run"] = datetime.now().isoformat()
                        job_info["runs_count"] += 1
                        cls._jobs[job_id] = job_info

                # 执行任务
                result = wrapped_func()
                log.info(f"后台任务执行成功: {func.__name__}, 结果: {result}")

                # 任务已完成，可以从列表中移除
                with cls._lock:
                    if job_id in cls._jobs:
                        del cls._jobs[job_id]

                return schedule.CancelJob  # 执行一次后取消任务
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"后台任务 {func.__name__} 执行失败: {str(e)}\n{error_detail}")

                # 即使任务失败，也应该将其标记为已完成
                with cls._lock:
                    if job_id in cls._jobs:
                        cls._jobs[job_id]["error"] = str(e)
                        cls._jobs[job_id]["error_detail"] = error_detail

                return schedule.CancelJob  # 执行一次后取消任务，即使失败

        # 将任务加入调度器，立即执行
        job = cls._scheduler.every(1).seconds.do(one_time_job)

        # 记录任务信息
        with cls._lock:
            cls._jobs[job_id] = {
                "name": getattr(func, "__name__", "unnamed_task"),
                "type": "one_time",
                "job": job,
                "created_at": datetime.now().isoformat(),
                "last_run": None,
                "runs_count": 0,
                "args": str(args)[:100] if args else None,
                "kwargs": str(kwargs)[:100] if kwargs else None,
            }

        return job_id

    @classmethod
    def _wrap_async_function(cls, func, *args, **kwargs):
        """包装异步函数，使其可以在同步环境中调用"""
        if inspect.iscoroutinefunction(func):
            # 异步函数需要在事件循环中运行
            def wrapper():
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，创建一个新的任务
                    future = asyncio.run_coroutine_threadsafe(func(*args, **kwargs), loop)
                    return future.result()
                else:
                    # 如果事件循环未运行，直接运行协程
                    return loop.run_until_complete(func(*args, **kwargs))

            return wrapper
        else:
            # 同步函数可以直接调用
            def wrapper():
                return func(*args, **kwargs)

            return wrapper

    @classmethod
    async def schedule_interval(cls, interval: int, unit: str, func, *args, **kwargs) -> str:
        """按固定间隔调度任务

        Args:
            interval: 时间间隔数值
            unit: 时间单位，可以是 'seconds', 'minutes', 'hours', 'days', 'weeks'
            func: 要执行的函数
            *args, **kwargs: 传递给函数的参数

        Returns:
            任务ID
        """
        from app.log.log import log

        log.info(f"添加定期任务: {func.__name__}, 间隔: {interval} {unit}")

        # 包装函数
        wrapped_func = cls._wrap_async_function(func, *args, **kwargs)

        # 生成唯一任务ID
        with cls._lock:
            cls._job_counter += 1
            job_id = f"interval_{cls._job_counter}_{int(time.time())}"

        # 创建任务执行包装器
        def task_wrapper():
            try:
                with cls._lock:
                    if job_id in cls._jobs:
                        job_info = cls._jobs[job_id]
                        job_info["last_run"] = datetime.now().isoformat()
                        job_info["runs_count"] += 1
                        cls._jobs[job_id] = job_info

                # 执行任务
                result = wrapped_func()
                log.info(f"定期任务执行成功: {func.__name__}, 结果: {result}")
                return result
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"定期任务 {func.__name__} 执行失败: {str(e)}\n{error_detail}")

                # 记录错误但不取消任务
                with cls._lock:
                    if job_id in cls._jobs:
                        cls._jobs[job_id]["last_error"] = str(e)
                        cls._jobs[job_id]["last_error_time"] = datetime.now().isoformat()

        # 根据时间单位设置调度
        job = None
        if unit == "seconds":
            job = cls._scheduler.every(interval).seconds.do(task_wrapper)
        elif unit == "minutes":
            job = cls._scheduler.every(interval).minutes.do(task_wrapper)
        elif unit == "hours":
            job = cls._scheduler.every(interval).hours.do(task_wrapper)
        elif unit == "days":
            job = cls._scheduler.every(interval).days.do(task_wrapper)
        elif unit == "weeks":
            job = cls._scheduler.every(interval).weeks.do(task_wrapper)
        else:
            raise ValueError(f"不支持的时间单位: {unit}")

        # 记录任务信息
        with cls._lock:
            cls._jobs[job_id] = {
                "name": getattr(func, "__name__", "unnamed_task"),
                "type": "interval",
                "interval": interval,
                "unit": unit,
                "job": job,
                "created_at": datetime.now().isoformat(),
                "last_run": None,
                "runs_count": 0,
                "args": str(args)[:100] if args else None,
                "kwargs": str(kwargs)[:100] if kwargs else None,
            }

        return job_id

    @classmethod
    async def schedule_daily(cls, time_str: str, func, *args, **kwargs) -> str:
        """每天特定时间执行任务

        Args:
            time_str: 执行时间，格式为 "HH:MM" 或 "HH:MM:SS"
            func: 要执行的函数
            *args, **kwargs: 传递给函数的参数

        Returns:
            任务ID
        """
        from app.log.log import log

        log.info(f"添加每日任务: {func.__name__}, 时间: {time_str}")

        # 包装函数
        wrapped_func = cls._wrap_async_function(func, *args, **kwargs)

        # 生成唯一任务ID
        with cls._lock:
            cls._job_counter += 1
            job_id = f"daily_{cls._job_counter}_{int(time.time())}"

        # 创建任务执行包装器
        def task_wrapper():
            try:
                with cls._lock:
                    if job_id in cls._jobs:
                        job_info = cls._jobs[job_id]
                        job_info["last_run"] = datetime.now().isoformat()
                        job_info["runs_count"] += 1
                        cls._jobs[job_id] = job_info

                # 执行任务
                result = wrapped_func()
                log.info(f"每日任务执行成功: {func.__name__}, 结果: {result}")
                return result
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"每日任务 {func.__name__} 执行失败: {str(e)}\n{error_detail}")

                # 记录错误但不取消任务
                with cls._lock:
                    if job_id in cls._jobs:
                        cls._jobs[job_id]["last_error"] = str(e)
                        cls._jobs[job_id]["last_error_time"] = datetime.now().isoformat()

        # 设置每日执行
        job = cls._scheduler.every().day.at(time_str).do(task_wrapper)

        # 记录任务信息
        with cls._lock:
            cls._jobs[job_id] = {
                "name": getattr(func, "__name__", "unnamed_task"),
                "type": "daily",
                "time": time_str,
                "job": job,
                "created_at": datetime.now().isoformat(),
                "last_run": None,
                "runs_count": 0,
                "args": str(args)[:100] if args else None,
                "kwargs": str(kwargs)[:100] if kwargs else None,
            }

        return job_id

    @classmethod
    async def schedule_weekly(cls, day: str, time_str: str, func, *args, **kwargs) -> str:
        """每周特定日期和时间执行任务

        Args:
            day: 星期几，可以是 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
            time_str: 执行时间，格式为 "HH:MM" 或 "HH:MM:SS"
            func: 要执行的函数
            *args, **kwargs: 传递给函数的参数

        Returns:
            任务ID
        """
        from app.log.log import log

        log.info(f"添加每周任务: {func.__name__}, 日期: {day}, 时间: {time_str}")

        # 包装函数
        wrapped_func = cls._wrap_async_function(func, *args, **kwargs)

        # 生成唯一任务ID
        with cls._lock:
            cls._job_counter += 1
            job_id = f"weekly_{cls._job_counter}_{int(time.time())}"

        # 创建任务执行包装器
        def task_wrapper():
            try:
                with cls._lock:
                    if job_id in cls._jobs:
                        job_info = cls._jobs[job_id]
                        job_info["last_run"] = datetime.now().isoformat()
                        job_info["runs_count"] += 1
                        cls._jobs[job_id] = job_info

                # 执行任务
                result = wrapped_func()
                log.info(f"每周任务执行成功: {func.__name__}, 结果: {result}")
                return result
            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"每周任务 {func.__name__} 执行失败: {str(e)}\n{error_detail}")

                # 记录错误但不取消任务
                with cls._lock:
                    if job_id in cls._jobs:
                        cls._jobs[job_id]["last_error"] = str(e)
                        cls._jobs[job_id]["last_error_time"] = datetime.now().isoformat()

        # 根据星期几设置调度
        job = None
        day = day.lower()
        if day == "monday":
            job = cls._scheduler.every().monday.at(time_str).do(task_wrapper)
        elif day == "tuesday":
            job = cls._scheduler.every().tuesday.at(time_str).do(task_wrapper)
        elif day == "wednesday":
            job = cls._scheduler.every().wednesday.at(time_str).do(task_wrapper)
        elif day == "thursday":
            job = cls._scheduler.every().thursday.at(time_str).do(task_wrapper)
        elif day == "friday":
            job = cls._scheduler.every().friday.at(time_str).do(task_wrapper)
        elif day == "saturday":
            job = cls._scheduler.every().saturday.at(time_str).do(task_wrapper)
        elif day == "sunday":
            job = cls._scheduler.every().sunday.at(time_str).do(task_wrapper)
        else:
            raise ValueError(f"不支持的星期几: {day}")

        # 记录任务信息
        with cls._lock:
            cls._jobs[job_id] = {
                "name": getattr(func, "__name__", "unnamed_task"),
                "type": "weekly",
                "day": day,
                "time": time_str,
                "job": job,
                "created_at": datetime.now().isoformat(),
                "last_run": None,
                "runs_count": 0,
                "args": str(args)[:100] if args else None,
                "kwargs": str(kwargs)[:100] if kwargs else None,
            }

        return job_id

    @classmethod
    async def cancel_task(cls, job_id: str) -> bool:
        """取消调度的任务

        Args:
            job_id: 任务ID

        Returns:
            是否成功取消
        """
        with cls._lock:
            if job_id in cls._jobs:
                job_info = cls._jobs[job_id]
                job = job_info.get("job")

                if job:
                    # 从schedule中取消任务
                    cls._scheduler.cancel_job(job)

                # 从任务字典中移除
                del cls._jobs[job_id]
                log.info(f"任务已取消: {job_id}")
                return True
            else:
                log.warning(f"取消任务失败，任务ID不存在: {job_id}")
                return False

    @classmethod
    async def execute_tasks(cls):
        """执行后台任务，兼容原有API
        一般是请求结果返回之后执行
        """
        bg_tasks = await cls.get_bg_tasks_obj()
        if not bg_tasks:
            return

        if not bg_tasks.tasks:
            return

        try:
            # 对于时间敏感的任务，确保它们会在响应后立即执行
            await bg_tasks()
        except Exception as e:
            error_detail = traceback.format_exc()
            log.error(f"执行后台任务队列时发生错误: {str(e)}\n{error_detail}")
