from typing import Optional, List
from datetime import datetime

from pydantic import BaseModel, Field, HttpUrl

from app.models.strm import ServerType, TaskStatus, FileType


class MediaServerBase(BaseModel):
    """媒体服务器基础模型"""
    name: str = Field(..., description="服务器名称")
    server_type: ServerType = Field(default=ServerType.HTTP, description="服务器类型")
    base_url: str = Field(..., description="服务器基础URL")
    is_default: bool = Field(default=False, description="是否为默认服务器")
    description: Optional[str] = Field(default=None, description="服务器描述")
    auth_required: bool = Field(default=False, description="是否需要认证")
    username: Optional[str] = Field(default=None, description="认证用户名")
    password: Optional[str] = Field(default=None, description="认证密码")


class MediaServerCreate(MediaServerBase):
    """创建媒体服务器模型"""
    pass


class MediaServerUpdate(BaseModel):
    """更新媒体服务器模型"""
    name: Optional[str] = Field(default=None, description="服务器名称")
    server_type: Optional[ServerType] = Field(default=None, description="服务器类型")
    base_url: Optional[str] = Field(default=None, description="服务器基础URL")
    is_default: Optional[bool] = Field(default=None, description="是否为默认服务器")
    description: Optional[str] = Field(default=None, description="服务器描述")
    auth_required: Optional[bool] = Field(default=None, description="是否需要认证")
    username: Optional[str] = Field(default=None, description="认证用户名")
    password: Optional[str] = Field(default=None, description="认证密码")


class MediaServerResponse(MediaServerBase):
    """媒体服务器响应模型"""
    id: int = Field(..., description="服务器ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class PathMappingBase(BaseModel):
    """路径映射规则基础模型"""
    name: str = Field(..., description="规则名称")
    source_path: str = Field(..., description="源路径")
    target_path: str = Field(..., description="目标路径")
    server_id: int = Field(..., description="所属服务器ID")
    is_regex: bool = Field(default=False, description="是否使用正则表达式")


class PathMappingCreate(PathMappingBase):
    """创建路径映射规则模型"""
    pass


class PathMappingUpdate(BaseModel):
    """更新路径映射规则模型"""
    name: Optional[str] = Field(default=None, description="规则名称")
    source_path: Optional[str] = Field(default=None, description="源路径")
    target_path: Optional[str] = Field(default=None, description="目标路径")
    server_id: Optional[int] = Field(default=None, description="所属服务器ID")
    is_regex: Optional[bool] = Field(default=None, description="是否使用正则表达式")


class PathMappingResponse(PathMappingBase):
    """路径映射规则响应模型"""
    id: int = Field(..., description="映射规则ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")
    
    class Config:
        from_attributes = True


class FilterRuleBase(BaseModel):
    """文件过滤规则基础模型"""
    name: str = Field(..., description="规则名称")
    file_type: Optional[FileType] = Field(default=None, description="文件类型")
    keyword: Optional[str] = Field(default=None, description="关键词")
    path_pattern: Optional[str] = Field(default=None, description="路径模式")
    is_include: bool = Field(default=True, description="是包含规则还是排除规则")


class FilterRuleCreate(FilterRuleBase):
    """创建文件过滤规则模型"""
    pass


class FilterRuleUpdate(BaseModel):
    """更新文件过滤规则模型"""
    name: Optional[str] = Field(default=None, description="规则名称")
    file_type: Optional[FileType] = Field(default=None, description="文件类型")
    keyword: Optional[str] = Field(default=None, description="关键词")
    path_pattern: Optional[str] = Field(default=None, description="路径模式")
    is_include: Optional[bool] = Field(default=None, description="是包含规则还是排除规则")


class FilterRuleResponse(FilterRuleBase):
    """文件过滤规则响应模型"""
    id: int = Field(..., description="过滤规则ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class StrmTaskBase(BaseModel):
    """STRM生成任务基础模型"""
    name: str = Field(..., description="任务名称")
    server_id: int = Field(..., description="使用的服务器ID")
    output_dir: str = Field(..., description="输出目录")


class StrmTaskCreate(StrmTaskBase):
    """创建STRM生成任务模型"""
    pass


class StrmTaskUpdate(BaseModel):
    """更新STRM生成任务模型"""
    name: Optional[str] = Field(default=None, description="任务名称")
    status: Optional[TaskStatus] = Field(default=None, description="任务状态")
    server_id: Optional[int] = Field(default=None, description="使用的服务器ID")
    output_dir: Optional[str] = Field(default=None, description="输出目录")


class StrmTaskResponse(StrmTaskBase):
    """STRM生成任务响应模型"""
    id: int = Field(..., description="任务ID")
    status: TaskStatus = Field(..., description="任务状态")
    source_file: Optional[str] = Field(default=None, description="源文件路径")
    total_files: int = Field(default=0, description="总文件数")
    processed_files: int = Field(default=0, description="已处理文件数")
    success_files: int = Field(default=0, description="成功生成文件数")
    failed_files: int = Field(default=0, description="失败文件数")
    start_time: Optional[datetime] = Field(default=None, description="开始时间")
    end_time: Optional[datetime] = Field(default=None, description="结束时间")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")
    created_by_id: int = Field(..., description="创建者ID")

    class Config:
        from_attributes = True


class StrmFileResponse(BaseModel):
    """STRM文件记录响应模型"""
    id: int = Field(..., description="文件ID")
    task_id: int = Field(..., description="所属任务ID")
    source_path: str = Field(..., description="源文件路径")
    target_path: str = Field(..., description="生成的STRM文件路径")
    file_type: FileType = Field(..., description="文件类型")
    file_size: Optional[int] = Field(default=None, description="文件大小(字节)")
    is_success: bool = Field(default=True, description="是否生成成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    create_time: datetime = Field(..., description="创建时间")

    class Config:
        from_attributes = True


class TreeFileResponse(BaseModel):
    """解析后的树文件响应模型"""
    path: str = Field(..., description="文件路径")
    file_type: FileType = Field(..., description="文件类型")
    extension: str = Field(..., description="文件扩展名")
    file_name: str = Field(..., description="文件名")
    directory: str = Field(..., description="所在目录")


__all__ = [
    "MediaServerCreate", "MediaServerUpdate", "MediaServerResponse",
    "PathMappingCreate", "PathMappingUpdate", "PathMappingResponse",
    "FilterRuleCreate", "FilterRuleUpdate", "FilterRuleResponse",
    "StrmTaskCreate", "StrmTaskUpdate", "StrmTaskResponse", 
    "StrmFileResponse", "TreeFileResponse"
] 