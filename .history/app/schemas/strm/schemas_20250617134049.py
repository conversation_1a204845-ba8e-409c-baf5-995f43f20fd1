from typing import Optional, List, Any
from datetime import datetime

from pydantic import BaseModel, Field, HttpUrl, validator

from app.models.strm import ServerType, TaskStatus, FileType
from app.schemas.form import as_form
from app.schemas.base import PageData


class MediaServerBase(BaseModel):
    """媒体服务器基础模型"""
    name: str = Field(..., description="服务器名称")
    server_type: ServerType = Field(default=ServerType.HTTP, description="服务器类型")
    base_url: str = Field(..., description="服务器基础URL")
    is_default: bool = Field(default=False, description="是否为默认服务器")
    description: Optional[str] = Field(default=None, description="服务器描述")
    auth_required: bool = Field(default=False, description="是否需要认证")
    username: Optional[str] = Field(default=None, description="认证用户名")
    password: Optional[str] = Field(default=None, description="认证密码")

    class Config:
        from_attributes = True


class MediaServerCreate(MediaServerBase):
    """创建媒体服务器模型"""
    pass


class MediaServerUpdate(BaseModel):
    """更新媒体服务器模型"""
    name: Optional[str] = Field(default=None, description="服务器名称")
    server_type: Optional[ServerType] = Field(default=None, description="服务器类型")
    base_url: Optional[str] = Field(default=None, description="服务器基础URL")
    is_default: Optional[bool] = Field(default=None, description="是否为默认服务器")
    description: Optional[str] = Field(default=None, description="服务器描述")
    auth_required: Optional[bool] = Field(default=None, description="是否需要认证")
    username: Optional[str] = Field(default=None, description="认证用户名")
    password: Optional[str] = Field(default=None, description="认证密码")


class MediaServerResponse(MediaServerBase):
    """媒体服务器响应模型"""
    id: int = Field(..., description="服务器ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class PathMappingBase(BaseModel):
    """路径映射规则基础模型"""
    name: str = Field(..., description="规则名称")
    source_path: str = Field(..., description="源路径")
    target_path: str = Field(..., description="目标路径")
    server_id: int = Field(..., description="所属服务器ID")
    is_regex: bool = Field(default=False, description="是否使用正则表达式")

    class Config:
        from_attributes = True


class PathMappingCreate(PathMappingBase):
    """创建路径映射规则模型"""
    pass


class PathMappingUpdate(BaseModel):
    """更新路径映射规则模型"""
    name: Optional[str] = Field(default=None, description="规则名称")
    source_path: Optional[str] = Field(default=None, description="源路径")
    target_path: Optional[str] = Field(default=None, description="目标路径")
    server_id: Optional[int] = Field(default=None, description="所属服务器ID")
    is_regex: Optional[bool] = Field(default=None, description="是否使用正则表达式")


class PathMappingResponse(PathMappingBase):
    """路径映射规则响应模型"""
    id: int = Field(..., description="映射规则ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class FilterRuleBase(BaseModel):
    """文件过滤规则基础模型"""
    name: str = Field(..., description="规则名称")
    file_type: Optional[FileType] = Field(default=None, description="文件类型")
    keyword: Optional[str] = Field(default=None, description="关键词")
    path_pattern: Optional[str] = Field(default=None, description="路径模式")
    is_include: bool = Field(default=True, description="是包含规则还是排除规则")

    class Config:
        from_attributes = True


class FilterRuleCreate(FilterRuleBase):
    """创建文件过滤规则模型"""
    pass


class FilterRuleUpdate(BaseModel):
    """更新文件过滤规则模型"""
    name: Optional[str] = Field(default=None, description="规则名称")
    file_type: Optional[FileType] = Field(default=None, description="文件类型")
    keyword: Optional[str] = Field(default=None, description="关键词")
    path_pattern: Optional[str] = Field(default=None, description="路径模式")
    is_include: Optional[bool] = Field(default=None, description="是包含规则还是排除规则")


class FilterRuleResponse(FilterRuleBase):
    """文件过滤规则响应模型"""
    id: int = Field(..., description="过滤规则ID")
    create_time: datetime = Field(..., description="创建时间")
    update_time: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class UploadResult(BaseModel):
    """上传结果模型"""
    filename: str  # 文件名
    path: str  # 存储路径
    record_id: int  # 上传记录ID


class ParseRequest(BaseModel):
    """解析请求模型"""
    record_id: int  # 上传记录ID
    file_path: Optional[str] = None  # 可选的文件路径参数


class UrlUploadRequest(BaseModel):
    """URL上传请求模型"""
    url: str  # 文件URL地址
    
    @validator('url')
    def validate_url(cls, v):
        if not v or not isinstance(v, str):
            raise ValueError('无效的URL')
        # 添加基本的URL格式验证
        if not v.startswith(('http://', 'https://')):
            raise ValueError('URL必须以http://或https://开头')
        return v


class UploadRecord(BaseModel):
    """上传记录模型"""
    id: int
    filename: str
    filesize: int
    status: str
    create_time: datetime
    uploader_id: int
    parse_time: Optional[datetime] = None


class UploadHistoryResponse(BaseModel):
    """上传历史响应模型"""
    total: int
    page: int
    page_size: int
    records: List[UploadRecord]


__all__ = [
    "MediaServerCreate", "MediaServerUpdate", "MediaServerResponse",
    "PathMappingCreate", "PathMappingUpdate", "PathMappingResponse",
    "FilterRuleCreate", "FilterRuleUpdate", "FilterRuleResponse",
    "ParseRequest", "UrlUploadRequest", "UploadRecord", "UploadHistoryResponse",
] 