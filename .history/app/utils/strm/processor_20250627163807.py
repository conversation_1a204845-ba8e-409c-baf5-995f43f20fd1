"""
STRM处理器，用于生成STRM文件
"""

import os
import re
import asyncio
import threading
import queue
import time
import shutil
import zipfile
import logging
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional, Union
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import quote

from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings
from app.utils.strm.parser import TreeParser


class ResourceDownloader:
    """资源文件多线程下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        threads: int = 3,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        task: Optional[StrmTask] = None,
        verbose_console_logging: bool = False,  # 添加参数控制是否输出详细日志到控制台
    ):
        """
        初始化资源文件下载器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            threads: 下载线程数
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            task: 关联的任务，用于记录日志
            verbose_console_logging: 是否在控制台输出详细的下载日志，默认为False
        """
        self.server = server
        self.output_dir = output_dir
        self.threads = max(1, threads)  # 至少1个线程
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.task = task  # 保存任务引用
        self.verbose_console_logging = False  # 强制禁用控制台详细日志，忽略传入的参数

        # 配置只输出到控制台的日志记录器
        logger_name = f"resource_downloader_{task.id if task else ''}"
        self.logger = logging.getLogger(logger_name)

        # 确保logger不会继承父日志处理器（防止输出到文件）
        self.logger.propagate = False

        # 清除已有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 只添加控制台处理器，但设置为ERROR级别以上（不显示INFO级别的下载消息）
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.CRITICAL)  # 只显示CRITICAL级别的消息
        self.logger.setLevel(logging.CRITICAL)  # 设置为CRITICAL级别
        self.logger.addHandler(console_handler)

        # 下载队列和状态
        self.download_queue = queue.Queue()
        self.lock = threading.Lock()
        self.success_count = 0
        self.failed_count = 0
        self.results = []  # 保存下载结果
        self.stop_event = threading.Event()

    def add_file(self, file_info: Dict[str, Any], task: Optional[StrmTask] = None):
        """
        添加文件到下载队列

        Args:
            file_info: 文件信息
            task: 关联的任务
        """
        self.download_queue.put((file_info, task))

    def download_worker(self):
        """下载线程工作函数"""
        thread_name = threading.current_thread().name
        
        while not self.stop_event.is_set():
            try:
                # 尝试从队列获取一个文件，等待最多1秒
                try:
                    file_info, task = self.download_queue.get(timeout=1.0)
                except queue.Empty:
                    # 队列为空，继续检查stop_event
                    continue

                # 检查是否已经设置了停止标志
                if self.stop_event.is_set():
                    # 任务已取消，将任务放回队列
                    self.download_queue.put((file_info, task))
                    # 标记任务为完成
                    self.download_queue.task_done()
                    continue

                # 解析文件信息
                path = file_info.get("path", "")
                file_type = file_info.get("file_type", "other")
                
                # 创建文件保存目录
                target_path = self.get_output_path(path)
                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                # 构建下载URL
                url = self.get_download_url(path)
                
                # 执行下载
                result = self.download_file(url, target_path)
                
                # 解析结果
                is_success = result.get("is_success", False)
                error_message = result.get("error_message", None)
                file_size = result.get("file_size", 0)
                download_time = result.get("download_time", 0)
                download_speed = result.get("download_speed", 0)
                
                # 更新统计
                with self.lock:
                    if is_success:
                        self.success_count += 1
                    else:
                        self.failed_count += 1
                
                # 计算进度
                with self.lock:
                    processed = self.success_count + self.failed_count
                    total = self.download_queue.qsize() + processed
                    progress_percent = processed / total * 100 if total > 0 else 0
                
                # 记录结果到任务日志（但不输出到控制台）
                self.log_to_db_sync(
                    message=f"线程 {thread_name} | 文件: {path} | 保存: {target_path} | 大小: {self._format_size(file_size) if file_size else 'N/A'} | 耗时: {download_time:.2f}秒 if download_time else 'N/A'} | 速度: {self._format_size(download_speed) + '/s' if download_speed else 'N/A'} | 进度: {progress_percent:.1f}% [{processed}/{total}] | URL: {url}",
                    level="ERROR" if error_message else "INFO",
                    file_path=path,
                    target_path=target_path,
                    file_type=file_type,
                    file_size=file_size,
                    download_time=download_time,
                    download_speed=download_speed,
                    is_success=is_success,
                    error_message=error_message,
                    url=url,
                    progress=progress_percent,
                )
                
                # 记录结果
                self.results.append({
                    "file_path": path,
                    "target_path": target_path,
                    "file_type": file_type,
                    "file_size": file_size,
                    "download_time": download_time,
                    "download_speed": download_speed,
                    "is_success": is_success,
                    "error_message": error_message,
                })
                
                # 标记任务为完成
                self.download_queue.task_done()
                
            except Exception as e:
                # 记录异常，但不输出到控制台
                with self.lock:
                    self.failed_count += 1
                
                # 标记任务为完成（如果任务已获取）
                try:
                    self.download_queue.task_done()
                except Exception:
                    pass

    async def _download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile], Dict[str, Any]]:
        """
        下载单个资源文件，内部使用

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录, 下载详情)
        """
        # 收集下载详情，用于生成综合日志
        current_time = time.time()
        download_details = {
            "start_time": current_time,
            "end_time": None,
            "duration": 0,
            "speed": 0,
            "file_size": 0,
            "url": "",
            "is_success": False,
            "error_message": None,
            "file_path": "",
            "target_path": "",
            "file_type": "",
        }

        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote
            import ssl

            # 构建媒体URL
            file_path = file_info["path"]
            file_type = file_info.get("file_type", FileType.OTHER)

            # 记录基本信息
            download_details["file_path"] = file_path
            download_details["file_type"] = file_type

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"
            download_details["url"] = url

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)
            download_details["target_path"] = resource_path

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    # 先尝试HEAD请求获取文件信息
                    expected_content_length = None
                    try:
                        async with session.head(url) as head_response:
                            if head_response.status == 200:
                                content_length = head_response.headers.get("Content-Length")
                                if content_length:
                                    expected_content_length = int(content_length)
                    except Exception:
                        # 忽略HEAD请求失败，继续尝试GET请求
                        pass

                    # 开始GET请求下载文件
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )

                            # 添加10秒等待时间（即使下载失败）
                            self.logger.info(f"文件 {file_path} 下载失败，开始等待10秒...")
                            time.sleep(10)  # 使用同步time.sleep替代异步等待
                            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                            return False, error_msg, strm_file, download_details

                        # 获取Content-Length
                        content_length = response.headers.get("Content-Length")
                        if content_length:
                            expected_content_length = int(content_length)

                        # 读取响应内容
                        content = await response.read()
                        file_size = len(content)
                        download_details["file_size"] = file_size

                        # 验证文件有效性
                        if file_size == 0:
                            error_msg = f"下载的文件大小为0，文件无效"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )

                            # 添加10秒等待时间（即使文件无效）
                            self.logger.info(f"文件 {file_path} 下载失败（大小为0），开始等待10秒...")
                            time.sleep(10)  # 使用同步time.sleep替代异步等待
                            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                            return False, error_msg, strm_file, download_details

                        # 如果之前HEAD请求获取到了预期的文件大小，进行比较验证
                        if expected_content_length is not None and expected_content_length > 0:
                            # 允许有10%的误差
                            size_difference = abs(expected_content_length - file_size)
                            if size_difference > expected_content_length * 0.1:  # 超过10%的差异
                                download_details["warning"] = (
                                    f"文件大小与预期不符: 预期 {expected_content_length}，实际 {file_size}"
                                )

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 验证写入的文件
                        try:
                            # 检查文件是否存在且大小正确
                            if not os.path.exists(resource_path):
                                error_msg = f"文件写入失败: 文件不存在"
                                download_details["is_success"] = False
                                download_details["error_message"] = error_msg

                                # 添加10秒等待时间（文件写入失败）
                                self.logger.info(f"文件 {file_path} 写入失败，开始等待10秒...")
                                time.sleep(10)  # 使用同步time.sleep替代异步等待
                                self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                                return False, error_msg, None, download_details

                            actual_file_size = os.path.getsize(resource_path)
                            if actual_file_size != file_size:
                                error_msg = f"文件写入不完整: 预期 {file_size}，实际 {actual_file_size}"
                                download_details["is_success"] = False
                                download_details["error_message"] = error_msg

                                # 添加10秒等待时间（文件写入不完整）
                                self.logger.info(f"文件 {file_path} 写入不完整，开始等待10秒...")
                                time.sleep(10)  # 使用同步time.sleep替代异步等待
                                self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                                return False, error_msg, None, download_details
                        except Exception as e:
                            error_msg = f"验证文件失败: {str(e)}"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 添加10秒等待时间（验证文件失败）
                            self.logger.info(f"文件 {file_path} 验证失败，开始等待10秒...")
                            time.sleep(10)  # 使用同步time.sleep替代异步等待
                            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                            return False, error_msg, None, download_details

                        # 计算下载耗时和速度
                        end_time = time.time()
                        duration = end_time - download_details["start_time"]
                        speed = file_size / duration if duration > 0 else 0

                        # 更新下载详情
                        download_details["end_time"] = end_time
                        download_details["duration"] = duration
                        download_details["speed"] = speed
                        download_details["is_success"] = True

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=file_size,
                                is_success=True,
                            )

                        # 添加10秒等待时间（下载成功）
                        self.logger.info(f"文件 {file_path} 下载成功，开始等待10秒...")
                        time.sleep(10)  # 使用同步time.sleep替代异步等待
                        self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                        return True, None, strm_file, download_details

                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    download_details["is_success"] = False
                    download_details["error_message"] = error_msg

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )

                    # 添加10秒等待时间（客户端错误）
                    self.logger.info(f"文件 {file_path} 客户端错误，开始等待10秒...")
                    time.sleep(10)  # 使用同步time.sleep替代异步等待
                    self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                    return False, error_msg, strm_file, download_details

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            download_details["is_success"] = False
            download_details["error_message"] = error_msg

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",
                    file_type=file_type,
                    is_success=False,
                    error_message=error_msg,
                )

            # 添加10秒等待时间（异常）
            self.logger.info(f"文件 {file_path} 发生异常，开始等待10秒...")
            time.sleep(10)  # 使用同步time.sleep替代异步等待
            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

            return False, error_msg, strm_file, download_details

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小为人类可读形式"""
        import math

        if size_bytes == 0:
            return "0 B"

        size_name = ("B", "KB", "MB", "GB", "TB")
        i = int(math.log(size_bytes, 1024))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_name[i]}"

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path

    def start_download(self):
        """
        启动下载线程并等待所有下载完成

        Returns:
            下载结果统计
        """
        # 创建并启动下载线程
        download_threads = []
        for i in range(self.threads):
            thread = threading.Thread(target=self.download_worker, name=f"DownloadWorker-{i + 1}")
            thread.daemon = True  # 设置为守护线程，这样主线程结束时它们也会结束
            thread.start()
            download_threads.append(thread)

        # 等待所有线程完成（队列为空）
        while not self.download_queue.empty():
            try:
                time.sleep(1.0)  # 每秒检查一次
            except KeyboardInterrupt:
                # 设置停止事件，通知所有线程停止
                self.stop_event.set()
                # 记录取消消息
                self._thread_safe_save_log_content("下载已被用户取消", level="WARNING", is_summary=True)
                break

        # 设置停止事件，通知所有线程停止（如果队列为空）
        self.stop_event.set()

        # 等待所有线程结束
        for thread in download_threads:
            thread.join(timeout=3.0)  # 等待最多3秒

        # 计算总体统计信息
        total_files = self.success_count + self.failed_count
        success_rate = (self.success_count / total_files) * 100 if total_files > 0 else 0

        # 返回下载结果统计
        return {
            "success_count": self.success_count,
            "failed_count": self.failed_count,
            "total_files": total_files,
            "success_rate": success_rate,
            "results": self.results,
        }

    def calculate_average_speed(self) -> float:
        """计算平均下载速度（字节/秒）"""
        try:
            # 计算总下载字节数
            total_bytes = 0
            successful_files = 0

            for result in self.results:
                if result.get("is_success", False) and result.get("file_size", 0) > 0:
                    total_bytes += result.get("file_size", 0)
                    successful_files += 1

            # 计算平均速度
            if successful_files > 0:
                # 至少需要一个成功的文件来计算平均速度
                return total_bytes / successful_files  # 字节/文件
            return 0
        except Exception as e:
            self.logger.error(f"计算平均下载速度失败: {str(e)}")
            return 0

    def create_summary_log(self, duration: float):
        """
        创建下载汇总日志

        Args:
            duration: 总耗时(秒)
        """
        # 计算总体统计信息
        total_files = self.success_count + self.failed_count
        success_rate = (self.success_count / total_files) * 100 if total_files > 0 else 0

        # 保存到任务记录中
        if self.task:
            self.task.log_content = self.task.log_content or ""
            self.task.log_content += f"\n[汇总] 总文件数: {total_files}, 成功: {self.success_count}, 失败: {self.failed_count}, 成功率: {success_rate:.1f}%, 总时间: {duration:.2f}秒\n"
            self._thread_safe_save_log(self.task, {"log_content": self.task.log_content})

    def log_initial_summary(self):
        """
        记录初始汇总信息
        """
        # 只保存到任务记录，不输出到控制台
        if self.task:
            self.task.log_content = self.task.log_content or ""
            self.task.log_content += f"\n[开始下载] 队列中文件数: {self.download_queue.qsize()}\n"
            self._thread_safe_save_log(self.task, {"log_content": self.task.log_content})

    def _thread_safe_save_log_content(self, message: str, level: str = "INFO", is_summary: bool = False):
        """
        以线程安全的方式更新任务日志内容

        Args:
            message: 日志消息
            level: 日志级别
            is_summary: 是否为汇总日志
        """
        if not self.task:
            return

        try:
            # 在日志条目末尾添加换行符
            log_entry = message + "\n"

            # 线程安全地更新任务日志内容
            with self.lock:
                current_log = self.task.log_content or ""
                self.task.log_content = current_log + log_entry

            # 只保存日志内容，不输出到控制台
            self._thread_safe_save_log(self.task, {"log_content": self.task.log_content})
        except Exception:
            # 忽略所有异常
            pass

    async def log_to_db(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
    ):
        """
        将日志记录到任务的log_content字段

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
        """
        # 如果没有关联任务，无法记录日志
        if not self.task:
            # 不记录日志，直接返回
            return

        try:
            # 格式化日志条目
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] [{level}] {message}"

            # 添加文件路径信息
            if file_path:
                log_entry += f" | 文件: {file_path}"

            # 添加目标路径信息
            if target_path:
                log_entry += f" | 目标: {target_path}"

            # 添加错误信息
            if error_message:
                log_entry += f" | 错误: {error_message}"

            # 添加成功状态
            status_text = "成功" if is_success else "失败"
            log_entry += f" | 状态: {status_text}"

            # 添加文件大小和下载时间信息（如有）
            if file_size is not None:
                size_formatted = self._format_size(file_size)
                log_entry += f" | 大小: {size_formatted}"

            if download_time is not None:
                log_entry += f" | 耗时: {download_time:.2f}s"

            if download_speed is not None and download_speed > 0:
                speed_formatted = self._format_size(int(download_speed)) + "/s"
                log_entry += f" | 速度: {speed_formatted}"

            # 在日志条目末尾添加换行符
            log_entry += "\n"

            # 获取当前的日志内容
            current_log = self.task.log_content or ""

            # 追加新的日志条目
            self.task.log_content = current_log + log_entry

            # 保存任务
            await self.task.save(update_fields=["log_content"])

        except Exception as e:
            # 记录日志失败时输出到日志
            self.logger.error(f"记录日志到任务失败: {str(e)}")

    def log_to_db_sync(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
        url: Optional[str] = None,
        progress: Optional[float] = None,
    ):
        """
        同步版本的日志记录函数，用于在线程中记录日志

        Args:
            message: 日志消息
            level: 日志级别
            file_path: 文件路径
            target_path: 目标路径
            file_type: 文件类型
            file_size: 文件大小(字节)
            download_time: 下载耗时(秒)
            download_speed: 下载速度(字节/秒)
            is_success: 是否成功
            error_message: 错误信息
            url: 下载URL
            progress: 当前进度百分比
        """
        if not self.task:
            return

        # 只记录到任务日志，不输出到控制台
        try:
            # 如果task_log_content不存在，初始化
            if not self.task.log_content:
                self.task.log_content = ""

            # 构建日志条目
            log_entry = f"[{datetime.now().isoformat()}] [{level}] {message}"

            # 更新任务日志内容 - 这是唯一需要保留的操作
            self._thread_safe_save_log_content(log_entry, level, is_summary=is_summary_log(message))

            # 更新任务状态，但不输出到控制台
            if level == "ERROR" and not is_summary_log(message):
                # 只更新任务状态，不打印日志
                self._thread_safe_save_log(
                    self.task,
                    {
                        "error_files": F.F("error_files") + 1,
                    },
                )
            elif level == "INFO" and not is_summary_log(message) and "线程" in message and "保存" in message:
                # 只更新任务状态，不打印日志
                if is_success:
                    self._thread_safe_save_log(
                        self.task,
                        {
                            "success_files": F.F("success_files") + 1,
                        },
                    )
                else:
                    self._thread_safe_save_log(
                        self.task,
                        {
                            "error_files": F.F("error_files") + 1,
                        },
                    )
        except Exception as e:
            # 记录异常，但不输出到控制台
            pass

    def _thread_safe_save_log(self, task, update_fields):
        """在新线程中安全地保存日志，避免事件循环冲突"""
        try:
            # 尝试获取当前事件循环，如果不存在则创建一个新的
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # 检查事件循环是否正在运行
            if loop.is_running():
                # 使用run_coroutine_threadsafe将协程安排到事件循环上
                try:
                    future = asyncio.run_coroutine_threadsafe(task.save(update_fields=update_fields), loop)
                    # 等待最多5秒
                    try:
                        future.result(timeout=5.0)
                    except asyncio.TimeoutError:
                        self.logger.error("线程安全保存超时，将在后台继续尝试")
                    except concurrent.futures.CancelledError:
                        self.logger.error("线程安全保存被取消")
                except Exception as e:
                    self.logger.error(f"将协程安排到事件循环失败: {type(e).__name__}: {str(e)}")
            else:
                # 事件循环没有运行，使用run_until_complete
                try:
                    loop.run_until_complete(task.save(update_fields=update_fields))
                    # 如果这是我们创建的新事件循环，关闭它
                    if not asyncio.get_event_loop_policy().get_event_loop() == loop:
                        loop.close()
                except Exception as e:
                    self.logger.error(f"运行协程时出错: {type(e).__name__}: {str(e)}")

        except Exception as e:
            # 处理所有异常，不让错误传播
            error_type = type(e).__name__
            self.logger.error(f"线程安全保存失败: {error_type}: {str(e)}")

            # 如果是锁相关的错误，提供更具体的信息
            if "is bound to a different event loop" in str(e):
                self.logger.error(
                    f"事件循环绑定错误: 资源已绑定到其他事件循环，此错误通常无需担心，数据会在其他线程中保存"
                )


class StrmProcessor:
    """STRM处理器，用于生成STRM文件"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        verbose_console_logging: bool = False,  # 添加控制台详细日志参数
        settings_dict: Optional[Dict[str, Any]] = None,  # 添加settings_dict参数
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            verbose_console_logging: 是否输出详细日志到控制台，默认为False
            settings_dict: 系统设置字典，用于获取文件类型配置
        """
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.verbose_console_logging = verbose_console_logging  # 保存详细日志设置

        # 配置日志记录器
        self.logger = logging.getLogger("strm_processor")

        # 设置控制台处理器的日志级别
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler):
                if not verbose_console_logging:
                    handler.setLevel(logging.ERROR)  # 只输出错误日志
                else:
                    handler.setLevel(logging.INFO)  # 输出所有日志

        # 如果提供了settings_dict，使用TreeParser获取文件类型
        self.tree_parser = None
        if settings_dict:
            try:
                self.tree_parser = TreeParser(settings_dict)
            except Exception as e:
                self.logger.error(f"初始化TreeParser失败: {str(e)}")
                self.tree_parser = None

    def get_file_type(self, file_path: str) -> str:
        """
        根据文件路径获取文件类型

        Args:
            file_path: 文件路径

        Returns:
            文件类型枚举值字符串
        """
        # 优先使用tree_parser获取文件类型
        if self.tree_parser:
            return self.tree_parser.get_file_type(file_path)

        # 默认实现逻辑
        if not file_path:
            return FileType.OTHER

        # 获取文件扩展名
        _, extension = os.path.splitext(file_path)
        extension = extension.lower()

        # 去掉扩展名前面的点
        if extension.startswith("."):
            extension = extension[1:]

        # 视频文件类型
        video_extensions = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "mpg", "mpeg", "m4v", "ts", "m2ts"]
        if extension in video_extensions:
            return FileType.VIDEO

        # 音频文件类型
        audio_extensions = ["mp3", "flac", "wav", "aac", "ogg", "m4a", "wma", "ape"]
        if extension in audio_extensions:
            return FileType.AUDIO

        # 图片文件类型
        image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "svg"]
        if extension in image_extensions:
            return FileType.IMAGE

        # 字幕文件类型
        subtitle_extensions = ["srt", "ass", "ssa", "vtt", "sub", "idx"]
        if extension in subtitle_extensions:
            return FileType.SUBTITLE

        # 元数据文件类型
        metadata_extensions = ["nfo", "xml", "json", "txt"]
        if extension in metadata_extensions:
            return FileType.METADATA

        # 默认为其他类型
        return FileType.OTHER

    def generate_strm(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建STRM文件路径
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 分割路径和扩展名
            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            # 完整的STRM文件路径
            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            # 确保目录存在
            os.makedirs(strm_dir, exist_ok=True)

            # 写入STRM文件
            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            # 创建STRM文件记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )
                # 文件记录需要在外部保存

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",  # 失败没有目标路径
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )
                # 文件记录需要在外部保存

            return False, error_msg, strm_file

    async def download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载单个资源文件，与generate_strm类似，但专用于资源文件

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote

            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 获取文件类型
            file_type = file_info.get("file_type", FileType.OTHER)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 禁用SSL验证，使用兼容的方式
            await self.log_to_db(f"SSL验证已禁用", file_path=file_path)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            self.logger.error(error_msg)

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        # 读取响应内容
                        content = await response.read()

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=len(content),
                                is_success=True,
                            )

                        return True, None, strm_file
                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    self.logger.error(error_msg)

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path if "file_path" in locals() else "",
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    async def process_files(
        self,
        files: List[Dict[str, Any]],
        task: Optional[StrmTask] = None,
        download_resources: bool = False,
        download_server: Optional[MediaServer] = None,
        verbose_console_logging: bool = False,  # 添加控制台详细日志参数
    ) -> Dict[str, Any]:
        """
        处理文件列表，生成STRM文件

        Args:
            files: 文件列表
            task: 关联的任务
            download_resources: 是否下载资源文件
            download_server: 下载服务器
            verbose_console_logging: 是否输出详细日志，默认为False

        Returns:
            处理结果
        """
        # 强制禁用详细日志
        verbose_console_logging = False

        # 处理结果
        result = {
            "total": len(files),
            "success": 0,
            "failed": 0,
            "errors": [],
            "details": [],
        }

        # 视频文件计数
        video_count = 0
        video_success = 0
        video_failed = 0

        # 处理文件
        for i, file_info in enumerate(files):
            try:
                # 跳过目录
                if file_info.get("is_dir", False):
                    continue

                # 获取文件类型
                file_type = file_info.get("file_type", self.get_file_type(file_info.get("path", "")))

                # 只处理视频文件生成STRM
                if file_type == FileType.VIDEO:
                    video_count += 1

                    # 生成STRM文件
                    success, error_message, strm_file = self.generate_strm(file_info, task)

                    # 更新统计
                    if success:
                        video_success += 1
                    else:
                        video_failed += 1
                        if error_message:
                            result["errors"].append(
                                {
                                    "file": file_info.get("path", ""),
                                    "error": error_message,
                                }
                            )

                    # 添加处理详情
                    if strm_file:
                        result["details"].append(
                            {
                                "id": strm_file.id,
                                "source_path": strm_file.source_path,
                                "target_path": strm_file.target_path,
                                "is_success": strm_file.is_success,
                                "error_message": strm_file.error_message,
                            }
                        )

                # 更新任务状态
                if task:
                    # 计算进度百分比
                    progress = int((i + 1) / len(files) * 100) if files else 100

                    # 更新任务进度和状态
                    update_fields = {
                        "progress": progress,
                        "processed_files": i + 1,
                    }

                    if file_type == FileType.VIDEO:
                        update_fields["success_files"] = video_success
                        update_fields["failed_files"] = video_failed

                    # 更新任务状态
                    for key, value in update_fields.items():
                        setattr(task, key, value)
                    await task.save(update_fields=list(update_fields.keys()))

            except Exception as e:
                # 记录错误
                result["failed"] += 1
                result["errors"].append(
                    {
                        "file": file_info.get("path", ""),
                        "error": f"处理文件时发生错误: {str(e)}",
                    }
                )

                # 如果是视频文件，更新视频统计
                if file_info.get("file_type") == FileType.VIDEO:
                    video_failed += 1

        # 更新统计
        result["success"] = video_success
        result["failed"] = video_failed
        result["video_count"] = video_count

        # 完成任务处理
        if task:
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()
            task.progress = 100
            await task.save()

        return result

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task: Optional[StrmTask] = None,
    download_resources: bool = False,
    download_server_id: Optional[int] = None,
    verbose_console_logging: bool = False,  # 添加控制台详细日志参数
) -> Dict[str, Any]:
    """
    处理整个目录树的文件

    Args:
        server_id: 服务器ID
        files: 文件列表
        output_dir: 输出目录
        task: 关联的任务
        download_resources: 是否下载资源文件
        download_server_id: 下载服务器ID
        verbose_console_logging: 是否输出详细下载日志到控制台，默认为False

    Returns:
        处理结果
    """
    # 获取服务器配置
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        return {"result": False, "message": f"找不到ID为{server_id}的服务器"}

    # 获取当前系统设置
    settings = await SystemSettings.all().first()
    if not settings:
        settings = await SystemSettings.create()

    # 将SystemSettings对象转换为字典，以便传递给TreeParser
    settings_dict = {
        "settings_version": settings.settings_version,
        "video_file_types": settings.video_file_types,
        "audio_file_types": settings.audio_file_types,
        "image_file_types": settings.image_file_types,
        "subtitle_file_types": settings.subtitle_file_types,
        "metadata_file_types": settings.metadata_file_types,
        "enable_path_replacement": settings.enable_path_replacement,
        "replacement_path": settings.replacement_path,
        "download_threads": settings.download_threads,
    }

    # 设置STRM处理器
    processor = StrmProcessor(
        server=server,
        output_dir=output_dir,
        enable_path_replacement=settings.enable_path_replacement,
        replacement_path=settings.replacement_path,
        verbose_console_logging=verbose_console_logging,  # 设置详细日志级别
        settings_dict=settings_dict,  # 传递settings_dict
    )

    # 下载资源文件处理
    download_result = {}
    if download_resources and task:
        download_server = None
        if download_server_id:
            download_server = await MediaServer.get_or_none(id=download_server_id)
        else:
            # 如果未指定下载服务器，使用系统默认下载服务器
            if settings.default_download_server:
                download_server = await settings.default_download_server

        if not download_server:
            return {"result": False, "message": "未指定下载服务器且未设置默认下载服务器"}

        # 创建资源下载器
        downloader = ResourceDownloader(
            server=download_server,
            output_dir=output_dir,
            threads=settings.download_threads,
            enable_path_replacement=settings.enable_path_replacement,
            replacement_path=settings.replacement_path,
            task=task,
            verbose_console_logging=False,  # 禁用详细日志
        )

        # 添加所有需要下载的文件到队列
        for file_info in files:
            if file_info.get("is_dir", False):
                continue  # 跳过目录

            # 获取文件类型 - 使用settings_dict传递给TreeParser
            file_type_parser = TreeParser(settings_dict)
            file_type = (
                file_type_parser.get_file_type(file_info.get("path", ""))
                if hasattr(file_type_parser, "get_file_type")
                else processor.get_file_type(file_info.get("path", ""))
            )
            file_info["file_type"] = file_type

            # 只下载特定类型的资源文件
            if file_type in [FileType.VIDEO, FileType.AUDIO, FileType.IMAGE, FileType.SUBTITLE, FileType.METADATA]:
                downloader.add_file(file_info, task)

        # 记录初始汇总日志
        downloader.log_initial_summary()

        # 开始下载，这将阻塞直到所有文件处理完成
        start_time = time.time()
        download_result = downloader.start_download()
        duration = time.time() - start_time

        # 更新任务下载耗时
        if task:
            task.download_duration = duration
            await task.save(update_fields=["download_duration"])

        # 创建汇总日志
        downloader.create_summary_log(duration)

        # 添加下载时间到结果
        download_result["download_time"] = duration

    # 处理STRM文件生成
    result = await processor.process_files(
        files=files,
        task=task,
        download_resources=download_resources,
        download_server=download_server if download_resources else None,
        verbose_console_logging=False,  # 禁用详细日志
    )

    # 合并下载结果和处理结果
    if download_resources:
        result["download"] = download_result

    return result


def is_summary_log(message: str) -> bool:
    """
    判断是否为汇总日志

    Args:
        message: 日志消息

    Returns:
        是否为汇总日志
    """
    # 检查汇总日志的特征
    summary_keywords = [
        "总耗时",
        "下载完成",
        "任务统计",
        "总文件数",
        "成功文件数",
        "失败文件数",
        "平均速度",
        "汇总",
        "总计",
        "summary",
        "开始多线程下载",
        "队列中文件数",
    ]

    # 如果消息中包含任何一个关键词，认为是汇总信息
    return any(keyword in message for keyword in summary_keywords)
