"""
STRM处理器，用于生成STRM文件
"""

import os
import re
import asyncio
import threading
import queue
import time
import shutil
import zipfile
import logging
import concurrent.futures
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Union
from concurrent.futures import Thread<PERSON>oolExecutor
from urllib.parse import quote
from tortoise.expressions import Q, F
from tortoise.functions import Sum
from tortoise.transactions import in_transaction
import traceback
from pathlib import Path

from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings, ProcessType
from app.models.strm import DownloadTask, DownloadTaskStatus
from app.utils.strm.parser import TreeParser
from app.log.log import log
from app.controllers.strm import system_settings_controller
from types import SimpleNamespace


class ResourceDownloader:
    """资源文件下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        task: StrmTask,
        threads: int = 3,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        verbose_console_logging: bool = False,
    ):
        self.server = server
        self.output_dir = Path(output_dir)
        self.task = task
        self.task_id = task.id
        self.threads = threads
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.verbose_console_logging = verbose_console_logging
        self.stop_event = asyncio.Event()
        self.logger = logging.getLogger(f"resource_downloader_{self.task_id}")

        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(threadName)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

        self.lock = threading.Lock()
        self.stats = {
            "total_files": 0,
            "success_files": 0,
            "failed_files": 0,
            "total_size": 0,
            "downloaded_size": 0,
            "start_time": time.time(),
            "download_time": 0,
            "average_speed": 0,
        }

    async def add_file(self, file_info: Dict[str, Any]):
        await DownloadTask.create(
            task=self.task,
            source_path=file_info.get("path"),
            file_size=file_info.get("size", 0),
            file_type=file_info.get("file_type"),
            status=DownloadTaskStatus.PENDING,
        )

    async def log_initial_summary(self, total_files: int):
        """记录初始汇总日志到数据库"""
        message = f"准备开始下载资源文件，总计 {total_files} 个文件已加入下载队列。"
        await self.task.log(message, level="INFO")

    async def create_summary_log(self, result: Dict[str, Any]):
        """创建下载完成后的汇总日志"""
        summary = (
            f"资源文件下载完成。\n"
            f"总耗时: {result.get('download_time', 0):.2f} 秒\n"
            f"成功文件数: {result.get('success_files', 0)}\n"
            f"失败文件数: {result.get('failed_files', 0)}\n"
            f"下载总大小: {result.get('downloaded_size', 0) / 1024 / 1024:.2f} MB\n"
            f"平均速度: {result.get('average_speed', 0) / 1024:.2f} KB/s"
        )

        await self.task.log(summary, level="INFO")

    async def download_worker(self):
        """下载工作线程"""
        while not self.stop_event.is_set():
            try:
                async with in_transaction("conn_system") as conn:
                    download_task = (
                        await DownloadTask.filter(
                            task__id=self.task_id, status__in=[DownloadTaskStatus.PENDING, DownloadTaskStatus.RETRY]
                        )
                        .select_for_update(skip_locked=True)
                        .first()
                    )

                    if not download_task:
                        await asyncio.sleep(1)
                        continue

                    download_task.status = DownloadTaskStatus.DOWNLOADING
                    await download_task.save(update_fields=["status"], using_db=conn)

                await asyncio.sleep(0.1)

                download_task.status = DownloadTaskStatus.COMPLETED
                await download_task.save(update_fields=["status"])

            except asyncio.CancelledError:
                break
            except Exception as e:
                log.error(f"Error in download worker: {e}")
                # Potentially mark task as failed here
                await asyncio.sleep(1)

    async def start_download(self) -> Dict[str, Any]:
        if not self.task_id:
            return {"result": False, "message": "Task ID not provided"}

        self.stats["start_time"] = time.time()

        # Start workers
        workers = [asyncio.create_task(self.download_worker()) for _ in range(self.threads)]

        # Wait for all tasks in the DB to be processed
        while await DownloadTask.filter(
            task__id=self.task_id, status__in=[DownloadTaskStatus.PENDING, DownloadTaskStatus.RETRY]
        ).exists():
            await asyncio.sleep(2)  # Poll every 2 seconds

        # Stop workers
        self.stop_event.set()
        await asyncio.gather(*workers, return_exceptions=True)

        self.stats["download_time"] = time.time() - self.stats["start_time"]

        success_count = await DownloadTask.filter(task__id=self.task_id, status=DownloadTaskStatus.COMPLETED).count()
        failed_count = await DownloadTask.filter(task__id=self.task_id, status=DownloadTaskStatus.FAILED).count()

        total_size_agg = (
            await DownloadTask.filter(task__id=self.task_id, status=DownloadTaskStatus.COMPLETED)
            .annotate(total=Sum("file_size"))
            .first()
        )
        total_size_downloaded = total_size_agg.total if total_size_agg and total_size_agg.total else 0

        self.stats.update(
            {
                "success_files": success_count,
                "failed_files": failed_count,
                "total_files": success_count + failed_count,
                "downloaded_size": total_size_downloaded,
                "average_speed": total_size_downloaded / self.stats["download_time"]
                if self.stats["download_time"] > 0
                else 0,
            }
        )

        task = await StrmTask.get_or_none(id=self.task_id)
        if task:
            task.success_files = (task.success_files or 0) + success_count
            task.failed_files = (task.failed_files or 0) + failed_count
            task.processed_files = (task.processed_files or 0) + self.stats["total_files"]
            await task.save()

        return self.stats


class StrmProcessor:
    """STRM处理器，用于生成STRM文件"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        verbose_console_logging: bool = False,
        settings_dict: Optional[Dict[str, Any]] = None,
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            verbose_console_logging: 是否输出详细日志到控制台，默认为False
            settings_dict: 系统设置字典，用于获取文件类型配置
        """
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.verbose_console_logging = verbose_console_logging

        self.logger = logging.getLogger("strm_processor")

        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler):
                if not verbose_console_logging:
                    handler.setLevel(logging.ERROR)
                else:
                    handler.setLevel(logging.INFO)

        self.tree_parser = None
        if settings_dict:
            try:
                self.tree_parser = TreeParser(settings_dict)
            except Exception as e:
                self.logger.error(f"初始化TreeParser失败: {str(e)}")
                self.tree_parser = None

    def get_file_type(self, file_path: str) -> str:
        """
        根据文件路径获取文件类型

        Args:
            file_path: 文件路径

        Returns:
            文件类型枚举值字符串
        """
        if self.tree_parser:
            return self.tree_parser.get_file_type(file_path)

        if not file_path:
            return FileType.OTHER

        _, extension = os.path.splitext(file_path)
        extension = extension.lower()

        if extension.startswith("."):
            extension = extension[1:]

        video_extensions = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "mpg", "mpeg", "m4v", "ts", "m2ts"]
        if extension in video_extensions:
            return FileType.VIDEO

        audio_extensions = ["mp3", "flac", "wav", "aac", "ogg", "m4a", "wma", "ape"]
        if extension in audio_extensions:
            return FileType.AUDIO

        image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "svg"]
        if extension in image_extensions:
            return FileType.IMAGE

        subtitle_extensions = ["srt", "ass", "ssa", "vtt", "sub", "idx"]
        if extension in subtitle_extensions:
            return FileType.SUBTITLE

        metadata_extensions = ["nfo", "xml", "json", "txt"]
        if extension in metadata_extensions:
            return FileType.METADATA

        return FileType.OTHER

    def generate_strm(
        self, file_info: Dict[str, Any], task_id: Optional[int] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task_id: 关联的任务ID

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            file_path = file_info["path"]

            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            os.makedirs(strm_dir, exist_ok=True)

            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            strm_file = None
            if task_id:
                # 注意：这里只创建了StrmFile对象，但没有保存到数据库
                # 因此需要在调用方执行await strm_file.save()才能将记录存入数据库
                strm_file = StrmFile(
                    task_id=task_id,  # 使用task_id而不是task对象
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            strm_file = None
            if task_id:
                strm_file = StrmFile(
                    task_id=task_id,  # 使用task_id而不是task对象
                    source_path=file_path,
                    target_path="",
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )

            return False, error_msg, strm_file

    async def download_resource_file(
        self, file_info: Dict[str, Any], task_id: Optional[int] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载单个资源文件，与generate_strm类似，但专用于资源文件

        Args:
            file_info: 文件信息
            task_id: 关联的任务ID

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote

            file_path = file_info["path"]

            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)

            os.makedirs(resource_dir, exist_ok=True)

            file_type = file_info.get("file_type", FileType.OTHER)

            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            self.logger.info("SSL验证已禁用")

            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            self.logger.error(error_msg)

                            strm_file = None
                            if task_id:
                                strm_file = StrmFile(
                                    task_id=task_id,  # 使用task_id而不是task对象
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        content = await response.read()

                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        strm_file = None
                        if task_id:
                            strm_file = StrmFile(
                                task_id=task_id,  # 使用task_id而不是task对象
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=len(content),
                                is_success=True,
                            )

                        return True, None, strm_file
                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    self.logger.error(error_msg)

                    strm_file = None
                    if task_id:
                        strm_file = StrmFile(
                            task_id=task_id,  # 使用task_id而不是task对象
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            self.logger.error(error_msg)

            strm_file = None
            if task_id:
                strm_file = StrmFile(
                    task_id=task_id,  # 使用task_id而不是task对象
                    source_path=file_path if "file_path" in locals() else "",
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    async def process_files(
        self,
        files: List[Dict[str, Any]],
        task_id: Optional[int] = None,
        download_resources: bool = False,
        download_server: Optional[MediaServer] = None,
        verbose_console_logging: bool = False,
    ) -> Dict[str, Any]:
        """
        处理文件列表，生成STRM文件

        Args:
            files: 文件列表
            task_id: 关联的任务ID
            download_resources: 是否下载资源文件
            download_server: 下载服务器

        Returns:
            处理结果
        """
        from app.models.strm import DownloadTask, DownloadTaskStatus

        log.info(f"开始处理文件列表: task_id={task_id if task_id else None}, 文件数量={len(files)}")

        total_files = 0
        video_files = 0
        processed_files = 0
        success_files = 0
        failed_files = 0
        start_time = time.time()

        for file_info in files:
            if file_info.get("is_dir", False):
                continue
            file_type = self.get_file_type(file_info.get("path", ""))
            file_info["file_type"] = file_type
            if file_type == FileType.VIDEO:
                video_files += 1
                total_files += 1

        log.info(f"文件统计: 总文件数={total_files}, 视频文件数={video_files}")

        if task_id:
            task = await StrmTask.get_or_none(id=task_id)
            if task and task.total_files == 0:
                await StrmTask.filter(id=task_id).update(total_files=video_files)
                log.info(f"更新任务总文件数: {video_files}")

        for file_info in files:
            try:
                if file_info.get("is_dir", False):
                    log.debug(f"跳过目录: {file_info.get('path', '')}")
                    continue

                processed_files += 1
                file_path = file_info.get("path", "")
                file_type = file_info.get("file_type", self.get_file_type(file_path))

                if file_type != FileType.VIDEO:
                    log.debug(f"跳过非视频文件: {file_path}, 类型: {file_type}")
                    continue

                success = False
                error_message = None
                strm_file_obj = None
                target_path = None

                try:
                    success, error_message, strm_file_obj = self.generate_strm(file_info, task_id)
                    if success:
                        success_files += 1
                        # 获取生成的STRM文件路径
                        if strm_file_obj and hasattr(strm_file_obj, "target_path"):
                            target_path = strm_file_obj.target_path

                except Exception as e:
                    failed_files += 1
                    error_detail = traceback.format_exc()
                    log.error(f"生成STRM文件时发生错误: {str(e)}\n{error_detail}")

                    # 创建失败的下载任务记录
                    if task_id and file_path:
                        try:
                            from app.models.strm import StrmTask

                            await DownloadTask.create(
                                task=StrmTask(id=task_id),  # 使用task字段而不是task_id
                                source_path=file_path,
                                target_path="",
                                file_type="strm",
                                status=DownloadTaskStatus.FAILED,
                                error_message=str(e),
                            )
                            log.debug(f"已为异常失败的STRM文件创建下载任务记录, 源路径: {file_path}")
                        except Exception as dt_error:
                            log.error(f"创建异常失败的STRM文件下载任务记录时发生错误: {str(dt_error)}")

                if task_id:
                    try:
                        task = await StrmTask.get_or_none(id=task_id)
                        if task:
                            task.processed_files = processed_files
                            task.success_files = success_files
                            task.failed_files = failed_files
                            await task.save(update_fields=["processed_files", "success_files", "failed_files"])
                    except Exception as e:
                        log.error(f"更新任务进度时发生错误: {str(e)}")

            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"处理文件项时发生未捕获的异常: {str(e)}\n{error_detail}")

        end_time = time.time()
        duration = end_time - start_time

        log.info(
            f"文件处理完成, 耗时: {duration:.2f}秒, 总文件: {video_files}, 成功: {success_files}, 失败: {failed_files}"
        )

        if task_id:
            try:
                task = await StrmTask.get_or_none(id=task_id)
                if task:
                    task.end_time = datetime.now()
                    task.status = TaskStatus.COMPLETED
                    await task.save(update_fields=["end_time", "status"])
                    log.info(f"已更新任务状态为已完成: {task.id}")
            except Exception as e:
                log.error(f"更新任务完成状态时发生错误: {str(e)}")

        return {
            "total": video_files,
            "success": success_files,
            "failed": failed_files,
            "processed": processed_files,
            "time": duration,
        }

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                path_parts[1] = self.replacement_path.strip("/")
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task_id: Optional[int] = None,
    download_server_id: Optional[int] = None,
    threads: int = 1,
    verbose_console_logging: bool = False,
) -> Dict[str, Any]:
    """
    处理文件目录树，生成STRM文件和下载资源文件

    Args:
        server_id: 媒体服务器ID
        files: 文件列表
        output_dir: 输出目录
        task_id: 任务ID
        download_server_id: 下载服务器ID
        threads: 下载线程数
        verbose_console_logging: 是否输出详细日志到控制台

    Returns:
        处理结果
    """
    try:
        # 获取媒体服务器
        server = await MediaServer.get_or_none(id=server_id)
        if not server:
            raise ValueError(f"找不到ID为{server_id}的媒体服务器")

        # 获取下载服务器（如果有）
        download_server = None
        if download_server_id:
            download_server = await MediaServer.get_or_none(id=download_server_id)
            if not download_server:
                raise ValueError(f"找不到ID为{download_server_id}的下载服务器")
        else:
            # 默认使用媒体服务器作为下载服务器
            download_server = server

        # 获取任务
        task = None
        if task_id:
            task = await StrmTask.get_or_none(id=task_id)
            if not task:
                # 添加错误日志
                log.error(f"找不到ID为{task_id}的任务")
                raise ValueError(f"找不到ID为{task_id}的任务")
            else:
                # 添加详细的调试日志
                print(f"[DEBUG] 获取到任务对象: ID={task.id}, 名称={task.name}, 类型={type(task)}")
                print(f"[DEBUG] 任务详情: 状态={task.status}, 总文件数={task.total_files}, 输出目录={task.output_dir}")

        # 获取系统设置
        settings = await system_settings_controller.get_settings()
        settings = SimpleNamespace(**settings)
        # 从数据库获取下载任务项
        if task_id:
            strm_tasks = await DownloadTask.filter(task__id=task_id, process_type="strm_generation").all()
        # 创建STRM处理器，用于媒体服务器
        strm_processor = StrmProcessor(
            server=server,
            output_dir=output_dir,
            enable_path_replacement=settings.enable_path_replacement,
            replacement_path=settings.replacement_path,
            verbose_console_logging=verbose_console_logging,
            settings_dict=settings,
        )

        # 创建资源下载处理器，用于下载服务器
        resource_processor = StrmProcessor(
            server=download_server,
            output_dir=output_dir,
            enable_path_replacement=settings.enable_path_replacement,
            replacement_path=settings.replacement_path,
            verbose_console_logging=verbose_console_logging,
            settings_dict=settings,
        )

        # 记录开始时间
        start_time = datetime.now()

        # 统计数据
        stats = {
            "total": len(files),
            "processed": 0,
            "success_strm": 0,
            "failed_strm": 0,
            "success_download": 0,
            "failed_download": 0,
            "time": 0,
        }

        # 从数据库获取下载任务项
        download_tasks = []
        if task_id:
            download_tasks = await DownloadTask.filter(task__id=task_id).all()
            # 添加详细的调试日志
            print(f"[DEBUG] 获取到下载任务项: 数量={len(download_tasks)}, 任务ID={task_id}")

        # 计算处理时间
        end_time = datetime.now()
        elapsed_time = (end_time - start_time).total_seconds()
        stats["time"] = elapsed_time

        # 更新任务状态为已完成
        if task:
            task.status = TaskStatus.COMPLETED
            task.end_time = end_time
            task.processed_files = stats["processed"]
            task.success_files = stats["success_strm"] + stats["success_download"]
            task.failed_files = stats["failed_strm"] + stats["failed_download"]
            task.download_duration = elapsed_time

            # 生成处理总结
            summary = (
                f"处理完成。总耗时: {elapsed_time:.2f} 秒\n"
                f"总文件数: {stats['total']}\n"
                f"成功生成STRM文件数: {stats['success_strm']}\n"
                f"失败生成STRM文件数: {stats['failed_strm']}\n"
                f"成功下载资源文件数: {stats['success_download']}\n"
                f"失败下载资源文件数: {stats['failed_download']}"
            )
            await task.log(summary, level="INFO")

            await task.save()

        return stats
    except Exception as e:
        # 记录整个处理过程的错误
        log.error(f"处理文件目录树时发生错误: {str(e)}")
        print(f"[ERROR] 处理文件目录树时发生错误: {str(e)}")
        print(f"[ERROR] 异常类型: {type(e)}")
        print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")

        # 更新任务状态为失败
        if task_id:
            task = await StrmTask.get_or_none(id=task_id)
            if task:
                task.status = TaskStatus.FAILED
                await task.log(f"处理文件目录树时发生错误: {str(e)}", level="ERROR")
                await task.save()

        # 重新抛出异常，让上层处理
        raise


# 辅助方法处理单个STRM任务
async def _process_strm_task(
    processor: StrmProcessor,
    file_info: Dict[str, Any],
    download_task: DownloadTask,
    task: Optional[StrmTask],
    stats: Dict[str, Any],
) -> None:
    try:
        # 添加调试日志
        log.debug(f"_process_strm_task: task类型={type(task)}, download_task.task_id={download_task.task_id}")
        print(f"[DEBUG] _process_strm_task: task类型={type(task)}, download_task.task_id={download_task.task_id}")

        # 更新下载任务状态
        download_task.status = DownloadTaskStatus.DOWNLOADING
        download_task.download_started = datetime.now()
        await download_task.save(update_fields=["status", "download_started"])

        # 获取任务ID - 添加检查确保task不是QuerySet
        task_id = None
        if task:
            # 检查是否是QuerySet
            if hasattr(task, "all") and callable(task.all):
                log.warning("task是QuerySet而不是单个对象，尝试获取第一个对象")
                task_obj = await task.first()
                if task_obj:
                    task = task_obj
                    task_id = task.id
                else:
                    log.error("QuerySet中没有对象")
                    task_id = download_task.task_id
            elif hasattr(task, "id"):
                task_id = task.id
            else:
                log.warning("task对象没有id属性")
                task_id = download_task.task_id
        else:
            task_id = download_task.task_id

        log.debug(f"_process_strm_task: 使用task_id={task_id}")
        print(f"[DEBUG] _process_strm_task: 使用task_id={task_id}")

        # 生成STRM文件
        success, error_msg, strm_file = processor.generate_strm(file_info, task_id)

        # 更新统计信息
        stats["processed"] += 1
        if success:
            stats["success_strm"] += 1

            # 更新下载任务状态为成功
            download_task.status = DownloadTaskStatus.COMPLETED
            download_task.target_path = strm_file.target_path if strm_file else None
            download_task.download_completed = datetime.now()
            download_task.download_duration = (
                download_task.download_completed - download_task.download_started
            ).total_seconds()
            await download_task.save()

            if task and hasattr(task, "log") and callable(task.log):
                await task.log(f"成功生成STRM文件: {download_task.source_path}", level="INFO")
        else:
            stats["failed_strm"] += 1

            # 更新下载任务状态为失败
            download_task.status = DownloadTaskStatus.FAILED
            download_task.error_message = error_msg
            download_task.download_completed = datetime.now()
            await download_task.save()

            if task and hasattr(task, "log") and callable(task.log):
                await task.log(f"生成STRM文件失败: {download_task.source_path}, 原因: {error_msg}", level="ERROR")
    except Exception as e:
        # 更新统计信息
        stats["processed"] += 1
        stats["failed_strm"] += 1

        # 输出错误信息
        print(f"[ERROR] _process_strm_task 处理失败: {str(e)}")
        print(f"[ERROR] 异常类型: {type(e)}")
        print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")

        # 更新下载任务状态为失败
        download_task.status = DownloadTaskStatus.FAILED
        download_task.error_message = str(e)
        download_task.download_completed = datetime.now()
        await download_task.save()

        if task and hasattr(task, "log") and callable(task.log):
            await task.log(f"处理STRM文件时发生错误: {download_task.source_path}, 错误: {str(e)}", level="ERROR")


# 辅助方法处理单个下载任务
async def _process_download_task(
    processor: StrmProcessor,
    file_info: Dict[str, Any],
    download_task: DownloadTask,
    task: Optional[StrmTask],
    stats: Dict[str, Any],
) -> None:
    try:
        # 添加调试日志
        log.debug(f"_process_download_task: task类型={type(task)}, download_task.task_id={download_task.task_id}")
        print(f"[DEBUG] _process_download_task: task类型={type(task)}, download_task.task_id={download_task.task_id}")

        # 更新下载任务状态
        download_task.status = DownloadTaskStatus.DOWNLOADING
        download_task.download_started = datetime.now()
        await download_task.save(update_fields=["status", "download_started"])

        # 获取任务ID - 添加检查确保task不是QuerySet
        task_id = None
        if task:
            # 检查是否是QuerySet
            if hasattr(task, "all") and callable(task.all):
                log.warning("task是QuerySet而不是单个对象，尝试获取第一个对象")
                task_obj = await task.first()
                if task_obj:
                    task = task_obj
                    task_id = task.id
                else:
                    log.error("QuerySet中没有对象")
                    task_id = download_task.task_id
            elif hasattr(task, "id"):
                task_id = task.id
            else:
                log.warning("task对象没有id属性")
                task_id = download_task.task_id
        else:
            task_id = download_task.task_id

        log.debug(f"_process_download_task: 使用task_id={task_id}")
        print(f"[DEBUG] _process_download_task: 使用task_id={task_id}")

        # 下载资源文件
        success, error_msg, download_file = await processor.download_resource_file(file_info, task_id)

        # 更新统计信息
        stats["processed"] += 1
        if success:
            stats["success_download"] += 1

            # 更新下载任务状态为成功
            download_task.status = DownloadTaskStatus.COMPLETED
            download_task.target_path = download_file.target_path if download_file else None
            download_task.download_completed = datetime.now()
            download_task.download_duration = (
                download_task.download_completed - download_task.download_started
            ).total_seconds()
            await download_task.save()

            if task and hasattr(task, "log") and callable(task.log):
                await task.log(f"成功下载资源文件: {download_task.source_path}", level="INFO")
        else:
            stats["failed_download"] += 1

            # 更新下载任务状态为失败
            download_task.status = DownloadTaskStatus.FAILED
            download_task.error_message = error_msg
            download_task.download_completed = datetime.now()
            await download_task.save()

            if task and hasattr(task, "log") and callable(task.log):
                await task.log(f"下载资源文件失败: {download_task.source_path}, 原因: {error_msg}", level="ERROR")
    except Exception as e:
        # 更新统计信息
        stats["processed"] += 1
        stats["failed_download"] += 1

        # 输出错误信息
        print(f"[ERROR] _process_download_task 处理失败: {str(e)}")
        print(f"[ERROR] 异常类型: {type(e)}")
        print(f"[ERROR] 异常堆栈: {traceback.format_exc()}")

        # 更新下载任务状态为失败
        download_task.status = DownloadTaskStatus.FAILED
        download_task.error_message = str(e)
        download_task.download_completed = datetime.now()
        await download_task.save()

        if task and hasattr(task, "log") and callable(task.log):
            await task.log(f"处理下载文件时发生错误: {download_task.source_path}, 错误: {str(e)}", level="ERROR")


def is_summary_log(message: str) -> bool:
    """
    判断是否为汇总日志

    Args:
        message: 日志消息

    Returns:
        是否为汇总日志
    """
    summary_keywords = [
        "总耗时",
        "下载完成",
        "任务统计",
        "总文件数",
        "成功文件数",
        "失败文件数",
        "平均速度",
        "汇总",
        "总计",
        "summary",
        "开始多线程下载",
        "队列中文件数",
    ]

    return any(keyword in message for keyword in summary_keywords)


class strm_downaload:
    def __init__(
        self,
        maxsize: int,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        settings_dict: Optional[Dict[str, Any]] = None,
    ):
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        # 初始化下载队列和信号量
        download_queue = queue.Queue(maxsize=maxsize)
        download_semaphore = threading.Semaphore(maxsize)

    async def genstrm(self):
        pass
