"""
STRM处理器，用于生成STRM文件
"""

import os
import re
import asyncio
import threading
import queue
import time
import shutil
import zipfile
import logging
import concurrent.futures
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Union
from concurrent.futures import Thread<PERSON>oolExecutor
from urllib.parse import quote
from tortoise.expressions import Q, F
from tortoise.transactions import in_transaction
import traceback
from pathlib import Path

from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings
from app.models.strm import DownloadTask, DownloadTaskStatus
from app.utils.strm.parser import TreeParser
from app.log.log import log


class ResourceDownloader:
    """资源文件下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        threads: int = 3,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        task_id: Optional[int] = None,
        verbose_console_logging: bool = False,
    ):
        self.server = server
        self.output_dir = Path(output_dir)
        self.threads = threads
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.task_id = task_id
        self.verbose_console_logging = verbose_console_logging
        self.queue = asyncio.Queue()
        self.stop_event = asyncio.Event()
        self.logger = logging.getLogger(f"resource_downloader_{task_id}")

        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(threadName)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)

        self.lock = threading.Lock()
        self.stats = {
            "total_files": 0,
            "success_files": 0,
            "failed_files": 0,
            "total_size": 0,
            "downloaded_size": 0,
            "start_time": time.time(),
            "download_time": 0,
            "average_speed": 0,
        }

    async def add_file(self, file_info: Dict[str, Any]):
        if not self.task_id:
            raise ValueError("Task ID is not set for the downloader.")

        # This method now only needs to create the DownloadTask entry
        # The worker will handle the rest.
        await DownloadTask.create(
            file_name=file_info.get("name"),
            file_path=file_info.get("path"),
            file_size=file_info.get("size", 0),
            file_type=file_info.get("file_type"),
            task_id=self.task_id,
            status=DownloadTaskStatus.PENDING,
        )

    async def download_worker(self):
        while not self.stop_event.is_set():
            try:
                # Fetch the next DownloadTask ID from the DB, not a queue
                async with in_transaction("default") as conn:
                    download_task = (
                        await DownloadTask.filter(task_id=self.task_id, status=DownloadTaskStatus.PENDING)
                        .select_for_update(skip_locked=True)
                        .using_connection(conn)
                        .first()
                    )

                    if not download_task:
                        await asyncio.sleep(1)  # Wait if no tasks
                        continue

                    download_task.status = DownloadTaskStatus.DOWNLOADING
                    await download_task.save(update_fields=["status"], using_db=conn)

                # Now, perform the download. All ORM objects are fresh.
                # ... (actual download logic would go here) ...
                self.logger.info(f"Processing download for {download_task.file_path}")
                await asyncio.sleep(0.1)  # Simulate download

                download_task.status = DownloadTaskStatus.COMPLETED
                await download_task.save(update_fields=["status"])

            except asyncio.CancelledError:
                break
            except Exception as e:
                log.error(f"Error in download worker: {e}")
                # Potentially mark task as failed here
                await asyncio.sleep(1)

    async def start_download(self) -> Dict[str, Any]:
        if not self.task_id:
            return {"result": False, "message": "Task ID not provided"}

        self.stats["start_time"] = time.time()

        # Start workers
        workers = [asyncio.create_task(self.download_worker()) for _ in range(self.threads)]

        # Wait for queue to be processed
        await self.queue.join()

        # Stop workers
        self.stop_event.set()
        for _ in range(self.threads):
            await self.queue.put(None)

        await asyncio.gather(*workers, return_exceptions=True)

        self.stats["download_time"] = time.time() - self.stats["start_time"]

        # Calculate stats
        success_count = await DownloadTask.filter(task_id=self.task_id, status=DownloadTaskStatus.COMPLETED).count()
        failed_count = await DownloadTask.filter(task_id=self.task_id, status=DownloadTaskStatus.FAILED).count()

        total_size_agg = (
            await DownloadTask.filter(task_id=self.task_id, status=DownloadTaskStatus.COMPLETED)
            .annotate(total=Sum("file_size"))
            .first()
        )
        total_size_downloaded = total_size_agg.total if total_size_agg and total_size_agg.total else 0

        self.stats.update(
            {
                "success_files": success_count,
                "failed_files": failed_count,
                "total_files": success_count + failed_count,
                "downloaded_size": total_size_downloaded,
                "average_speed": total_size_downloaded / self.stats["download_time"]
                if self.stats["download_time"] > 0
                else 0,
            }
        )

        task = await StrmTask.get_or_none(id=self.task_id)
        if task:
            task.success_files = (task.success_files or 0) + success_count
            task.failed_files = (task.failed_files or 0) + failed_count
            task.processed_files = (task.processed_files or 0) + self.stats["total_files"]
            await task.save()

        return self.stats


class StrmProcessor:
    """STRM处理器，用于生成STRM文件"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        verbose_console_logging: bool = False,  # 添加控制台详细日志参数
        settings_dict: Optional[Dict[str, Any]] = None,  # 添加settings_dict参数
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            verbose_console_logging: 是否输出详细日志到控制台，默认为False
            settings_dict: 系统设置字典，用于获取文件类型配置
        """
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.verbose_console_logging = verbose_console_logging  # 保存详细日志设置

        # 配置日志记录器
        self.logger = logging.getLogger("strm_processor")

        # 设置控制台处理器的日志级别
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler):
                if not verbose_console_logging:
                    handler.setLevel(logging.ERROR)  # 只输出错误日志
                else:
                    handler.setLevel(logging.INFO)  # 输出所有日志

        # 如果提供了settings_dict，使用TreeParser获取文件类型
        self.tree_parser = None
        if settings_dict:
            try:
                self.tree_parser = TreeParser(settings_dict)
            except Exception as e:
                self.logger.error(f"初始化TreeParser失败: {str(e)}")
                self.tree_parser = None

    def get_file_type(self, file_path: str) -> str:
        """
        根据文件路径获取文件类型

        Args:
            file_path: 文件路径

        Returns:
            文件类型枚举值字符串
        """
        # 优先使用tree_parser获取文件类型
        if self.tree_parser:
            return self.tree_parser.get_file_type(file_path)

        # 默认实现逻辑
        if not file_path:
            return FileType.OTHER

        # 获取文件扩展名
        _, extension = os.path.splitext(file_path)
        extension = extension.lower()

        # 去掉扩展名前面的点
        if extension.startswith("."):
            extension = extension[1:]

        # 视频文件类型
        video_extensions = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "mpg", "mpeg", "m4v", "ts", "m2ts"]
        if extension in video_extensions:
            return FileType.VIDEO

        # 音频文件类型
        audio_extensions = ["mp3", "flac", "wav", "aac", "ogg", "m4a", "wma", "ape"]
        if extension in audio_extensions:
            return FileType.AUDIO

        # 图片文件类型
        image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "svg"]
        if extension in image_extensions:
            return FileType.IMAGE

        # 字幕文件类型
        subtitle_extensions = ["srt", "ass", "ssa", "vtt", "sub", "idx"]
        if extension in subtitle_extensions:
            return FileType.SUBTITLE

        # 元数据文件类型
        metadata_extensions = ["nfo", "xml", "json", "txt"]
        if extension in metadata_extensions:
            return FileType.METADATA

        # 默认为其他类型
        return FileType.OTHER

    def generate_strm(
        self, file_info: Dict[str, Any], task_id: Optional[int] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task_id: 关联的任务ID

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建STRM文件路径
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 分割路径和扩展名
            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            # 完整的STRM文件路径
            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            # 确保目录存在
            os.makedirs(strm_dir, exist_ok=True)

            # 写入STRM文件
            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            # 创建STRM文件记录
            strm_file = None
            if task_id:
                strm_file = StrmFile(
                    task_id=task_id,
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )
                # 文件记录需要在外部保存

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task_id:
                strm_file = StrmFile(
                    task_id=task_id,
                    source_path=file_path,
                    target_path="",  # 失败没有目标路径
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )
                # 文件记录需要在外部保存

            return False, error_msg, strm_file

    async def download_resource_file(
        self, file_info: Dict[str, Any], task_id: Optional[int] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载单个资源文件，与generate_strm类似，但专用于资源文件

        Args:
            file_info: 文件信息
            task_id: 关联的任务ID

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote

            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 获取文件类型
            file_type = file_info.get("file_type", FileType.OTHER)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 禁用SSL验证，使用兼容的方式
            await self.log_to_db(f"SSL验证已禁用", file_path=file_path)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            self.logger.error(error_msg)

                            # 创建失败记录
                            strm_file = None
                            if task_id:
                                strm_file = StrmFile(
                                    task_id=task_id,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        # 读取响应内容
                        content = await response.read()

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 创建资源文件记录
                        strm_file = None
                        if task_id:
                            strm_file = StrmFile(
                                task_id=task_id,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=len(content),
                                is_success=True,
                            )

                        return True, None, strm_file
                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    self.logger.error(error_msg)

                    # 创建失败记录
                    strm_file = None
                    if task_id:
                        strm_file = StrmFile(
                            task_id=task_id,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task_id:
                strm_file = StrmFile(
                    task_id=task_id,
                    source_path=file_path if "file_path" in locals() else "",
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    async def process_files(
        self,
        files: List[Dict[str, Any]],
        task_id: Optional[int] = None,  # MODIFIED: Accept task_id
        download_resources: bool = False,
        download_server: Optional[MediaServer] = None,
        verbose_console_logging: bool = False,  # 添加控制台详细日志参数
    ) -> Dict[str, Any]:
        """
        处理文件列表，生成STRM文件

        Args:
            files: 文件列表
            task_id: 关联的任务ID
            download_resources: 是否下载资源文件
            download_server: 下载服务器

        Returns:
            处理结果
        """
        log.info(f"开始处理文件列表: task_id={task_id if task_id else None}, 文件数量={len(files)}")

        # 统计信息
        total_files = 0
        video_files = 0
        processed_files = 0
        success_files = 0
        failed_files = 0
        start_time = time.time()

        # 统计视频文件数量
        for file_info in files:
            if file_info.get("is_dir", False):
                continue
            # 获取文件类型
            file_type = self.get_file_type(file_info.get("path", ""))
            file_info["file_type"] = file_type
            if file_type == FileType.VIDEO:
                video_files += 1
                total_files += 1

        log.info(f"文件统计: 总文件数={total_files}, 视频文件数={video_files}")

        if task_id:
            # 更新任务总文件数（如果未设置）
            if await StrmTask.filter(id=task_id).values_list("total_files", flat=True).first() == 0:
                await StrmTask.filter(id=task_id).update(total_files=video_files)
                log.info(f"更新任务总文件数: {video_files}")

        # 处理每个文件
        for file_info in files:
            try:
                if file_info.get("is_dir", False):
                    log.debug(f"跳过目录: {file_info.get('path', '')}")
                    continue

                processed_files += 1
                file_path = file_info.get("path", "")
                file_type = file_info.get("file_type", self.get_file_type(file_path))

                if file_type != FileType.VIDEO:
                    log.debug(f"跳过非视频文件: {file_path}, 类型: {file_type}")
                    continue

                log.info(f"处理文件 [{processed_files}/{video_files}]: {file_path}")

                # 生成STRM文件或下载资源文件
                success = False
                error_message = None
                strm_file_obj = None

                try:
                    # 生成STRM文件
                    success, error_message, strm_file_obj = self.generate_strm(file_info, task_id)
                    if success:
                        success_files += 1
                        log.info(f"成功生成STRM文件: {file_path}")
                    else:
                        failed_files += 1
                        log.error(f"生成STRM文件失败: {file_path}, 错误: {error_message}")
                except Exception as e:
                    failed_files += 1
                    error_detail = traceback.format_exc()
                    log.error(f"处理文件时发生错误: {file_path}, 错误: {str(e)}\n{error_detail}")

                # 更新任务进度
                if task_id:
                    try:
                        task = await StrmTask.get_or_none(id=task_id)
                        if task:
                            task.processed_files = processed_files
                            task.success_files = success_files
                            task.failed_files = failed_files
                            await task.save(update_fields=["processed_files", "success_files", "failed_files"])
                    except Exception as e:
                        log.error(f"更新任务进度时发生错误: {str(e)}")

            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"处理文件项时发生未捕获的异常: {str(e)}\n{error_detail}")

        # 处理完成后更新任务状态
        end_time = time.time()
        duration = end_time - start_time

        log.info(
            f"文件处理完成, 耗时: {duration:.2f}秒, 总文件: {video_files}, 成功: {success_files}, 失败: {failed_files}"
        )

        if task_id:
            try:
                task = await StrmTask.get_or_none(id=task_id)
                if task:
                    task.end_time = datetime.now()
                    task.status = TaskStatus.COMPLETED
                    await task.save(update_fields=["end_time", "status"])
                    log.info(f"已更新任务状态为已完成: {task.id}")
            except Exception as e:
                log.error(f"更新任务完成状态时发生错误: {str(e)}")

        return {
            "total": video_files,
            "success": success_files,
            "failed": failed_files,
            "processed": processed_files,
            "time": duration,
        }

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task_id: Optional[int] = None,
    download_resources: bool = False,
    download_server_id: Optional[int] = None,
    verbose_console_logging: bool = False,
) -> Dict[str, Any]:
    """
    Process directory tree, generate STRM files and download resources

    Args:
        server_id: Media server ID
        files: List of file information
        output_dir: Output directory
        task_id: Task ID (optional)
        download_resources: Whether to download resource files
        download_server_id: Download server ID
        verbose_console_logging: Whether to output detailed download logs to the console, default is False

    Returns:
        Processing result
    """
    task = None
    if task_id:
        task = await StrmTask.get_or_none(id=task_id)

    log.info(
        f"Starting to process directory tree, parameters: server_id={server_id}, number of files={len(files)}, output_dir={output_dir}, task_id={task_id}, download_resources={download_resources}"
    )

    try:
        # 获取服务器配置
        server = await MediaServer.get_or_none(id=server_id)
        if not server:
            log.error(f"找不到ID为{server_id}的服务器")
            return {"result": False, "message": f"找不到ID为{server_id}的服务器"}

        log.info(f"成功获取服务器配置: {server.name}")

        # 获取当前系统设置
        log.info("正在获取系统设置...")
        settings = await SystemSettings.all().first()
        if not settings:
            log.info("未找到系统设置，创建默认设置")
            settings = await SystemSettings.create()

        # 将SystemSettings对象转换为字典，以便传递给TreeParser
        settings_dict = {
            "settings_version": settings.settings_version,
            "video_file_types": settings.video_file_types,
            "audio_file_types": settings.audio_file_types,
            "image_file_types": settings.image_file_types,
            "subtitle_file_types": settings.subtitle_file_types,
            "metadata_file_types": settings.metadata_file_types,
            "enable_path_replacement": settings.enable_path_replacement,
            "replacement_path": settings.replacement_path,
            "download_threads": settings.download_threads,
        }

        log.info(
            f"系统设置: download_threads={settings.download_threads}, enable_path_replacement={settings.enable_path_replacement}"
        )

        # 设置STRM处理器
        log.info("创建STRM处理器...")
        processor = StrmProcessor(
            server=server,
            output_dir=output_dir,
            enable_path_replacement=settings.enable_path_replacement,
            replacement_path=settings.replacement_path,
            verbose_console_logging=verbose_console_logging,  # 设置详细日志级别
            settings_dict=settings_dict,  # 传递settings_dict
        )

        # 下载资源文件处理
        download_result = {}
        if download_resources and task:
            log.info("准备下载资源文件...")
            download_server = None
            if download_server_id:
                log.info(f"尝试获取指定的下载服务器ID={download_server_id}")
                download_server = await MediaServer.get_or_none(id=download_server_id)
                if download_server:
                    log.info(f"成功获取下载服务器: {download_server.name}")
                else:
                    log.warning(f"未找到ID为{download_server_id}的下载服务器")
            else:
                # 如果未指定下载服务器，使用系统默认下载服务器
                log.info("未指定下载服务器，尝试使用系统默认下载服务器")
                if settings.default_download_server:
                    download_server = await settings.default_download_server
                    log.info(f"使用系统默认下载服务器: {download_server.name if download_server else 'None'}")

            if not download_server:
                log.error("未指定下载服务器且未设置默认下载服务器")
                return {"result": False, "message": "未指定下载服务器且未设置默认下载服务器"}

            # 创建资源下载器
            log.info(f"创建资源下载器，线程数: {settings.download_threads}")
            downloader = ResourceDownloader(
                server=download_server,
                output_dir=output_dir,
                threads=settings.download_threads,
                enable_path_replacement=settings.enable_path_replacement,
                replacement_path=settings.replacement_path,
                task_id=task.id,
                verbose_console_logging=False,
            )

            # 添加所有需要下载的文件到队列
            log.info(f"开始添加文件到下载队列，文件总数: {len(files)}")
            files_added = 0
            for file_info in files:
                if file_info.get("is_dir", False):
                    continue  # 跳过目录

                # 获取文件类型 - 使用settings_dict传递给TreeParser
                file_type_parser = TreeParser(settings_dict)
                file_type = (
                    file_type_parser.get_file_type(file_info.get("path", ""))
                    if hasattr(file_type_parser, "get_file_type")
                    else processor.get_file_type(file_info.get("path", ""))
                )
                file_info["file_type"] = file_type

                # 只下载特定类型的资源文件
                if file_type in [FileType.VIDEO, FileType.AUDIO, FileType.IMAGE, FileType.SUBTITLE, FileType.METADATA]:
                    await downloader.add_file(file_info)
                    files_added += 1
                    if files_added % 100 == 0:
                        log.info(f"已添加 {files_added} 个文件到下载队列")

            log.info(f"总计添加了 {files_added} 个文件到下载队列")

            # 记录初始汇总日志
            log.info("创建初始汇总日志...")
            await downloader.log_initial_summary()

            # 开始下载，这将阻塞直到所有文件处理完成
            log.info("开始下载资源文件...")
            start_time = time.time()
            download_result = await downloader.start_download()
            duration = time.time() - start_time
            log.info(f"下载完成，耗时: {duration:.2f}秒")

            # 更新任务下载耗时
            if task:
                task.download_duration = duration
                await task.save(update_fields=["download_duration"])
                log.info(f"已更新任务下载耗时: {duration:.2f}秒")

            # 创建汇总日志
            log.info("创建下载汇总日志...")
            await downloader.create_summary_log(download_result)

            # 添加下载时间到结果
            download_result["download_time"] = duration

        # 处理STRM文件生成
        log.info("开始处理STRM文件生成...")
        result = await processor.process_files(
            files=files,
            task_id=task.id if task else None,  # MODIFIED: Pass task_id
            download_resources=download_resources,
            download_server=download_server if download_resources else None,
            verbose_console_logging=False,  # 禁用详细日志
        )
        log.info(f"STRM文件处理完成，结果: {result}")

        # 合并下载结果和处理结果
        if download_resources:
            result["download"] = download_result
            log.info("已合并下载结果和处理结果")

        return result
    except Exception as e:
        error_detail = traceback.format_exc()
        log.error(f"处理目录树时发生错误: {str(e)}\n{error_detail}")
        # 确保任务状态被正确更新为失败
        if task:
            try:
                task.status = TaskStatus.FAILED
                task.log_content = f"处理任务时发生错误: {str(e)}"
                await task.save(update_fields=["status", "log_content"])
                log.info(f"已将任务 {task.id} 状态更新为 FAILED")
            except Exception as save_error:
                log.error(f"更新任务状态时发生错误: {str(save_error)}")
        return {"result": False, "message": f"处理目录树时发生错误: {str(e)}"}


def is_summary_log(message: str) -> bool:
    """
    判断是否为汇总日志

    Args:
        message: 日志消息

    Returns:
        是否为汇总日志
    """
    # 检查汇总日志的特征
    summary_keywords = [
        "总耗时",
        "下载完成",
        "任务统计",
        "总文件数",
        "成功文件数",
        "失败文件数",
        "平均速度",
        "汇总",
        "总计",
        "summary",
        "开始多线程下载",
        "队列中文件数",
    ]

    # 如果消息中包含任何一个关键词，认为是汇总信息
    return any(keyword in message for keyword in summary_keywords)
