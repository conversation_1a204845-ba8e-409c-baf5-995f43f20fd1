"""
115目录树解析器，用于解析115导出的目录树文件
"""

import os
import re
from typing import List, Dict, Any, Optional, Tuple
import chardet

from app.models.strm import FileType, SystemSettings


class TreeParser:
    """115目录树解析器"""

    def __init__(self, settings=None):
        """
        初始化解析器

        Args:
            settings: 可选的系统设置对象，包含文件类型配置
            如果未提供，将使用默认值
        """
        # 如果提供了系统设置，使用系统设置中的文件类型配置
        if settings:
            # 使用系统设置中的文件类型配置
            self.video_extensions = (
                [ext.strip().lstrip(".") for ext in settings.video_file_types.split(",")]
                if settings.video_file_types
                else []
            )
            self.audio_extensions = (
                [ext.strip().lstrip(".") for ext in settings.audio_file_types.split(",")]
                if settings.audio_file_types
                else []
            )
            self.image_extensions = (
                [ext.strip().lstrip(".") for ext in settings.image_file_types.split(",")]
                if settings.image_file_types
                else []
            )
            self.subtitle_extensions = (
                [ext.strip().lstrip(".") for ext in settings.subtitle_file_types.split(",")]
                if settings.subtitle_file_types
                else []
            )
            self.metadata_extensions = (
                [ext.strip().lstrip(".") for ext in settings.metadata_file_types.split(",")]
                if settings.metadata_file_types
                else []
            )

            print(f"使用系统设置的文件类型配置，字幕文件扩展名: {self.subtitle_extensions}")
            print(f"元数据文件扩展名: {self.metadata_extensions}")
        else:
            # 仅当没有提供系统设置时，才使用默认值
            print("未提供系统设置，使用默认配置")
            self.video_extensions = ["mkv", "iso", "ts", "mp4", "avi", "rmvb", "wmv", "m2ts", "mpg", "flv", "rm", "mov"]
            self.audio_extensions = ["mp3", "flac", "wav", "aac", "ape", "ogg", "m4a"]
            self.image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "svg", "webp", "heic"]
            self.subtitle_extensions = ["srt", "ass", "ssa", "sub", "idx", "sup"]
            self.metadata_extensions = ["nfo", "xml", "json"]

            print(f"使用默认文件类型配置，字幕文件扩展名: {self.subtitle_extensions}")
            print(f"元数据文件扩展名: {self.metadata_extensions}")

    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析115导出的目录树文件

        Args:
            file_path: 文件路径

        Returns:
            解析后的文件列表，每项包含文件路径、类型等信息
        """
        try:
            result = []
            current_path_stack = []

            # Detect file encoding
            with open(file_path, "rb") as f:
                raw_data = f.read()
                encoding_result = chardet.detect(raw_data)
                encoding = encoding_result["encoding"] or "utf-8"

            # Decode the file content and process line by line
            file_content = raw_data.decode(encoding)
            for line in file_content.splitlines():
                # 移除 BOM 和多余空白
                line = line.lstrip("\ufeff").rstrip()

                # 计算目录级别
                line_depth = line.count("|")

                # 获取当前项名称
                item_name = line.split("|-")[-1].strip()
                if not item_name:
                    continue

                # 维护路径栈
                while len(current_path_stack) > line_depth:
                    current_path_stack.pop()

                if len(current_path_stack) == line_depth:
                    if current_path_stack:
                        current_path_stack.pop()

                current_path_stack.append(item_name)

                # 构建完整路径
                full_path = "/" + "/".join(current_path_stack)

                # 检查是否为目录
                filename = os.path.basename(full_path)
                if "." not in filename:
                    continue

                # 格式化路径 (移除第一段路径)
                formatted_path = self._format_file_path(full_path)

                # 获取文件类型
                file_type, extension = self._get_file_type(filename)

                if file_type is not None:
                    result.append(
                        {
                            "path": formatted_path,
                            "file_type": file_type,
                            "extension": extension,
                            "file_name": filename,
                            "directory": os.path.dirname(formatted_path),
                        }
                    )

            return result
        except Exception as e:
            raise ValueError(f"解析115目录树文件失败: {str(e)}")

    def _format_file_path(self, file_path: str) -> str:
        """
        格式化文件路径，移除第一段路径（通常是"根目录/nas"等）

        Args:
            file_path: 原始文件路径

        Returns:
            格式化后的文件路径
        """
        first_slash = file_path.find("/")
        if first_slash != -1:
            second_slash = file_path.find("/", first_slash + 1)
            if second_slash != -1:
                return file_path[second_slash:]
        return file_path

    def _get_file_type(self, filename: str) -> Tuple[Optional[str], str]:
        """
        根据文件名判断文件类型

        Args:
            filename: 文件名

        Returns:
            (文件类型, 文件扩展名)
        """
        _, extension = os.path.splitext(filename)
        extension = extension[1:].lower() if extension else ""
        print(f"文件名: {filename}, 扩展名: {extension}")

        # 使用配置的扩展名列表（可能来自系统设置）进行文件类型判断
        if extension in self.video_extensions:
            return "video", extension
        elif extension in self.audio_extensions:
            return "audio", extension
        elif extension in self.image_extensions:
            return "image", extension
        elif extension in self.subtitle_extensions:
            return "subtitle", extension
        elif extension in self.metadata_extensions:
            return "metadata", extension
        else:
            return "other", extension

    def filter_files(
        self,
        files: List[Dict[str, Any]],
        file_type: Optional[str] = None,
        keyword: Optional[str] = None,
        path_pattern: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        根据条件过滤文件

        Args:
            files: 文件列表
            file_type: 文件类型
            keyword: 关键词
            path_pattern: 路径模式

        Returns:
            过滤后的文件列表
        """
        result = files

        if file_type:
            result = [f for f in result if f["file_type"] == file_type]

        if keyword:
            result = [f for f in result if keyword.lower() in f["path"].lower()]

        if path_pattern:
            regex = re.compile(path_pattern)
            result = [f for f in result if regex.search(f["path"])]

        return result


async def parse_directory_tree(file_path: str, settings=None) -> List[Dict[str, Any]]:
    """
    解析115导出的目录树文件

    Args:
        file_path: 文件路径
        settings: 可选的系统设置对象，包含文件类型配置

    Returns:
        解析后的文件列表，每项包含文件路径、类型等信息
    """
    # 如果未提供系统设置，尝试从数据库获取
    if settings is None:
        try:
            from app.models.strm import SystemSettings

            settings = await SystemSettings.all().first()
            if settings:
                print("从数据库获取系统设置成功")
            else:
                print("数据库中没有系统设置，将使用默认配置")
        except Exception as e:
            print(f"获取系统设置失败: {str(e)}")

    # 创建解析器实例，传入系统设置
    parser = TreeParser(settings)
    return parser.parse_file(file_path)


def parse_directory_tree_sync(file_path: str, settings=None) -> List[Dict[str, Any]]:
    """
    同步解析115导出的目录树文件，适用于不能使用异步函数的场合

    Args:
        file_path: 文件路径
        settings: 可选的系统设置对象，包含文件类型配置

    Returns:
        解析后的文件列表，每项包含文件路径、类型等信息
    """
    # 创建解析器实例，传入系统设置
    parser = TreeParser(settings)
    return parser.parse_file(file_path)


async def test_file_type_detection():
    """
    用于测试文件类型检测的函数
    此函数展示如何根据系统设置正确识别文件类型
    """

    # 创建一个模拟的系统设置对象
    class MockSettings:
        def __init__(self):
            self.video_file_types = "mkv,mp4"
            self.audio_file_types = "mp3,wav"
            self.image_file_types = "jpg,png"
            self.subtitle_file_types = "srt,ass"
            self.metadata_file_types = "xml,json"  # 默认NFO不在元数据中

    # 测试 1: 使用默认配置
    parser1 = TreeParser()
    nfo_type1, _ = parser1._get_file_type("movie.nfo")
    print(f"默认配置下，.nfo文件类型为: {nfo_type1}")  # 应该是 "metadata"

    # 测试 2: 使用自定义配置，将NFO配置为元数据类型
    settings2 = MockSettings()
    settings2.metadata_file_types = "nfo,xml,json"
    parser2 = TreeParser(settings2)
    nfo_type2, _ = parser2._get_file_type("movie.nfo")
    print(f"将NFO配置为元数据时，.nfo文件类型为: {nfo_type2}")  # 应该是 "metadata"

    # 测试 3: 使用自定义配置，将NFO配置为字幕类型
    settings3 = MockSettings()
    settings3.subtitle_file_types = "nfo,srt,ass"  # NFO作为字幕类型
    settings3.metadata_file_types = "xml,json"  # 元数据中不包含NFO
    parser3 = TreeParser(settings3)
    nfo_type3, _ = parser3._get_file_type("movie.nfo")
    print(f"将NFO配置为字幕时，.nfo文件类型为: {nfo_type3}")  # 应该是 "subtitle"

    return {"default_nfo_type": nfo_type1, "metadata_nfo_type": nfo_type2, "subtitle_nfo_type": nfo_type3}
