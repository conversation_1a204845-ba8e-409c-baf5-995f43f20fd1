"""
STRM处理器，用于生成STRM文件
"""

import os
import re
import asyncio
import threading
import queue
import time
import shutil
import zipfile
import logging
import concurrent.futures
from datetime import datetime, timedelta
from typing import List, Dict, Any, Tuple, Optional, Union
from concurrent.futures import ThreadPoolExecutor
from urllib.parse import quote
from tortoise.expressions import Q, F
from tortoise.transactions import in_transaction
import traceback

from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings
from app.models.strm import DownloadTask, DownloadTaskStatus
from app.utils.strm.parser import TreeParser
from app.log.log import log


class ResourceDownloader:
    """资源文件多线程下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        threads: int = 3,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        task: Optional[StrmTask] = None,
        verbose_console_logging: bool = False,  # 添加参数控制是否输出详细日志到控制台
    ):
        """
        初始化资源文件下载器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            threads: 下载线程数
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            task: 关联的任务，用于记录日志
            verbose_console_logging: 是否在控制台输出详细的下载日志，默认为False
        """
        self.server = server
        self.output_dir = output_dir
        self.threads = max(1, threads)  # 至少1个线程
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.task = task  # 保存任务引用
        self.verbose_console_logging = False  # 强制禁用控制台详细日志，忽略传入的参数

        # 配置只输出到控制台的日志记录器
        logger_name = f"resource_downloader_{task.id if task else ''}"
        self.logger = logging.getLogger(logger_name)

        # 确保logger不会继承父日志处理器（防止输出到文件）
        self.logger.propagate = False

        # 清除已有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 只添加控制台处理器，但设置为ERROR级别以上（不显示INFO级别的下载消息）
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.CRITICAL)  # 只显示CRITICAL级别的消息
        self.logger.setLevel(logging.CRITICAL)  # 设置为CRITICAL级别
        self.logger.addHandler(console_handler)

        # 下载状态
        self.lock = threading.Lock()
        self.success_count = 0  # 这些统计值现在只是临时的，最终会从数据库中计算
        self.failed_count = 0
        self.results = []  # 保存下载结果，将来可以考虑直接从数据库读取
        self.stop_event = threading.Event()  # 用于通知线程停止下载

    async def add_file(self, file_info: Dict[str, Any], task: Optional[StrmTask] = None):
        """
        添加文件到下载队列（数据库）

        Args:
            file_info: 文件信息
            task: 关联的任务
        """
        # 确保有任务
        if task is None:
            task = self.task

        if task is None:
            raise ValueError("必须提供关联的任务")

        # 创建下载任务记录
        await DownloadTask.create(
            task=task,
            source_path=file_info.get("path", ""),
            file_type=file_info.get("file_type", "other"),
            status=DownloadTaskStatus.PENDING,
            priority=file_info.get("priority", 0),
            max_attempts=3,  # 默认最多尝试3次
        )

    async def download_worker(self):
        """下载线程工作函数（异步版本）"""
        thread_name = threading.current_thread().name
        worker_id = f"{thread_name}-{time.time()}"

        while not self.stop_event.is_set():
            try:
                # 尝试从数据库获取一个待处理的下载任务
                async with in_transaction():
                    # 查找状态为PENDING或RETRY且retry_after为空或已过期的任务
                    download_task = (
                        await DownloadTask.filter(
                            Q(status=DownloadTaskStatus.PENDING)
                            | Q(status=DownloadTaskStatus.RETRY, retry_after__lte=datetime.now())
                        )
                        .filter(task_id=self.task.id, worker_id__isnull=True)
                        .order_by("priority", "created_at")
                        .first()
                    )

                    if download_task:
                        # 标记该任务正在被此worker处理
                        download_task.status = DownloadTaskStatus.DOWNLOADING
                        download_task.worker_id = worker_id
                        download_task.download_started = datetime.now()
                        download_task.attempt_count += 1
                        await download_task.save()
                    else:
                        # 没有可处理的任务，等待一秒后重试
                        await asyncio.sleep(1)
                        continue

                # 检查是否已经设置了停止标志
                if self.stop_event.is_set():
                    # 任务已取消，将任务状态恢复
                    download_task.status = DownloadTaskStatus.PENDING
                    download_task.worker_id = None
                    await download_task.save()
                    continue

                # 记录开始处理该文件的日志
                await self.log_to_db(
                    message=f"线程 {thread_name} | 开始处理文件",
                    level="INFO",
                    file_path=download_task.source_path,
                    file_type=download_task.file_type,
                    is_success=True,
                )

                try:
                    # 创建文件保存目录
                    target_path = self.get_output_path(download_task.source_path)
                    os.makedirs(os.path.dirname(target_path), exist_ok=True)

                    # 更新目标路径
                    download_task.target_path = target_path
                    await download_task.save()

                    # 构建下载URL
                    url = self.get_download_url(download_task.source_path)

                    # 执行下载
                    result = self.download_file(url, target_path)

                    # 解析结果
                    is_success = result.get("is_success", False)
                    error_message = result.get("error_message", None)
                    file_size = result.get("file_size", 0)
                    download_time = result.get("download_time", 0)
                    download_speed = result.get("download_speed", 0)

                    # 更新下载任务状态和统计
                    now = datetime.now()
                    download_task.status = DownloadTaskStatus.COMPLETED if is_success else DownloadTaskStatus.FAILED
                    download_task.file_size = file_size
                    download_task.download_completed = now
                    download_task.download_duration = download_time
                    download_task.download_speed = download_speed
                    download_task.error_message = error_message

                    # 如果失败且未达到最大重试次数，标记为重试
                    if not is_success and download_task.attempt_count < download_task.max_attempts:
                        download_task.status = DownloadTaskStatus.RETRY
                        download_task.retry_after = datetime.now() + timedelta(seconds=30)  # 30秒后重试
                        download_task.worker_id = None  # 释放worker

                    await download_task.save()

                    # 更新统计
                    with self.lock:
                        if is_success:
                            self.success_count += 1
                        else:
                            self.failed_count += 1

                    # 计算进度
                    with self.lock:
                        total_tasks = await DownloadTask.filter(task_id=self.task.id).count()
                        completed_tasks = await DownloadTask.filter(
                            task_id=self.task.id, status__in=[DownloadTaskStatus.COMPLETED, DownloadTaskStatus.FAILED]
                        ).count()
                        progress_percent = completed_tasks / total_tasks * 100 if total_tasks > 0 else 0

                    # 格式化大小、时间和速度
                    size_str = self._format_size(file_size) if file_size else "N/A"
                    time_str = f"{download_time:.2f}秒" if download_time else "N/A"
                    speed_str = f"{self._format_size(download_speed)}/s" if download_speed else "N/A"

                    # 记录结果到任务日志
                    await self.log_to_db(
                        message=f"线程 {thread_name} | 文件处理完成",
                        level="ERROR" if error_message else "INFO",
                        file_path=download_task.source_path,
                        target_path=target_path,
                        file_type=download_task.file_type,
                        file_size=file_size,
                        download_time=download_time,
                        download_speed=download_speed,
                        is_success=is_success,
                        error_message=error_message,
                        url=url,
                        progress=progress_percent,
                    )

                    # 记录结果
                    self.results.append(
                        {
                            "file_path": download_task.source_path,
                            "target_path": target_path,
                            "file_type": download_task.file_type,
                            "file_size": file_size,
                            "download_time": download_time,
                            "download_speed": download_speed,
                            "is_success": is_success,
                            "error_message": error_message,
                        }
                    )

                except Exception as e:
                    # 记录单文件处理异常
                    error_message = f"处理文件时发生异常: {type(e).__name__} - {str(e)}"

                    # 更新下载任务状态
                    download_task.status = DownloadTaskStatus.FAILED
                    download_task.error_message = error_message
                    download_task.download_completed = datetime.now()

                    # 如果未达到最大重试次数，标记为重试
                    if download_task.attempt_count < download_task.max_attempts:
                        download_task.status = DownloadTaskStatus.RETRY
                        download_task.retry_after = datetime.now() + timedelta(seconds=30)  # 30秒后重试
                        download_task.worker_id = None  # 释放worker

                    await download_task.save()

                    # 更新统计
                    with self.lock:
                        self.failed_count += 1

                    # 计算进度
                    with self.lock:
                        total_tasks = await DownloadTask.filter(task_id=self.task.id).count()
                        completed_tasks = await DownloadTask.filter(
                            task_id=self.task.id, status__in=[DownloadTaskStatus.COMPLETED, DownloadTaskStatus.FAILED]
                        ).count()
                        progress_percent = completed_tasks / total_tasks * 100 if total_tasks > 0 else 0

                    # 记录异常到日志
                    await self.log_to_db(
                        message=f"线程 {thread_name} | 文件处理异常",
                        level="ERROR",
                        file_path=download_task.source_path,
                        file_type=download_task.file_type,
                        is_success=False,
                        error_message=error_message,
                        progress=progress_percent,
                    )

                    # 记录结果
                    self.results.append(
                        {
                            "file_path": download_task.source_path,
                            "target_path": "",
                            "file_type": download_task.file_type,
                            "file_size": 0,
                            "download_time": 0,
                            "download_speed": 0,
                            "is_success": False,
                            "error_message": error_message,
                        }
                    )

            except Exception as e:
                # 记录全局异常
                error_message = f"下载线程异常: {type(e).__name__} - {str(e)}"

                # 记录异常，但不输出到控制台
                with self.lock:
                    self.failed_count += 1

                # 记录异常到日志
                await self.log_to_db(
                    message=f"线程 {thread_name} | 全局异常",
                    level="ERROR",
                    is_success=False,
                    error_message=error_message,
                )

                # 如果正在处理任务，释放任务
                try:
                    if "download_task" in locals() and download_task:
                        download_task.status = DownloadTaskStatus.PENDING
                        download_task.worker_id = None
                        await download_task.save()
                except Exception:
                    pass

                # 暂停一下，避免出错后立即重试
                await asyncio.sleep(1)

    async def _download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile], Dict[str, Any]]:
        """
        下载单个资源文件，内部使用

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录, 下载详情)
        """
        # 收集下载详情，用于生成综合日志
        current_time = time.time()
        download_details = {
            "start_time": current_time,
            "end_time": None,
            "duration": 0,
            "speed": 0,
            "file_size": 0,
            "url": "",
            "is_success": False,
            "error_message": None,
            "file_path": "",
            "target_path": "",
            "file_type": "",
        }

        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote
            import ssl

            # 构建媒体URL
            file_path = file_info["path"]
            file_type = file_info.get("file_type", FileType.OTHER)

            # 记录基本信息
            download_details["file_path"] = file_path
            download_details["file_type"] = file_type

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"
            download_details["url"] = url

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)
            download_details["target_path"] = resource_path

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    # 先尝试HEAD请求获取文件信息
                    expected_content_length = None
                    try:
                        async with session.head(url) as head_response:
                            if head_response.status == 200:
                                content_length = head_response.headers.get("Content-Length")
                                if content_length:
                                    expected_content_length = int(content_length)
                    except Exception:
                        # 忽略HEAD请求失败，继续尝试GET请求
                        pass

                    # 开始GET请求下载文件
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )

                            # 添加10秒等待时间（即使下载失败）
                            self.logger.info(f"文件 {file_path} 下载失败，开始等待10秒...")
                            time.sleep(10)  # 使用同步time.sleep替代异步等待
                            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                            return False, error_msg, strm_file, download_details

                        # 获取Content-Length
                        content_length = response.headers.get("Content-Length")
                        if content_length:
                            expected_content_length = int(content_length)

                        # 读取响应内容
                        content = await response.read()
                        file_size = len(content)
                        download_details["file_size"] = file_size

                        # 验证文件有效性
                        if file_size == 0:
                            error_msg = f"下载的文件大小为0，文件无效"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )

                            # 添加10秒等待时间（即使文件无效）
                            self.logger.info(f"文件 {file_path} 下载失败（大小为0），开始等待10秒...")
                            time.sleep(10)  # 使用同步time.sleep替代异步等待
                            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                            return False, error_msg, strm_file, download_details

                        # 如果之前HEAD请求获取到了预期的文件大小，进行比较验证
                        if expected_content_length is not None and expected_content_length > 0:
                            # 允许有10%的误差
                            size_difference = abs(expected_content_length - file_size)
                            if size_difference > expected_content_length * 0.1:  # 超过10%的差异
                                download_details["warning"] = (
                                    f"文件大小与预期不符: 预期 {expected_content_length}，实际 {file_size}"
                                )

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 验证写入的文件
                        try:
                            # 检查文件是否存在且大小正确
                            if not os.path.exists(resource_path):
                                error_msg = f"文件写入失败: 文件不存在"
                                download_details["is_success"] = False
                                download_details["error_message"] = error_msg

                                # 添加10秒等待时间（文件写入失败）
                                self.logger.info(f"文件 {file_path} 写入失败，开始等待10秒...")
                                time.sleep(10)  # 使用同步time.sleep替代异步等待
                                self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                                return False, error_msg, None, download_details

                            actual_file_size = os.path.getsize(resource_path)
                            if actual_file_size != file_size:
                                error_msg = f"文件写入不完整: 预期 {file_size}，实际 {actual_file_size}"
                                download_details["is_success"] = False
                                download_details["error_message"] = error_msg

                                # 添加10秒等待时间（文件写入不完整）
                                self.logger.info(f"文件 {file_path} 写入不完整，开始等待10秒...")
                                time.sleep(10)  # 使用同步time.sleep替代异步等待
                                self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                                return False, error_msg, None, download_details
                        except Exception as e:
                            error_msg = f"验证文件失败: {str(e)}"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 添加10秒等待时间（验证文件失败）
                            self.logger.info(f"文件 {file_path} 验证失败，开始等待10秒...")
                            time.sleep(10)  # 使用同步time.sleep替代异步等待
                            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                            return False, error_msg, None, download_details

                        # 计算下载耗时和速度
                        end_time = time.time()
                        duration = end_time - download_details["start_time"]
                        speed = file_size / duration if duration > 0 else 0

                        # 更新下载详情
                        download_details["end_time"] = end_time
                        download_details["duration"] = duration
                        download_details["speed"] = speed
                        download_details["is_success"] = True

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=file_size,
                                is_success=True,
                            )

                        # 添加10秒等待时间（下载成功）
                        self.logger.info(f"文件 {file_path} 下载成功，开始等待10秒...")
                        time.sleep(10)  # 使用同步time.sleep替代异步等待
                        self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                        return True, None, strm_file, download_details

                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    download_details["is_success"] = False
                    download_details["error_message"] = error_msg

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )

                    # 添加10秒等待时间（客户端错误）
                    self.logger.info(f"文件 {file_path} 客户端错误，开始等待10秒...")
                    time.sleep(10)  # 使用同步time.sleep替代异步等待
                    self.logger.info(f"文件 {file_path} 等待结束，继续处理")

                    return False, error_msg, strm_file, download_details

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            download_details["is_success"] = False
            download_details["error_message"] = error_msg

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",
                    file_type=file_type,
                    is_success=False,
                    error_message=error_msg,
                )

            # 添加10秒等待时间（异常）
            self.logger.info(f"文件 {file_path} 发生异常，开始等待10秒...")
            time.sleep(10)  # 使用同步time.sleep替代异步等待
            self.logger.info(f"文件 {file_path} 等待结束，继续处理")

            return False, error_msg, strm_file, download_details

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小为人类可读形式"""
        import math

        if size_bytes == 0:
            return "0 B"

        size_name = ("B", "KB", "MB", "GB", "TB")
        i = int(math.log(size_bytes, 1024))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_name[i]}"

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path

    async def start_download(self):
        """
        启动下载线程并等待所有下载完成

        Returns:
            下载结果统计
        """
        # 输出队列状态
        queue_size = await DownloadTask.filter(task_id=self.task.id, status=DownloadTaskStatus.PENDING).count()
        await self._thread_safe_save_log_content(
            f"开始下载处理，队列中文件数: {queue_size}", level="INFO", is_summary=True
        )

        if queue_size == 0:
            await self._thread_safe_save_log_content("队列为空，没有文件需要处理", level="WARNING", is_summary=True)
            return {
                "success_count": 0,
                "failed_count": 0,
                "total_files": 0,
                "success_rate": 0,
                "results": [],
            }

        # 创建并启动下载线程
        download_tasks = []
        for i in range(self.threads):
            # 创建异步任务
            task = asyncio.create_task(self.download_worker())
            download_tasks.append(task)

        # 持续监控直到所有任务处理完毕
        while True:
            # 检查是否有未完成的任务
            pending_count = await DownloadTask.filter(
                task_id=self.task.id,
                status__in=[DownloadTaskStatus.PENDING, DownloadTaskStatus.DOWNLOADING, DownloadTaskStatus.RETRY],
            ).count()

            if pending_count == 0:
                # 所有任务已处理完毕
                break

            # 检查是否应该停止
            if self.stop_event.is_set():
                break

            # 等待一段时间后再检查
            await asyncio.sleep(1)

        # 设置停止事件，通知所有线程停止
        self.stop_event.set()

        # 等待所有线程结束
        await asyncio.gather(*download_tasks, return_exceptions=True)

        # 计算总体统计信息
        success_count = await DownloadTask.filter(task_id=self.task.id, status=DownloadTaskStatus.COMPLETED).count()

        failed_count = await DownloadTask.filter(task_id=self.task.id, status__in=[DownloadTaskStatus.FAILED]).count()

        total_files = success_count + failed_count
        success_rate = (success_count / total_files * 100) if total_files > 0 else 0

        # 更新主任务的统计信息
        if self.task:
            # 获取当前的统计数据
            current_success_files = self.task.success_files or 0
            current_failed_files = self.task.failed_files or 0
            current_processed_files = self.task.processed_files or 0

            # 更新任务统计信息，加上下载任务的统计
            self.task.success_files = current_success_files + success_count
            self.task.failed_files = current_failed_files + failed_count
            self.task.processed_files = current_processed_files + total_files
            await self.task.save()

        # 如果全部失败，添加一条警告日志
        if total_files > 0 and success_count == 0:
            await self._thread_safe_save_log_content(
                f"警告: 所有文件({total_files}个)下载均失败，可能是服务器配置错误或网络问题",
                level="WARNING",
                is_summary=True,
            )

        # 生成统计信息
        stats = {
            "success_count": success_count,
            "failed_count": failed_count,
            "total_files": total_files,
            "success_rate": success_rate,
            "results": self.results,
        }

        await self.create_summary_log(stats)
        return stats

    def calculate_average_speed(self) -> float:
        """计算平均下载速度（字节/秒）"""
        try:
            # 计算总下载字节数
            total_bytes = 0
            successful_files = 0

            for result in self.results:
                if result.get("is_success", False) and result.get("file_size", 0) > 0:
                    total_bytes += result.get("file_size", 0)
                    successful_files += 1

            # 计算平均速度
            if successful_files > 0:
                # 至少需要一个成功的文件来计算平均速度
                return total_bytes / successful_files  # 字节/文件
            return 0
        except Exception as e:
            self.logger.error(f"计算平均下载速度失败: {str(e)}")
            return 0

    async def create_summary_log(self, stats: Dict[str, Any]):
        """
        创建下载汇总日志

        Args:
            stats: 下载结果统计信息
        """
        # 计算总体统计信息
        total_files = stats["total_files"]
        success_count = stats["success_count"]
        failed_count = stats["failed_count"]
        success_rate = stats["success_rate"]
        duration = stats.get("duration", 0)

        # 创建详细的汇总信息
        summary = "\n"

        # 基本统计
        summary += f"[汇总] 总文件数: {total_files}, 成功: {success_count}, 失败: {failed_count}, 成功率: {success_rate:.1f}%, 总时间: {duration:.2f}秒\n"

        # 如果有文件被处理
        if total_files > 0:
            # 计算平均下载速度
            avg_speed = self.calculate_average_speed()
            if avg_speed > 0:
                avg_speed_str = self._format_size(int(avg_speed))
                summary += f"[平均下载速度] {avg_speed_str}/s\n"

            # 如果有失败的文件，添加失败原因统计
            if failed_count > 0:
                # 统计常见的错误原因
                error_counts = {}
                for result in self.results:
                    if not result.get("is_success", True) and result.get("error_message"):
                        error_msg = result.get("error_message")
                        # 简化错误消息，只取前30个字符作为键
                        error_key = error_msg[:30] + ("..." if len(error_msg) > 30 else "")
                        error_counts[error_key] = error_counts.get(error_key, 0) + 1

                # 显示失败原因统计
                if error_counts:
                    summary += "[失败原因统计]\n"
                    for error, count in sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5]:
                        summary += f"  - {error}: {count}个文件\n"

                    # 如果有更多失败原因
                    if len(error_counts) > 5:
                        summary += f"  - 还有{len(error_counts) - 5}种其他原因\n"

            # 按类型统计文件
            file_types = {}
            success_types = {}
            for result in self.results:
                file_type = result.get("file_type", "other")
                file_types[file_type] = file_types.get(file_type, 0) + 1
                if result.get("is_success", False):
                    success_types[file_type] = success_types.get(file_type, 0) + 1

            # 显示文件类型统计
            if file_types:
                summary += "[文件类型统计]\n"
                for ftype, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
                    success_count = success_types.get(ftype, 0)
                    success_rate_type = (success_count / count) * 100 if count > 0 else 0
                    summary += f"  - {ftype}: 总数 {count}, 成功 {success_count}, 成功率 {success_rate_type:.1f}%\n"

            # 性能统计
            if duration > 0 and total_files > 0:
                files_per_second = total_files / duration
                summary += f"[性能统计] 每秒处理文件数: {files_per_second:.2f}\n"

        # 保存到任务记录中
        if self.task:
            self.task.log_content = self.task.log_content or ""
            self.task.log_content += summary
            await self._thread_safe_save_log(self.task, {"log_content": self.task.log_content})

    async def log_initial_summary(self):
        """
        记录初始汇总信息
        """
        queue_size = await DownloadTask.filter(task_id=self.task.id, status=DownloadTaskStatus.PENDING).count()

        # 基本信息
        summary = f"[开始下载] 队列中文件数: {queue_size}\n"

        # 服务器信息
        summary += f"[服务器信息] 名称: {self.server.name}, URL: {self.server.base_url}\n"

        # 输出目录信息
        summary += f"[输出目录] {self.output_dir}\n"

        # 线程数信息
        summary += f"[配置信息] 下载线程数: {self.threads}\n"

        # 如果队列非空，记录前3个文件样本
        if queue_size > 0:
            sample_size = min(3, queue_size)
            summary += f"[文件样本] 显示前{sample_size}个文件:\n"

            # 获取前3个任务进行展示
            sample_tasks = await DownloadTask.filter(task_id=self.task.id, status=DownloadTaskStatus.PENDING).limit(
                sample_size
            )

            for i, task in enumerate(sample_tasks, 1):
                file_path = task.source_path
                file_type = task.file_type
                summary += f"  {i}. 路径: {file_path}, 类型: {file_type}\n"

            # 如果队列中有更多文件
            if queue_size > sample_size:
                summary += f"  ... 还有{queue_size - sample_size}个文件未显示\n"

        # 保存到任务记录中
        if self.task:
            self.task.log_content = self.task.log_content or ""
            self.task.log_content += summary
            await self._thread_safe_save_log(self.task, {"log_content": self.task.log_content})

    async def _thread_safe_save_log_content(self, message: str, level: str = "INFO", is_summary: bool = False):
        """
        异步更新任务日志内容
        (注：函数名保留为_thread_safe_save_log_content是为了向后兼容，但它是异步的)

        Args:
            message: 日志消息
            level: 日志级别
            is_summary: 是否为汇总日志
        """
        if not self.task:
            return

        try:
            # 在日志条目末尾添加换行符
            log_entry = message + "\n"

            # 更新任务日志内容
            current_log = self.task.log_content or ""
            self.task.log_content = current_log + log_entry

            # 只保存日志内容，不输出到控制台
            await self._thread_safe_save_log(self.task, {"log_content": self.task.log_content})
        except Exception:
            # 忽略所有异常
            pass

    async def log_to_db(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
    ):
        """
        将日志记录到任务的log_content字段

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
        """
        # 如果没有关联任务，无法记录日志
        if not self.task:
            # 不记录日志，直接返回
            return

        try:
            # 格式化日志条目
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] [{level}] {message}"

            # 添加文件路径信息
            if file_path:
                log_entry += f" | 文件: {file_path}"

            # 添加目标路径信息
            if target_path:
                log_entry += f" | 目标: {target_path}"

            # 添加错误信息
            if error_message:
                log_entry += f" | 错误: {error_message}"

            # 添加成功状态
            status_text = "成功" if is_success else "失败"
            log_entry += f" | 状态: {status_text}"

            # 添加文件大小和下载时间信息（如有）
            if file_size is not None:
                size_formatted = self._format_size(file_size)
                log_entry += f" | 大小: {size_formatted}"

            if download_time is not None:
                log_entry += f" | 耗时: {download_time:.2f}s"

            if download_speed is not None and download_speed > 0:
                speed_formatted = self._format_size(int(download_speed)) + "/s"
                log_entry += f" | 速度: {speed_formatted}"

            # 在日志条目末尾添加换行符
            log_entry += "\n"

            # 获取当前的日志内容
            current_log = self.task.log_content or ""

            # 追加新的日志条目
            self.task.log_content = current_log + log_entry

            # 保存任务
            await self.task.save(update_fields=["log_content"])

        except Exception as e:
            # 记录日志失败时输出到日志
            self.logger.error(f"记录日志到任务失败: {str(e)}")

    async def log_to_db_sync(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
        url: Optional[str] = None,
        progress: Optional[float] = None,
    ):
        """
        异步日志记录函数，用于向数据库记录日志
        (注：函数名保留为log_to_db_sync是为了向后兼容，但它是异步的)

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
            url: 下载URL
            progress: 下载进度
        """
        # 如果没有关联任务，无法记录日志
        if not self.task:
            # 不记录日志，直接返回
            return

        try:
            # 确保任务的log_content字段已初始化
            if self.task.log_content is None:
                self.task.log_content = ""

            # 构建日志条目
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] [{level}] "

            # 添加原始消息
            log_entry += message

            # 显示URL信息(如果提供)
            if url:
                log_entry += f" | URL: {url}"

            # 添加文件路径信息(如果提供)
            if file_path:
                log_entry += f" | 文件: {file_path}"

            # 添加目标路径信息(如果提供)
            if target_path:
                log_entry += f" | 目标: {target_path}"

            # 添加错误信息(如果提供)
            if error_message:
                log_entry += f" | 错误: {error_message}"

            # 添加成功状态
            status_text = "成功" if is_success else "失败"
            log_entry += f" | 状态: {status_text}"

            # 添加文件大小信息(如果提供)
            if file_size is not None and file_size > 0:
                size_formatted = self._format_size(file_size)
                log_entry += f" | 大小: {size_formatted}"

            # 添加下载时间信息(如果提供)
            if download_time is not None and download_time > 0:
                log_entry += f" | 耗时: {download_time:.2f}秒"

            # 添加下载速度信息(如果提供)
            if download_speed is not None and download_speed > 0:
                speed_formatted = self._format_size(int(download_speed)) + "/s"
                log_entry += f" | 速度: {speed_formatted}"

            # 添加进度信息(如果提供)
            if progress is not None:
                log_entry += f" | 进度: {progress:.1f}%"

            # 在日志条目末尾添加换行符
            log_entry += "\n"

            # 直接更新任务的日志内容
            await self._thread_safe_save_log_content(log_entry.strip(), level=level, is_summary=False)

            # 更新任务状态，但不输出到控制台
            if level == "ERROR" and not is_summary_log(message):
                # 更新任务状态，不打印日志
                await self._thread_safe_save_log(
                    self.task,
                    {
                        "error_files": F.F("error_files") + 1,
                    },
                )
            elif level == "INFO" and "线程" in message and "保存" in message:
                # 更新任务状态，不打印日志
                if is_success:
                    await self._thread_safe_save_log(
                        self.task,
                        {
                            "success_files": F.F("success_files") + 1,
                        },
                    )
                else:
                    await self._thread_safe_save_log(
                        self.task,
                        {
                            "error_files": F.F("error_files") + 1,
                        },
                    )
        except Exception as e:
            # 记录异常，但不输出到控制台
            pass

    async def _thread_safe_save_log(self, task, update_fields):
        """
        异步保存任务记录
        (注：函数名保留为_thread_safe_save_log是为了向后兼容，但它是异步的)

        Args:
            task: 任务对象
            update_fields: 需要更新的字段
        """
        try:
            # 更新任务
            for field, value in update_fields.items():
                setattr(task, field, value)
            await task.save()
        except Exception as e:
            self.logger.error(f"保存任务记录失败: {str(e)}")

    def get_download_url(self, path: str) -> str:
        """
        获取文件的下载URL

        Args:
            path: 文件路径

        Returns:
            完整的下载URL
        """
        # 应用路径替换逻辑
        processed_path = self.replace_base_path(path)

        # 使用urllib.parse.quote替换特殊字符
        from urllib.parse import quote

        return f"{self.server.base_url}{quote(processed_path)}"

    def get_output_path(self, path: str) -> str:
        """
        获取文件的保存路径

        Args:
            path: 文件原始路径

        Returns:
            本地保存路径
        """
        # 获取文件名和目录
        file_name = os.path.basename(path)
        file_dir = os.path.dirname(path)

        # 构建输出目录路径
        output_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))

        # 完整的保存路径
        return os.path.join(output_dir, file_name)

    def download_file(self, url: str, target_path: str) -> Dict[str, Any]:
        """
        下载单个文件

        Args:
            url: 下载URL
            target_path: 目标保存路径

        Returns:
            下载结果信息
        """
        import httpx
        import time

        # 初始化结果
        result = {
            "is_success": False,
            "error_message": None,
            "file_size": 0,
            "download_time": 0,
            "download_speed": 0,
        }

        try:
            # 记录开始时间
            start_time = time.time()

            # 设置超时和头信息
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            }

            # 设置客户端选项
            client_kwargs = {
                "timeout": 60.0,  # 60秒超时
                "verify": False,  # 禁用SSL验证
            }

            # 配置认证
            if self.server.auth_type == "basic" and self.server.username and self.server.password:
                client_kwargs["auth"] = (self.server.username, self.server.password)

            # 记录开始下载日志
            self.logger.info(f"开始下载文件: {url} -> {target_path}")

            # 确保目录存在
            os.makedirs(os.path.dirname(target_path), exist_ok=True)

            # 使用httpx同步API下载文件
            with httpx.Client(**client_kwargs) as client:
                with client.stream("GET", url, headers=headers) as response:
                    # 检查响应状态
                    if response.status_code != 200:
                        error_msg = f"HTTP错误: {response.status_code} {response.reason_phrase}"
                        result["error_message"] = error_msg
                        self.logger.error(f"下载失败 - {error_msg}")
                        return result

                    # 获取文件大小
                    content_length = response.headers.get("Content-Length")
                    file_size = int(content_length) if content_length else 0
                    result["file_size"] = file_size

                    # 将响应内容写入文件
                    downloaded_size = 0
                    with open(target_path, "wb") as f:
                        for chunk in response.iter_bytes(chunk_size=8192):
                            f.write(chunk)
                            downloaded_size += len(chunk)

            # 计算下载时间和速度
            end_time = time.time()
            download_time = end_time - start_time
            result["download_time"] = download_time

            # 避免除零
            if download_time > 0:
                download_speed = downloaded_size / download_time
                result["download_speed"] = download_speed

            # 核对下载文件大小
            if file_size > 0 and downloaded_size != file_size:
                error_msg = f"文件大小不匹配: 预期 {file_size} 字节，实际 {downloaded_size} 字节"
                result["error_message"] = error_msg
                self.logger.error(f"下载失败 - {error_msg}")
                return result

            # 下载成功
            result["is_success"] = True
            self.logger.info(f"下载成功: {target_path} ({self._format_size(downloaded_size)})")
            return result

        except httpx.TimeoutException as e:
            error_msg = f"连接超时: {str(e)}"
            result["error_message"] = error_msg
            self.logger.error(f"下载失败 - {error_msg}")
            return result

        except httpx.ConnectError as e:
            error_msg = f"连接错误: {str(e)}"
            result["error_message"] = error_msg
            self.logger.error(f"下载失败 - {error_msg}")
            return result

        except httpx.RequestError as e:
            error_msg = f"请求错误: {str(e)}"
            result["error_message"] = error_msg
            self.logger.error(f"下载失败 - {error_msg}")
            return result

        except Exception as e:
            error_msg = f"下载异常: {type(e).__name__} - {str(e)}"
            result["error_message"] = error_msg
            self.logger.error(f"下载失败 - {error_msg}")
            return result


class StrmProcessor:
    """STRM处理器，用于生成STRM文件"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        verbose_console_logging: bool = False,  # 添加控制台详细日志参数
        settings_dict: Optional[Dict[str, Any]] = None,  # 添加settings_dict参数
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            verbose_console_logging: 是否输出详细日志到控制台，默认为False
            settings_dict: 系统设置字典，用于获取文件类型配置
        """
        self.server = server
        self.output_dir = output_dir
        self.base_url = server.base_url
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.verbose_console_logging = verbose_console_logging  # 保存详细日志设置

        # 配置日志记录器
        self.logger = logging.getLogger("strm_processor")

        # 设置控制台处理器的日志级别
        for handler in self.logger.handlers:
            if isinstance(handler, logging.StreamHandler):
                if not verbose_console_logging:
                    handler.setLevel(logging.ERROR)  # 只输出错误日志
                else:
                    handler.setLevel(logging.INFO)  # 输出所有日志

        # 如果提供了settings_dict，使用TreeParser获取文件类型
        self.tree_parser = None
        if settings_dict:
            try:
                self.tree_parser = TreeParser(settings_dict)
            except Exception as e:
                self.logger.error(f"初始化TreeParser失败: {str(e)}")
                self.tree_parser = None

    def get_file_type(self, file_path: str) -> str:
        """
        根据文件路径获取文件类型

        Args:
            file_path: 文件路径

        Returns:
            文件类型枚举值字符串
        """
        # 优先使用tree_parser获取文件类型
        if self.tree_parser:
            return self.tree_parser.get_file_type(file_path)

        # 默认实现逻辑
        if not file_path:
            return FileType.OTHER

        # 获取文件扩展名
        _, extension = os.path.splitext(file_path)
        extension = extension.lower()

        # 去掉扩展名前面的点
        if extension.startswith("."):
            extension = extension[1:]

        # 视频文件类型
        video_extensions = ["mp4", "mkv", "avi", "mov", "wmv", "flv", "mpg", "mpeg", "m4v", "ts", "m2ts"]
        if extension in video_extensions:
            return FileType.VIDEO

        # 音频文件类型
        audio_extensions = ["mp3", "flac", "wav", "aac", "ogg", "m4a", "wma", "ape"]
        if extension in audio_extensions:
            return FileType.AUDIO

        # 图片文件类型
        image_extensions = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "svg"]
        if extension in image_extensions:
            return FileType.IMAGE

        # 字幕文件类型
        subtitle_extensions = ["srt", "ass", "ssa", "vtt", "sub", "idx"]
        if extension in subtitle_extensions:
            return FileType.SUBTITLE

        # 元数据文件类型
        metadata_extensions = ["nfo", "xml", "json", "txt"]
        if extension in metadata_extensions:
            return FileType.METADATA

        # 默认为其他类型
        return FileType.OTHER

    def generate_strm(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建STRM文件路径
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 分割路径和扩展名
            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            # 完整的STRM文件路径
            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            # 确保目录存在
            os.makedirs(strm_dir, exist_ok=True)

            # 写入STRM文件
            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            # 创建STRM文件记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )
                # 文件记录需要在外部保存

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",  # 失败没有目标路径
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )
                # 文件记录需要在外部保存

            return False, error_msg, strm_file

    async def download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载单个资源文件，与generate_strm类似，但专用于资源文件

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote

            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 获取文件类型
            file_type = file_info.get("file_type", FileType.OTHER)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 禁用SSL验证，使用兼容的方式
            await self.log_to_db(f"SSL验证已禁用", file_path=file_path)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            self.logger.error(error_msg)

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        # 读取响应内容
                        content = await response.read()

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=len(content),
                                is_success=True,
                            )

                        return True, None, strm_file
                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    self.logger.error(error_msg)

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path if "file_path" in locals() else "",
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    async def process_files(
        self,
        files: List[Dict[str, Any]],
        task: Optional[StrmTask] = None,
        download_resources: bool = False,
        download_server: Optional[MediaServer] = None,
        verbose_console_logging: bool = False,  # 添加控制台详细日志参数
    ) -> Dict[str, Any]:
        """
        处理文件列表，生成STRM文件

        Args:
            files: 文件列表
            task: 关联的任务
            download_resources: 是否下载资源文件
            download_server: 下载服务器

        Returns:
            处理结果
        """
        log.info(f"开始处理文件列表: task_id={task.id if task else None}, 文件数量={len(files)}")

        # 统计信息
        total_files = 0
        video_files = 0
        processed_files = 0
        success_files = 0
        failed_files = 0
        start_time = time.time()

        # 统计视频文件数量
        for file_info in files:
            if file_info.get("is_dir", False):
                continue
            # 获取文件类型
            file_type = self.get_file_type(file_info.get("path", ""))
            file_info["file_type"] = file_type
            if file_type == FileType.VIDEO:
                video_files += 1
                total_files += 1

        log.info(f"文件统计: 总文件数={total_files}, 视频文件数={video_files}")

        if task:
            # 更新任务总文件数（如果未设置）
            if task.total_files == 0:
                task.total_files = video_files
                log.info(f"更新任务总文件数: {video_files}")
                await task.save(update_fields=["total_files"])

        # 处理每个文件
        for file_info in files:
            try:
                if file_info.get("is_dir", False):
                    log.debug(f"跳过目录: {file_info.get('path', '')}")
                    continue

                processed_files += 1
                file_path = file_info.get("path", "")
                file_type = file_info.get("file_type", self.get_file_type(file_path))

                if file_type != FileType.VIDEO:
                    log.debug(f"跳过非视频文件: {file_path}, 类型: {file_type}")
                    continue

                log.info(f"处理文件 [{processed_files}/{video_files}]: {file_path}")

                # 生成STRM文件或下载资源文件
                success = False
                error_message = None
                strm_file_obj = None

                try:
                    # 生成STRM文件
                    success, error_message, strm_file_obj = self.generate_strm(file_info, task)
                    if success:
                        success_files += 1
                        log.info(f"成功生成STRM文件: {file_path}")
                    else:
                        failed_files += 1
                        log.error(f"生成STRM文件失败: {file_path}, 错误: {error_message}")
                except Exception as e:
                    failed_files += 1
                    error_detail = traceback.format_exc()
                    log.error(f"处理文件时发生错误: {file_path}, 错误: {str(e)}\n{error_detail}")

                # 更新任务进度
                if task:
                    try:
                        task.processed_files = processed_files
                        task.success_files = success_files
                        task.failed_files = failed_files
                        await task.save(update_fields=["processed_files", "success_files", "failed_files"])
                    except Exception as e:
                        log.error(f"更新任务进度时发生错误: {str(e)}")

            except Exception as e:
                error_detail = traceback.format_exc()
                log.error(f"处理文件项时发生未捕获的异常: {str(e)}\n{error_detail}")

        # 处理完成后更新任务状态
        end_time = time.time()
        duration = end_time - start_time

        log.info(
            f"文件处理完成, 耗时: {duration:.2f}秒, 总文件: {video_files}, 成功: {success_files}, 失败: {failed_files}"
        )

        if task:
            try:
                task.end_time = datetime.now()
                task.status = TaskStatus.COMPLETED
                await task.save(update_fields=["end_time", "status"])
                log.info(f"已更新任务状态为已完成: {task.id}")
            except Exception as e:
                log.error(f"更新任务完成状态时发生错误: {str(e)}")

        return {
            "total": video_files,
            "success": success_files,
            "failed": failed_files,
            "processed": processed_files,
            "time": duration,
        }

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task: Optional[StrmTask] = None,
    download_resources: bool = False,
    download_server_id: Optional[int] = None,
    verbose_console_logging: bool = False,  # 添加控制台详细日志参数
) -> Dict[str, Any]:
    """
    处理整个目录树的文件

    Args:
        server_id: 服务器ID
        files: 文件列表
        output_dir: 输出目录
        task: 关联的任务
        download_resources: 是否下载资源文件
        download_server_id: 下载服务器ID
        verbose_console_logging: 是否输出详细下载日志到控制台，默认为False

    Returns:
        处理结果
    """
    # 添加详细日志
    log.info(
        f"开始处理目录树，参数: server_id={server_id}, files数量={len(files)}, output_dir={output_dir}, task_id={task.id if task else None}, download_resources={download_resources}"
    )

    try:
        # 获取服务器配置
        server = await MediaServer.get_or_none(id=server_id)
        if not server:
            log.error(f"找不到ID为{server_id}的服务器")
            return {"result": False, "message": f"找不到ID为{server_id}的服务器"}

        log.info(f"成功获取服务器配置: {server.name}")

        # 获取当前系统设置
        log.info("正在获取系统设置...")
        settings = await SystemSettings.all().first()
        if not settings:
            log.info("未找到系统设置，创建默认设置")
            settings = await SystemSettings.create()

        # 将SystemSettings对象转换为字典，以便传递给TreeParser
        settings_dict = {
            "settings_version": settings.settings_version,
            "video_file_types": settings.video_file_types,
            "audio_file_types": settings.audio_file_types,
            "image_file_types": settings.image_file_types,
            "subtitle_file_types": settings.subtitle_file_types,
            "metadata_file_types": settings.metadata_file_types,
            "enable_path_replacement": settings.enable_path_replacement,
            "replacement_path": settings.replacement_path,
            "download_threads": settings.download_threads,
        }

        log.info(
            f"系统设置: download_threads={settings.download_threads}, enable_path_replacement={settings.enable_path_replacement}"
        )

        # 设置STRM处理器
        log.info("创建STRM处理器...")
        processor = StrmProcessor(
            server=server,
            output_dir=output_dir,
            enable_path_replacement=settings.enable_path_replacement,
            replacement_path=settings.replacement_path,
            verbose_console_logging=verbose_console_logging,  # 设置详细日志级别
            settings_dict=settings_dict,  # 传递settings_dict
        )

        # 下载资源文件处理
        download_result = {}
        if download_resources and task:
            log.info("准备下载资源文件...")
            download_server = None
            if download_server_id:
                log.info(f"尝试获取指定的下载服务器ID={download_server_id}")
                download_server = await MediaServer.get_or_none(id=download_server_id)
                if download_server:
                    log.info(f"成功获取下载服务器: {download_server.name}")
                else:
                    log.warning(f"未找到ID为{download_server_id}的下载服务器")
            else:
                # 如果未指定下载服务器，使用系统默认下载服务器
                log.info("未指定下载服务器，尝试使用系统默认下载服务器")
                if settings.default_download_server:
                    download_server = await settings.default_download_server
                    log.info(f"使用系统默认下载服务器: {download_server.server_name if download_server else 'None'}")

            if not download_server:
                log.error("未指定下载服务器且未设置默认下载服务器")
                return {"result": False, "message": "未指定下载服务器且未设置默认下载服务器"}

            # 创建资源下载器
            log.info(f"创建资源下载器，线程数: {settings.download_threads}")
            downloader = ResourceDownloader(
                server=download_server,
                output_dir=output_dir,
                threads=settings.download_threads,
                enable_path_replacement=settings.enable_path_replacement,
                replacement_path=settings.replacement_path,
                task=task,
                verbose_console_logging=False,  # 禁用详细日志
            )

            # 添加所有需要下载的文件到队列
            log.info(f"开始添加文件到下载队列，文件总数: {len(files)}")
            files_added = 0
            for file_info in files:
                if file_info.get("is_dir", False):
                    continue  # 跳过目录

                # 获取文件类型 - 使用settings_dict传递给TreeParser
                file_type_parser = TreeParser(settings_dict)
                file_type = (
                    file_type_parser.get_file_type(file_info.get("path", ""))
                    if hasattr(file_type_parser, "get_file_type")
                    else processor.get_file_type(file_info.get("path", ""))
                )
                file_info["file_type"] = file_type

                # 只下载特定类型的资源文件
                if file_type in [FileType.VIDEO, FileType.AUDIO, FileType.IMAGE, FileType.SUBTITLE, FileType.METADATA]:
                    await downloader.add_file(file_info, task)
                    files_added += 1
                    if files_added % 100 == 0:
                        log.info(f"已添加 {files_added} 个文件到下载队列")

            log.info(f"总计添加了 {files_added} 个文件到下载队列")

            # 记录初始汇总日志
            log.info("创建初始汇总日志...")
            await downloader.log_initial_summary()

            # 开始下载，这将阻塞直到所有文件处理完成
            log.info("开始下载资源文件...")
            start_time = time.time()
            download_result = await downloader.start_download()
            duration = time.time() - start_time
            log.info(f"下载完成，耗时: {duration:.2f}秒")

            # 更新任务下载耗时
            if task:
                task.download_duration = duration
                await task.save(update_fields=["download_duration"])
                log.info(f"已更新任务下载耗时: {duration:.2f}秒")

            # 创建汇总日志
            log.info("创建下载汇总日志...")
            await downloader.create_summary_log(download_result)

            # 添加下载时间到结果
            download_result["download_time"] = duration

        # 处理STRM文件生成
        log.info("开始处理STRM文件生成...")
        result = await processor.process_files(
            files=files,
            task=task,
            download_resources=download_resources,
            download_server=download_server if download_resources else None,
            verbose_console_logging=False,  # 禁用详细日志
        )
        log.info(f"STRM文件处理完成，结果: {result}")

        # 合并下载结果和处理结果
        if download_resources:
            result["download"] = download_result
            log.info("已合并下载结果和处理结果")

        return result
    except Exception as e:
        error_detail = traceback.format_exc()
        log.error(f"处理目录树时发生错误: {str(e)}\n{error_detail}")
        # 确保任务状态被正确更新为失败
        if task:
            try:
                task.status = TaskStatus.FAILED
                task.log_content = f"处理任务时发生错误: {str(e)}"
                await task.save(update_fields=["status", "log_content"])
                log.info(f"已将任务 {task.id} 状态更新为 FAILED")
            except Exception as save_error:
                log.error(f"更新任务状态时发生错误: {str(save_error)}")
        return {"result": False, "message": f"处理目录树时发生错误: {str(e)}"}


def is_summary_log(message: str) -> bool:
    """
    判断是否为汇总日志

    Args:
        message: 日志消息

    Returns:
        是否为汇总日志
    """
    # 检查汇总日志的特征
    summary_keywords = [
        "总耗时",
        "下载完成",
        "任务统计",
        "总文件数",
        "成功文件数",
        "失败文件数",
        "平均速度",
        "汇总",
        "总计",
        "summary",
        "开始多线程下载",
        "队列中文件数",
    ]

    # 如果消息中包含任何一个关键词，认为是汇总信息
    return any(keyword in message for keyword in summary_keywords)
