"""
STRM处理器，用于生成STRM文件
"""

import os
import shutil
import zipfile
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple

from urllib.parse import quote

from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings


class StrmProcessor:
    """STRM处理器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
        """
        self.server = server
        self.output_dir = output_dir
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.logger = logging.getLogger("strm_processor")

    def generate_strm(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建STRM文件路径
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 分割路径和扩展名
            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            # 完整的STRM文件路径
            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            # 确保目录存在
            os.makedirs(strm_dir, exist_ok=True)

            # 写入STRM文件
            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            # 创建STRM文件记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )
                # 文件记录需要在外部保存

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",  # 失败没有目标路径
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )
                # 文件记录需要在外部保存

            return False, error_msg, strm_file

    async def process_files(self, files: List[Dict[str, Any]], task: Optional[StrmTask] = None) -> Dict[str, Any]:
        """
        批量处理文件

        Args:
            files: 文件列表
            task: 关联的任务

        Returns:
            处理结果统计
        """
        result = {"total": len(files), "success": 0, "failed": 0, "skipped": 0, "strm_files": [], "errors": []}

        # 更新任务状态
        if task:
            task.total_files = len(files)
            task.start_time = datetime.now()
            task.status = TaskStatus.RUNNING
            await task.save()

        # 处理每个文件
        for i, file_info in enumerate(files):
            # 只处理视频文件
            if file_info["file_type"] != FileType.VIDEO:
                result["skipped"] += 1
                continue

            success, error_msg, strm_file = self.generate_strm(file_info, task)

            if success:
                result["success"] += 1
                if strm_file:
                    await strm_file.save()
                    # 序列化对象为字典，避免JSON序列化问题
                    result["strm_files"].append(
                        {
                            "id": strm_file.id,
                            "source_path": strm_file.source_path,
                            "target_path": strm_file.target_path,
                            "file_type": strm_file.file_type,
                            "is_success": strm_file.is_success,
                            "error_message": strm_file.error_message,
                        }
                    )
            else:
                result["failed"] += 1
                result["errors"].append(error_msg)
                if strm_file:
                    await strm_file.save()

            # 更新任务进度
            if task:
                task.processed_files = i + 1
                task.success_files = result["success"]
                task.failed_files = result["failed"]
                await task.save()

        # 更新任务状态
        if task:
            task.end_time = datetime.now()
            task.status = TaskStatus.COMPLETED
            await task.save()

        return result

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int, files: List[Dict[str, Any]], output_dir: str, task: Optional[StrmTask] = None
) -> Dict[str, Any]:
    """
    处理目录树，生成STRM文件

    Args:
        server_id: 媒体服务器ID
        files: 解析后的文件列表
        output_dir: 输出目录
        task: 关联的任务

    Returns:
        处理结果统计
    """
    # 获取媒体服务器
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise ValueError(f"找不到ID为 {server_id} 的媒体服务器")

    # 获取系统设置
    system_settings = await SystemSettings.all().first()
    enable_path_replacement = False
    replacement_path = "/nas"

    if system_settings:
        enable_path_replacement = system_settings.enable_path_replacement
        replacement_path = system_settings.replacement_path or "/nas"

    # 创建处理器
    processor = StrmProcessor(
        server, output_dir, enable_path_replacement=enable_path_replacement, replacement_path=replacement_path
    )

    # 处理文件
    result = await processor.process_files(files, task)

    return result
