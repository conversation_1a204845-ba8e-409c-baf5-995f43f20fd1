"""
STRM处理器，用于生成STRM文件
"""

import os
import shutil
import zipfile
import logging
import threading
import queue
import asyncio
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any, Optional, Union, Tuple

from urllib.parse import quote

from app.models.strm import MediaServer, FileType, StrmTask, StrmFile, TaskStatus, SystemSettings, DownloadLog


class ResourceDownloader:
    """资源文件多线程下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        threads: int = 3,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        task: Optional[StrmTask] = None,
    ):
        """
        初始化资源文件下载器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            threads: 下载线程数
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            task: 关联的任务，用于记录日志
        """
        self.server = server
        self.output_dir = output_dir
        self.threads = max(1, threads)  # 至少1个线程
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.task = task  # 保存任务引用

        # 配置常规日志（用于控制台输出）
        self.logger = logging.getLogger(f"resource_downloader_{task.id if task else ''}")

        # 下载队列和状态
        self.download_queue = queue.Queue()
        self.lock = threading.Lock()
        self.success_count = 0
        self.failed_count = 0
        self.results = []  # 保存下载结果
        self.stop_event = threading.Event()

        # 初始化汇总日志记录
        self.log_initial_summary()

    def add_file(self, file_info: Dict[str, Any], task: Optional[StrmTask] = None):
        """
        添加文件到下载队列

        Args:
            file_info: 文件信息
            task: 关联的任务
        """
        self.download_queue.put((file_info, task))

    def download_worker(self):
        """下载线程函数"""
        import aiohttp
        import aiofiles
        import threading

        # 创建一个新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        thread_name = threading.current_thread().name
        # 记录线程启动日志
        self.log_to_db_sync(f"下载线程 {thread_name} 启动")

        while not self.stop_event.is_set():
            try:
                # 从队列获取下载任务，不阻塞
                try:
                    file_info, task = self.download_queue.get(block=False)
                    file_path = file_info.get("path", "未知路径")
                    file_type = file_info.get("file_type", FileType.OTHER)
                    # 记录开始处理文件日志
                    self.log_to_db_sync(
                        f"线程 {thread_name} 开始处理文件: {file_path}", file_path=file_path, file_type=file_type
                    )
                except queue.Empty:
                    # 队列为空，退出线程
                    self.log_to_db_sync(f"线程 {thread_name} 队列为空，退出")
                    break

                try:
                    start_time = datetime.now()
                    # 使用现有的下载逻辑
                    success, error_msg, resource_file = loop.run_until_complete(
                        self._download_resource_file(file_info, task)
                    )
                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()

                    # 更新统计和保存文件记录
                    with self.lock:
                        if success:
                            self.success_count += 1
                            file_size = resource_file.file_size if resource_file else 0
                            # 记录成功日志
                            self.log_to_db_sync(
                                f"线程 {thread_name} 文件下载成功: {file_path}",
                                file_path=file_path,
                                target_path=resource_file.target_path if resource_file else None,
                                file_type=file_type,
                                file_size=file_size,
                                download_time=duration,
                                is_success=True,
                            )

                            if resource_file:
                                # 异步保存文件记录
                                loop.run_until_complete(resource_file.save())

                                # 添加到结果列表
                                self.results.append(
                                    {
                                        "id": resource_file.id,
                                        "source_path": resource_file.source_path,
                                        "target_path": resource_file.target_path,
                                        "file_type": resource_file.file_type,
                                        "is_success": resource_file.is_success,
                                        "error_message": resource_file.error_message,
                                    }
                                )
                        else:
                            self.failed_count += 1
                            # 记录失败日志
                            self.log_to_db_sync(
                                f"线程 {thread_name} 文件下载失败: {file_path}",
                                level="ERROR",
                                file_path=file_path,
                                file_type=file_type,
                                download_time=duration,
                                is_success=False,
                                error_message=error_msg,
                            )
                            if resource_file:
                                loop.run_until_complete(resource_file.save())

                except Exception as e:
                    error_message = f"线程 {thread_name} 下载出错: {str(e)}"
                    # 记录错误日志
                    self.log_to_db_sync(
                        error_message,
                        level="ERROR",
                        file_path=file_path if "file_path" in locals() else None,
                        file_type=file_type if "file_type" in locals() else None,
                        is_success=False,
                        error_message=str(e),
                    )
                    with self.lock:
                        self.failed_count += 1

                # 标记任务完成
                self.download_queue.task_done()

                # 记录当前进度
                total_processed = self.success_count + self.failed_count
                total_files = self.download_queue.qsize() + total_processed
                progress_percent = (total_processed / total_files * 100) if total_files > 0 else 0
                # 记录进度日志
                self.log_to_db_sync(
                    f"下载进度: {total_processed}/{total_files} ({progress_percent:.1f}%), "
                    f"成功: {self.success_count}, 失败: {self.failed_count}"
                )

            except Exception as e:
                # 记录未处理异常日志
                self.log_to_db_sync(
                    f"线程 {thread_name} 未处理异常: {str(e)}", level="ERROR", is_success=False, error_message=str(e)
                )

        # 关闭事件循环
        loop.close()
        # 记录线程结束日志
        self.log_to_db_sync(f"线程 {thread_name} 结束")

    async def _download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载单个资源文件，内部使用

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote
            import time

            # 构建媒体URL
            file_path = file_info["path"]

            # 记录开始下载
            await self.log_to_db(
                f"开始下载文件: {file_path}", file_path=file_path, file_type=file_info.get("file_type", FileType.OTHER)
            )
            start_time = time.time()

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"
            await self.log_to_db(f"下载URL: {url}", file_path=file_path)

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)
            await self.log_to_db(f"保存路径: {resource_path}", file_path=file_path)

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 获取文件类型
            file_type = file_info.get("file_type", FileType.OTHER)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)
            await self.log_to_db(f"连接超时设置: {timeout.total}秒", file_path=file_path)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    # 先尝试HEAD请求获取文件信息
                    try:
                        await self.log_to_db(f"发送HEAD请求获取文件信息", file_path=file_path)
                        async with session.head(url) as head_response:
                            if head_response.status == 200:
                                content_length = head_response.headers.get("Content-Length")
                                if content_length:
                                    size_str = self._format_size(int(content_length))
                                    await self.log_to_db(
                                        f"文件大小: {size_str}", file_path=file_path, file_size=int(content_length)
                                    )
                            else:
                                await self.log_to_db(
                                    f"HEAD请求返回状态码: {head_response.status}", level="WARNING", file_path=file_path
                                )
                    except Exception as e:
                        await self.log_to_db(f"获取文件信息失败: {str(e)}", level="WARNING", file_path=file_path)

                    # 开始GET请求下载文件
                    await self.log_to_db(f"发送GET请求下载文件", file_path=file_path)
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            await self.log_to_db(
                                error_msg, level="ERROR", file_path=file_path, is_success=False, error_message=error_msg
                            )

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        # 获取Content-Length
                        content_length = response.headers.get("Content-Length")
                        if content_length:
                            size_str = self._format_size(int(content_length))
                            await self.log_to_db(
                                f"文件大小确认: {size_str}", file_path=file_path, file_size=int(content_length)
                            )

                        # 读取响应内容
                        content = await response.read()
                        file_size = len(content)

                        # 写入文件
                        await self.log_to_db(f"写入文件内容: {resource_path}", file_path=file_path)
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 计算下载耗时和速度
                        end_time = time.time()
                        duration = end_time - start_time
                        speed = file_size / duration if duration > 0 else 0

                        size_str = self._format_size(file_size)
                        speed_str = self._format_size(speed)
                        await self.log_to_db(
                            f"下载完成: {file_path}, 大小: {size_str}, 耗时: {duration:.2f}秒, 速度: {speed_str}/秒",
                            file_path=file_path,
                            target_path=resource_path,
                            file_type=file_type,
                            file_size=file_size,
                            download_time=duration,
                            download_speed=speed,
                            is_success=True,
                        )

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=file_size,
                                is_success=True,
                            )

                        return True, None, strm_file

                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    await self.log_to_db(
                        error_msg,
                        level="ERROR",
                        file_path=file_path,
                        file_type=file_type,
                        is_success=False,
                        error_message=error_msg,
                    )

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            if hasattr(self, "log_to_db"):
                await self.log_to_db(
                    error_msg,
                    level="ERROR",
                    file_path=file_path if "file_path" in locals() else None,
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            else:
                self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path if "file_path" in locals() else "",
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小为人类可读形式"""
        import math

        if size_bytes == 0:
            return "0 B"

        size_name = ("B", "KB", "MB", "GB", "TB")
        i = int(math.log(size_bytes, 1024))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_name[i]}"

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path

    def start_download(self):
        """开始多线程下载"""
        # 创建线程池
        workers = []

        # 记录开始时间
        start_time = datetime.now()
        self.log_to_db_sync(f"=== 开始多线程下载 ===")
        self.log_to_db_sync(f"队列中文件数: {self.download_queue.qsize()}")

        for i in range(self.threads):
            thread = threading.Thread(target=self.download_worker, name=f"DownloadWorker-{i + 1}")
            thread.daemon = True
            thread.start()
            workers.append(thread)

        # 等待所有任务完成
        self.download_queue.join()

        # 发送停止信号
        self.stop_event.set()

        # 等待所有线程结束
        for thread in workers:
            thread.join(timeout=1.0)

        # 计算总时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 记录下载总结
        self.log_to_db_sync(f"=== 下载任务完成 ===", download_time=duration)
        self.log_to_db_sync(f"总时间: {duration:.2f}秒", download_time=duration)
        self.log_to_db_sync(f"成功: {self.success_count} 个文件", is_success=True)
        self.log_to_db_sync(f"失败: {self.failed_count} 个文件", is_success=False if self.failed_count > 0 else True)

        if self.success_count > 0 and duration > 0:
            avg_time = duration / self.success_count
            avg_speed = self.calculate_average_speed()
            if avg_speed > 0:
                avg_speed_str = self._format_size(avg_speed)
                self.log_to_db_sync(
                    f"平均下载时间: {avg_time:.2f}秒/文件, 平均速度: {avg_speed_str}/秒",
                    download_time=avg_time,
                    download_speed=avg_speed,
                )
            else:
                self.log_to_db_sync(f"平均下载时间: {avg_time:.2f}秒/文件", download_time=avg_time)

        self.log_to_db_sync(f"====================")

        # 创建总结日志
        self.create_summary_log(duration)

        # 返回下载统计
        return {
            "success": self.success_count,
            "failed": self.failed_count,
            "results": self.results,
            "duration": duration,
        }

    def calculate_average_speed(self) -> float:
        """计算平均下载速度"""
        try:
            # 创建事件循环
            loop = asyncio.new_event_loop()
            try:
                # 执行异步查询，获取所有下载日志
                if self.task:
                    total_size = 0
                    total_time = 0
                    avg_speed = 0

                    # 注意：这里使用同步方式执行异步查询
                    # 这不是最佳实践，但对于这个简单的计算足够了
                    async def get_logs():
                        logs = await DownloadLog.filter(
                            task=self.task, is_success=True, file_size__not_isnull=True, download_time__not_isnull=True
                        ).all()
                        return logs

                    logs = loop.run_until_complete(get_logs())

                    # 计算总大小和总时间
                    for log in logs:
                        if log.file_size and log.download_time and log.download_time > 0:
                            total_size += log.file_size
                            total_time += log.download_time

                    # 计算平均速度
                    if total_time > 0:
                        avg_speed = total_size / total_time

                    return avg_speed
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"计算平均下载速度失败: {str(e)}")
            return 0

        return 0

    def create_summary_log(self, duration: float):
        """创建总结日志记录"""
        try:
            # 只有当关联了任务时才创建总结日志
            if not self.task:
                return

            # 创建事件循环
            loop = asyncio.new_event_loop()
            try:
                # 执行异步操作
                async def create_log():
                    # 计算总下载大小
                    total_size = 0
                    logs = await DownloadLog.filter(task=self.task, is_success=True, file_size__not_isnull=True).all()

                    for log in logs:
                        if log.file_size:
                            total_size += log.file_size

                    # 创建总结日志
                    summary_log = DownloadLog(
                        task=self.task,
                        file_path="SUMMARY",
                        file_size=total_size,
                        download_time=duration,
                        download_speed=(total_size / duration) if duration > 0 and total_size > 0 else 0,
                        is_success=self.failed_count == 0,
                        log_level="INFO",
                        log_message=f"下载总结: 总文件数={self.success_count + self.failed_count}, "
                        f"成功={self.success_count}, 失败={self.failed_count}, "
                        f"总大小={self._format_size(total_size)}, 总时间={duration:.2f}秒",
                    )

                    await summary_log.save()

                loop.run_until_complete(create_log())
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"创建总结日志失败: {str(e)}")

    async def log_to_db(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
    ):
        """
        将日志记录到数据库

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
        """
        # 如果没有关联任务，无法记录到数据库
        if not self.task:
            self.logger.info(f"无关联任务，跳过数据库日志记录: {message}")
            return

        # 根据日志级别记录到控制台
        if level == "INFO":
            self.logger.info(message)
        elif level == "WARNING":
            self.logger.warning(message)
        elif level == "ERROR":
            self.logger.error(message)
        elif level == "DEBUG":
            self.logger.debug(message)

        try:
            # 创建日志记录
            log_entry = DownloadLog(
                task=self.task,
                file_path=file_path or "",
                target_path=target_path,
                file_type=file_type,
                file_size=file_size,
                download_time=download_time,
                download_speed=download_speed,
                is_success=is_success,
                error_message=error_message,
                log_level=level,
                log_message=message,
            )

            # 保存到数据库
            await log_entry.save()

        except Exception as e:
            # 如果数据库记录失败，至少记录到控制台
            self.logger.error(f"记录日志到数据库失败: {str(e)}")

    def log_to_db_sync(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
    ):
        """
        同步方式将日志记录到数据库（通过创建事件循环）

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
        """
        # 如果没有关联任务，无法记录到数据库
        if not self.task:
            self.logger.info(f"无关联任务，跳过数据库日志记录: {message}")
            return

        # 根据日志级别记录到控制台
        if level == "INFO":
            self.logger.info(message)
        elif level == "WARNING":
            self.logger.warning(message)
        elif level == "ERROR":
            self.logger.error(message)
        elif level == "DEBUG":
            self.logger.debug(message)

        # 创建事件循环
        loop = asyncio.new_event_loop()
        try:
            # 执行异步操作
            loop.run_until_complete(
                self.log_to_db(
                    message=message,
                    level=level,
                    file_path=file_path,
                    target_path=target_path,
                    file_type=file_type,
                    file_size=file_size,
                    download_time=download_time,
                    download_speed=download_speed,
                    is_success=is_success,
                    error_message=error_message,
                )
            )
        except Exception as e:
            self.logger.error(f"同步记录日志到数据库失败: {str(e)}")
        finally:
            loop.close()


class StrmProcessor:
    """STRM处理器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
        """
        self.server = server
        self.output_dir = output_dir
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.logger = logging.getLogger("strm_processor")

    def generate_strm(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建STRM文件路径
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 分割路径和扩展名
            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            # 完整的STRM文件路径
            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            # 确保目录存在
            os.makedirs(strm_dir, exist_ok=True)

            # 写入STRM文件
            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            # 创建STRM文件记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )
                # 文件记录需要在外部保存

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",  # 失败没有目标路径
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )
                # 文件记录需要在外部保存

            return False, error_msg, strm_file

    async def download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载资源文件（音频、图片、字幕等）

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote

            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 获取文件类型
            file_type = file_info.get("file_type", FileType.OTHER)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            self.logger.error(error_msg)

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        # 读取响应内容
                        content = await response.read()

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=len(content),
                                is_success=True,
                            )

                        return True, None, strm_file

                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    self.logger.error(error_msg)

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    async def process_files(
        self,
        files: List[Dict[str, Any]],
        task: Optional[StrmTask] = None,
        download_resources: bool = False,
        download_server: Optional[MediaServer] = None,
    ) -> Dict[str, Any]:
        """
        处理文件列表，生成STRM文件

        Args:
            files: 文件列表
            task: 关联的任务
            download_resources: 是否下载资源文件（音频、图片、字幕等），默认为False
            download_server: 下载服务器，如果为None则使用媒体服务器

        Returns:
            处理结果统计
        """
        self.logger.info(f"开始处理{len(files)}个文件")
        self.logger.info(f"是否下载资源文件: {download_resources}")

        # 如果提供了下载服务器，使用下载服务器
        server_for_download = download_server or self.server
        if download_server:
            self.logger.info(f"使用指定下载服务器: {server_for_download.name}")

        # 结果统计
        result = {
            "success": 0,
            "failed": 0,
            "strm_files": [],
            "errors": [],
            "resources_total": 0,
            "resources_success": 0,
            "resources_failed": 0,
        }

        # 更新任务状态
        if task:
            task.total_files = len(files)
            task.start_time = datetime.now()
            task.status = TaskStatus.RUNNING
            await task.save()

        # 分离视频文件和资源文件
        video_files = []
        resource_files = []

        for file_info in files:
            if file_info["file_type"] == FileType.VIDEO:
                video_files.append(file_info)
            elif download_resources and file_info["file_type"] in [
                FileType.AUDIO,
                FileType.IMAGE,
                FileType.SUBTITLE,
                FileType.METADATA,
            ]:
                resource_files.append(file_info)

        # 更新资源文件总数统计
        result["resources_total"] = len(resource_files)

        # 处理视频文件，生成STRM文件
        video_count = len(video_files)
        for i, file_info in enumerate(video_files):
            success, error_msg, strm_file = self.generate_strm(file_info, task)

            if success:
                result["success"] += 1
                if strm_file:
                    await strm_file.save()
                    # 序列化对象为字典，避免JSON序列化问题
                    result["strm_files"].append(
                        {
                            "id": strm_file.id,
                            "source_path": strm_file.source_path,
                            "target_path": strm_file.target_path,
                            "file_type": strm_file.file_type,
                            "is_success": strm_file.is_success,
                            "error_message": strm_file.error_message,
                        }
                    )
            else:
                result["failed"] += 1
                result["errors"].append(error_msg)
                if strm_file:
                    await strm_file.save()

            # 更新任务进度，只考虑视频文件的进度
            if task:
                task.processed_files = i + 1
                task.success_files = result["success"]
                task.failed_files = result["failed"]
                await task.save()

        # 处理资源文件（如果启用了下载资源文件选项）
        if download_resources and resource_files:
            # 获取系统设置中的下载线程数
            system_settings = await SystemSettings.all().first()
            download_threads = 3  # 默认线程数

            if system_settings and system_settings.download_threads:
                download_threads = system_settings.download_threads
                self.logger.info(f"使用系统设置的下载线程数: {download_threads}")
            else:
                self.logger.info(f"使用默认下载线程数: {download_threads}")

            # 创建多线程下载器
            downloader = ResourceDownloader(
                server=server_for_download,
                output_dir=self.output_dir,
                threads=download_threads,
                enable_path_replacement=self.enable_path_replacement,
                replacement_path=self.replacement_path,
                task=task,
            )

            # 添加所有资源文件到下载队列
            for file_info in resource_files:
                downloader.add_file(file_info, task)

            self.logger.info(f"开始多线程下载 {len(resource_files)} 个资源文件，使用 {download_threads} 个线程")

            # 开始下载
            download_result = downloader.start_download()

            # 更新统计结果
            result["resources_success"] = download_result["success"]
            result["resources_failed"] = download_result["failed"]

            # 如果有错误，添加到errors列表
            if download_result["failed"] > 0:
                for item in download_result["results"]:
                    if not item["is_success"] and item["error_message"]:
                        result["errors"].append(item["error_message"])

            self.logger.info(f"资源文件下载完成：成功 {result['resources_success']}，失败 {result['resources_failed']}")

            # 记录下载总时间
            if download_result["duration"] > 0 and task:
                self.logger.info(f"下载耗时: {download_result['duration']:.2f}秒")
                # 添加下载总时间到任务详情
                task.download_duration = download_result["duration"]
                await task.save()

                # 查询总结日志记录，关联到任务
                try:
                    summary_log = await DownloadLog.filter(task=task, file_path="SUMMARY").first()
                    if summary_log:
                        self.logger.info(f"找到资源文件下载总结日志: ID={summary_log.id}")
                except Exception as e:
                    self.logger.error(f"查询下载总结日志失败: {str(e)}")

        # 更新任务状态
        if task:
            task.end_time = datetime.now()
            task.status = TaskStatus.COMPLETED
            await task.save()

        return result

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task: Optional[StrmTask] = None,
    download_resources: bool = False,
    download_server_id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    处理目录树，生成STRM文件

    Args:
        server_id: 媒体服务器ID
        files: 解析后的文件列表
        output_dir: 输出目录
        task: 关联的任务
        download_resources: 是否下载资源文件（音频、图片、字幕等），默认为False
        download_server_id: 下载服务器ID，默认为None（使用与媒体服务器相同的服务器）

    Returns:
        处理结果统计
    """
    logger = logging.getLogger("process_directory_tree")

    # 获取媒体服务器
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise ValueError(f"找不到ID为 {server_id} 的媒体服务器")

    # 如果指定了下载服务器，使用下载服务器进行资源下载
    download_server = server  # 默认使用相同的服务器
    if download_resources and download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)
        if not download_server:
            logger.warning(f"找不到ID为 {download_server_id} 的下载服务器，将使用媒体服务器 {server.name} 下载资源文件")
            download_server = server
        else:
            logger.info(f"将使用下载服务器 {download_server.name} 下载资源文件")

    # 获取系统设置
    system_settings = await SystemSettings.all().first()
    enable_path_replacement = False
    replacement_path = "/nas"
    log_dir = None  # 默认日志目录

    if system_settings:
        enable_path_replacement = system_settings.enable_path_replacement
        replacement_path = system_settings.replacement_path or "/nas"

        # 获取默认输出目录作为日志目录
        log_dir = system_settings.output_directory
        if log_dir:
            # 确保日志目录存在
            import os

            if not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir, exist_ok=True)
                    logger.info(f"创建日志目录: {log_dir}")
                except Exception as e:
                    logger.error(f"创建日志目录失败: {str(e)}")
                    log_dir = None  # 如果创建失败，不使用日志目录
            else:
                # 创建资源下载日志子目录
                logs_subdir = os.path.join(log_dir, "download_logs")
                try:
                    os.makedirs(logs_subdir, exist_ok=True)
                    log_dir = logs_subdir  # 使用子目录作为日志目录
                    logger.info(f"使用下载日志目录: {log_dir}")
                except Exception as e:
                    logger.error(f"创建下载日志子目录失败: {str(e)}")
                    # 继续使用主日志目录

        logger.info(
            f"系统设置: 路径替换={enable_path_replacement}, "
            f"替换路径={replacement_path}, "
            f"下载线程数={system_settings.download_threads}, "
            f"日志目录={log_dir or '未指定'}"
        )

    # 创建处理器，使用适当的服务器（媒体服务器用于STRM，下载服务器用于资源）
    processor = StrmProcessor(
        server, output_dir, enable_path_replacement=enable_path_replacement, replacement_path=replacement_path
    )

    # 如果使用不同的下载服务器，记录信息
    if download_server.id != server.id:
        logger.info(f"资源文件将从服务器 {download_server.name} ({download_server.base_url}) 下载")

    # 处理文件
    result = await processor.process_files(
        files,
        task,
        download_resources=download_resources,
        download_server=download_server,
    )

    return result
