"""
STRM处理器，用于生成STRM文件
"""

import os
import re
import asyncio
import threading
import queue
import time
from datetime import datetime
from typing import List, Dict, Any, Tuple, Optional, Union
import shutil
import zipfile
import logging
from concurrent.futures import ThreadPoolExecutor

from urllib.parse import quote

from app.models.strm.models import StrmTask, StrmFile
from app.models.strm.file import FileType, MediaServer


class ResourceDownloader:
    """资源文件多线程下载器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        threads: int = 3,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
        task: Optional[StrmTask] = None,
    ):
        """
        初始化资源文件下载器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            threads: 下载线程数
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
            task: 关联的任务，用于记录日志
        """
        self.server = server
        self.output_dir = output_dir
        self.threads = max(1, threads)  # 至少1个线程
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.task = task  # 保存任务引用

        # 配置只输出到控制台的日志记录器
        logger_name = f"resource_downloader_{task.id if task else ''}"
        self.logger = logging.getLogger(logger_name)

        # 确保logger不会继承父日志处理器（防止输出到文件）
        self.logger.propagate = False

        # 清除已有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 只添加控制台处理器
        console_handler = logging.StreamHandler()
        self.logger.addHandler(console_handler)

        # 下载队列和状态
        self.download_queue = queue.Queue()
        self.lock = threading.Lock()
        self.success_count = 0
        self.failed_count = 0
        self.results = []  # 保存下载结果
        self.stop_event = threading.Event()

    def add_file(self, file_info: Dict[str, Any], task: Optional[StrmTask] = None):
        """
        添加文件到下载队列

        Args:
            file_info: 文件信息
            task: 关联的任务
        """
        self.download_queue.put((file_info, task))

    def download_worker(self):
        """下载线程函数"""
        import aiohttp
        import aiofiles
        import threading
        import time

        # 创建一个新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        thread_name = threading.current_thread().name
        # 记录线程启动日志
        self.log_to_db_sync(f"🧵 下载线程 {thread_name} 启动")

        while not self.stop_event.is_set():
            try:
                # 尝试从队列获取一个文件，如果没有则等待一小段时间
                try:
                    (file_info, task) = self.download_queue.get(block=True, timeout=0.5)
                except queue.Empty:
                    continue

                # 获取文件路径和类型
                file_path = file_info["path"]
                file_type = file_info.get("file_type", FileType.OTHER)

                try:
                    # 使用现有的下载逻辑，但不记录中间日志
                    success, error_msg, resource_file, download_details = loop.run_until_complete(
                        self._download_resource_file(file_info, task)
                    )

                    # 更新统计和保存文件记录
                    with self.lock:
                        # 计算当前进度百分比
                        total_files = self.success_count + self.failed_count + 1  # +1表示当前文件
                        queue_size = self.download_queue.qsize()
                        total_tasks = total_files + queue_size
                        progress_pct = (total_files / total_tasks) * 100 if total_tasks > 0 else 0

                        # 添加emoji状态标识
                        if success:
                            self.success_count += 1
                            status_emoji = "✅"
                            log_level = "INFO"
                        else:
                            self.failed_count += 1
                            status_emoji = "❌"
                            log_level = "ERROR"

                        # 格式化文件大小和速度
                        file_size = download_details.get("file_size", 0)
                        size_str = self._format_size(file_size) if file_size else "N/A"

                        speed = download_details.get("speed", 0)
                        speed_str = self._format_size(speed) + "/s" if speed > 0 else "N/A"

                        duration = download_details.get("duration", 0)
                        duration_str = f"{duration:.2f}s" if duration > 0 else "N/A"

                        # 构建URL，移除敏感信息
                        url = download_details.get("url", "")
                        safe_url = url.split("?")[0] if url else ""

                        # 生成综合日志消息
                        log_message = (
                            f"{status_emoji} 线程 {thread_name} | "
                            f"文件: {os.path.basename(file_path)} | "
                            f"大小: {size_str} | "
                            f"耗时: {duration_str} | "
                            f"速度: {speed_str} | "
                            f"进度: {progress_pct:.1f}% [{total_files}/{total_tasks}]"
                        )

                        # 如果有警告信息，添加警告图标
                        if "warning" in download_details:
                            log_message += f" | ⚠️ {download_details['warning']}"

                        # 记录单条综合日志
                        self.log_to_db_sync(
                            log_message,
                            level=log_level,
                            file_path=file_path,
                            target_path=download_details.get("target_path", ""),
                            file_type=file_type,
                            file_size=file_size,
                            download_time=duration,
                            download_speed=speed,
                            is_success=success,
                            error_message=error_msg,
                            url=safe_url,
                            progress=progress_pct,
                        )

                        if resource_file:
                            # 异步保存文件记录
                            loop.run_until_complete(resource_file.save())

                            # 添加到结果列表
                            self.results.append(
                                {
                                    "id": resource_file.id,
                                    "source_path": resource_file.source_path,
                                    "target_path": resource_file.target_path,
                                    "file_type": resource_file.file_type,
                                    "is_success": resource_file.is_success,
                                    "error_message": resource_file.error_message,
                                }
                            )

                except Exception as e:
                    # 处理下载过程中的异常
                    self.failed_count += 1
                    error_message = f"下载过程中发生异常: {str(e)}"

                    # 计算当前进度
                    total_files = self.success_count + self.failed_count
                    queue_size = self.download_queue.qsize()
                    total_tasks = total_files + queue_size
                    progress_pct = (total_files / total_tasks) * 100 if total_tasks > 0 else 0

                    # 记录综合异常日志
                    self.log_to_db_sync(
                        f"❌ 线程 {thread_name} | 文件: {os.path.basename(file_path)} | 错误: {error_message} | 进度: {progress_pct:.1f}% [{total_files}/{total_tasks}]",
                        level="ERROR",
                        file_path=file_path,
                        file_type=file_type,
                        is_success=False,
                        error_message=error_message,
                        progress=progress_pct,
                    )

                    # 创建失败记录
                    try:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_message,
                        )
                        loop.run_until_complete(strm_file.save())

                        # 添加到结果列表
                        self.results.append(
                            {
                                "id": strm_file.id,
                                "source_path": strm_file.source_path,
                                "target_path": strm_file.target_path,
                                "file_type": strm_file.file_type,
                                "is_success": strm_file.is_success,
                                "error_message": strm_file.error_message,
                            }
                        )
                    except Exception as save_error:
                        self.log_to_db_sync(
                            f"⚠️ 线程 {thread_name} 保存失败记录异常: {str(save_error)}",
                            level="ERROR",
                            file_path=file_path,
                            is_success=False,
                        )

                # 无论成功还是失败，都标记任务完成
                self.download_queue.task_done()

            except Exception as e:
                # 处理线程级别的异常
                try:
                    error_message = f"下载线程 {thread_name} 发生意外异常: {str(e)}"
                    self.log_to_db_sync(f"🔥 {error_message}", level="ERROR", is_success=False)
                except Exception:
                    # 如果连日志都无法记录，只能打印到控制台
                    print(f"严重错误: 下载线程 {thread_name} 异常且无法记录日志: {str(e)}")

                # 确保任务被标记为完成，防止队列阻塞
                try:
                    self.download_queue.task_done()
                except Exception:
                    pass

        # 线程结束
        self.log_to_db_sync(f"🏁 下载线程 {thread_name} 结束")

    async def _download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile], Dict[str, Any]]:
        """
        下载单个资源文件，内部使用

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录, 下载详情)
        """
        # 收集下载详情，用于生成综合日志
        download_details = {
            "start_time": time.time(),
            "end_time": None,
            "duration": 0,
            "speed": 0,
            "file_size": 0,
            "url": "",
            "is_success": False,
            "error_message": None,
            "file_path": "",
            "target_path": "",
            "file_type": "",
        }

        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote
            import time
            import ssl

            # 构建媒体URL
            file_path = file_info["path"]
            file_type = file_info.get("file_type", FileType.OTHER)

            # 记录基本信息
            download_details["file_path"] = file_path
            download_details["file_type"] = file_type

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"
            download_details["url"] = url

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)
            download_details["target_path"] = resource_path

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    # 先尝试HEAD请求获取文件信息
                    expected_content_length = None
                    try:
                        async with session.head(url) as head_response:
                            if head_response.status == 200:
                                content_length = head_response.headers.get("Content-Length")
                                if content_length:
                                    expected_content_length = int(content_length)
                    except Exception:
                        # 忽略HEAD请求失败，继续尝试GET请求
                        pass

                    # 开始GET请求下载文件
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file, download_details

                        # 获取Content-Length
                        content_length = response.headers.get("Content-Length")
                        if content_length:
                            expected_content_length = int(content_length)

                        # 读取响应内容
                        content = await response.read()
                        file_size = len(content)
                        download_details["file_size"] = file_size

                        # 验证文件有效性
                        if file_size == 0:
                            error_msg = f"下载的文件大小为0，文件无效"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file, download_details

                        # 如果之前HEAD请求获取到了预期的文件大小，进行比较验证
                        if expected_content_length is not None and expected_content_length > 0:
                            # 允许有10%的误差
                            size_difference = abs(expected_content_length - file_size)
                            if size_difference > expected_content_length * 0.1:  # 超过10%的差异
                                download_details["warning"] = (
                                    f"文件大小与预期不符: 预期 {expected_content_length}，实际 {file_size}"
                                )

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 验证写入的文件
                        try:
                            # 检查文件是否存在且大小正确
                            if not os.path.exists(resource_path):
                                error_msg = f"文件写入失败: 文件不存在"
                                download_details["is_success"] = False
                                download_details["error_message"] = error_msg
                                return False, error_msg, None, download_details

                            actual_file_size = os.path.getsize(resource_path)
                            if actual_file_size != file_size:
                                error_msg = f"文件写入不完整: 预期 {file_size}，实际 {actual_file_size}"
                                download_details["is_success"] = False
                                download_details["error_message"] = error_msg
                                return False, error_msg, None, download_details
                        except Exception as e:
                            error_msg = f"验证文件失败: {str(e)}"
                            download_details["is_success"] = False
                            download_details["error_message"] = error_msg
                            return False, error_msg, None, download_details

                        # 计算下载耗时和速度
                        end_time = time.time()
                        duration = end_time - download_details["start_time"]
                        speed = file_size / duration if duration > 0 else 0

                        # 更新下载详情
                        download_details["end_time"] = end_time
                        download_details["duration"] = duration
                        download_details["speed"] = speed
                        download_details["is_success"] = True

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=file_size,
                                is_success=True,
                            )

                        return True, None, strm_file, download_details

                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    download_details["is_success"] = False
                    download_details["error_message"] = error_msg

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file, download_details

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            download_details["is_success"] = False
            download_details["error_message"] = error_msg

            # 创建失败记录
            strm_file = None
            file_path = file_info.get("path", "")
            file_type = file_info.get("file_type", FileType.OTHER)

            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",
                    file_type=file_type,
                    is_success=False,
                    error_message=error_msg,
                )

            return False, error_msg, strm_file, download_details

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小为人类可读形式"""
        import math

        if size_bytes == 0:
            return "0 B"

        size_name = ("B", "KB", "MB", "GB", "TB")
        i = int(math.log(size_bytes, 1024))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)

        return f"{s} {size_name[i]}"

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path

    def start_download(self):
        """开始多线程下载"""
        # 创建线程池
        workers = []

        # 确保初始化日志记录
        self.log_initial_summary()

        # 记录开始时间
        start_time = datetime.now()

        # 记录下载服务器和线程数信息
        self.log_to_db_sync(f"🖥️ 使用下载服务器: {self.server.name} ({self.server.base_url})")
        self.log_to_db_sync(f"🧵 下载线程数: {self.threads}")

        self.log_to_db_sync(f"🚀 === 开始多线程下载 ===")
        self.log_to_db_sync(f"📋 队列中文件数: {self.download_queue.qsize()}")

        for i in range(self.threads):
            thread = threading.Thread(target=self.download_worker, name=f"DownloadWorker-{i + 1}")
            thread.daemon = True
            thread.start()
            workers.append(thread)

        # 等待所有任务完成
        self.download_queue.join()

        # 发送停止信号
        self.stop_event.set()

        # 等待所有线程结束
        for thread in workers:
            thread.join(timeout=1.0)

        # 计算总时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 记录下载总结
        self.log_to_db_sync(f"🏁 === 下载任务完成 ===", download_time=duration)
        self.log_to_db_sync(f"⏱️ 总时间: {duration:.2f}秒", download_time=duration)
        self.log_to_db_sync(f"✅ 成功: {self.success_count} 个文件", is_success=True)
        self.log_to_db_sync(
            f"{'❌' if self.failed_count > 0 else '✅'} 失败: {self.failed_count} 个文件",
            is_success=self.failed_count == 0,
        )

        if self.success_count > 0 and duration > 0:
            avg_time = duration / self.success_count
            avg_speed = self.calculate_average_speed()
            if avg_speed > 0:
                avg_speed_str = self._format_size(avg_speed)
                self.log_to_db_sync(
                    f"🚀 平均下载时间: {avg_time:.2f}秒/文件, 平均速度: {avg_speed_str}/秒",
                    download_time=avg_time,
                    download_speed=avg_speed,
                )
            else:
                self.log_to_db_sync(f"⏱️ 平均下载时间: {avg_time:.2f}秒/文件", download_time=avg_time)

        self.log_to_db_sync(f"===========================================")

        # 创建总结日志
        self.create_summary_log(duration)

        # 返回下载统计
        return {
            "success": self.success_count,
            "failed": self.failed_count,
            "results": self.results,
            "duration": duration,
        }

    def calculate_average_speed(self) -> float:
        """计算平均下载速度"""
        try:
            # 如果有任务记录
            if self.task and self.task.log_content:
                total_size = 0
                total_time = 0
                avg_speed = 0

                # 从日志内容中解析文件大小和下载时间
                # 这种方法避免了使用ORM模型和事件循环的问题
                log_lines = self.task.log_content.split("\n")
                for line in log_lines:
                    if "| 大小:" in line and "| 耗时:" in line and "| 状态: 成功" in line:
                        try:
                            # 提取文件大小 (MB)
                            size_part = line.split("| 大小:")[1].split("|")[0].strip()
                            if "MB" in size_part:
                                size_mb = float(size_part.replace("MB", "").strip())
                                size_bytes = size_mb * 1024 * 1024
                            elif "KB" in size_part:
                                size_kb = float(size_part.replace("KB", "").strip())
                                size_bytes = size_kb * 1024
                            elif "B" in size_part:
                                size_bytes = float(size_part.replace("B", "").strip())
                            else:
                                continue

                            # 提取下载时间 (秒)
                            time_part = line.split("| 耗时:")[1].split("s")[0].strip()
                            time_seconds = float(time_part)

                            # 累加
                            if size_bytes > 0 and time_seconds > 0:
                                total_size += size_bytes
                                total_time += time_seconds
                        except Exception:
                            # 忽略解析错误
                            pass

                # 计算平均速度
                if total_time > 0:
                    avg_speed = total_size / total_time

                return avg_speed

            return 0
        except Exception as e:
            print(f"计算平均下载速度失败: {str(e)}")
            return 0

    def create_summary_log(self, duration: float):
        """
        创建下载总结日志

        Args:
            duration: 总下载时间（秒）
        """
        # 如果没有关联任务，无法创建总结日志
        if not self.task:
            return

        try:
            summary_items = [
                f"📊 资源文件下载总结 📊",
                f"总文件数: {self.success_count + self.failed_count}",
                f"成功文件: {self.success_count} ✅",
                f"失败文件: {self.failed_count} {('❌' if self.failed_count > 0 else '✅')}",
                f"总耗时: {duration:.2f}秒 ⏱️",
            ]

            # 添加平均速度信息
            avg_speed = self.calculate_average_speed()
            if avg_speed > 0:
                avg_speed_str = self._format_size(avg_speed)
                avg_time = duration / self.success_count if self.success_count > 0 else 0
                summary_items.append(f"平均下载时间: {avg_time:.2f}秒/文件")
                summary_items.append(f"平均下载速度: {avg_speed_str}/秒 🚀")

            # 添加结论
            if self.failed_count == 0:
                summary_items.append(f"结论: 所有文件下载成功 ✅")
            else:
                summary_items.append(f"结论: 部分文件下载失败 ⚠️")

            # 合并总结信息
            summary_message = " | ".join(summary_items)

            # 使用线程安全的方式保存总结日志
            self._thread_safe_save_log_content(summary_message, "INFO", is_summary=True)

        except Exception as e:
            # 记录错误到控制台
            print(f"创建总结日志失败: {str(e)}")

    def _thread_safe_save_log_content(self, message: str, level: str = "INFO", is_summary: bool = False):
        """
        线程安全地保存日志内容

        Args:
            message: 日志消息
            level: 日志级别
            is_summary: 是否是总结日志
        """
        try:
            # 创建一个新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 创建日志条目
                timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3]
                log_entry = f"[{timestamp}] [{level}] {message}"

                # 获取当前日志内容
                current_log = self.task.log_content or ""

                # 添加新日志
                self.task.log_content = current_log + "\n" + log_entry if current_log else log_entry

                # 保存任务
                loop.run_until_complete(self.task.save(update_fields=["log_content"]))

                # 打印到控制台
                print(log_entry)

            finally:
                loop.close()
                asyncio.set_event_loop(None)  # 清除当前线程的事件循环引用

        except Exception as e:
            print(f"线程安全保存日志内容失败: {str(e)}")

    async def log_to_db(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
    ):
        """
        将日志记录到任务的log_content字段

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
        """
        # 如果没有关联任务，无法记录日志
        if not self.task:
            # 不记录日志，直接返回
            return

        try:
            # 格式化日志条目
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] [{level}] {message}"

            # 添加文件路径信息
            if file_path:
                log_entry += f" | 文件: {file_path}"

            # 添加目标路径信息
            if target_path:
                log_entry += f" | 目标: {target_path}"

            # 添加错误信息
            if error_message:
                log_entry += f" | 错误: {error_message}"

            # 添加成功状态
            status_text = "成功" if is_success else "失败"
            log_entry += f" | 状态: {status_text}"

            # 添加文件大小和下载时间信息（如有）
            if file_size is not None:
                size_formatted = self._format_size(file_size)
                log_entry += f" | 大小: {size_formatted}"

            if download_time is not None:
                log_entry += f" | 耗时: {download_time:.2f}s"

            if download_speed is not None and download_speed > 0:
                speed_formatted = self._format_size(int(download_speed)) + "/s"
                log_entry += f" | 速度: {speed_formatted}"

            # 在日志条目末尾添加换行符
            log_entry += "\n"

            # 获取当前的日志内容
            current_log = self.task.log_content or ""

            # 追加新的日志条目
            self.task.log_content = current_log + log_entry

            # 保存任务
            await self.task.save(update_fields=["log_content"])

        except Exception as e:
            # 记录日志失败时输出到控制台
            print(f"记录日志到任务失败: {str(e)}")

    def log_to_db_sync(
        self,
        message: str,
        level: str = "INFO",
        file_path: Optional[str] = None,
        target_path: Optional[str] = None,
        file_type: Optional[str] = None,
        file_size: Optional[int] = None,
        download_time: Optional[float] = None,
        download_speed: Optional[float] = None,
        is_success: bool = True,
        error_message: Optional[str] = None,
        url: Optional[str] = None,
        progress: Optional[float] = None,
    ):
        """
        同步方式记录日志到任务的log_content字段

        Args:
            message: 日志消息
            level: 日志级别（INFO, WARNING, ERROR, DEBUG）
            file_path: 文件路径
            target_path: 目标文件路径
            file_type: 文件类型
            file_size: 文件大小
            download_time: 下载耗时
            download_speed: 下载速度
            is_success: 是否成功
            error_message: 错误信息
            url: 下载URL
            progress: 下载进度百分比
        """
        # 如果没有关联任务，无法记录日志
        if not self.task:
            # 不记录日志，直接返回
            return

        try:
            # 直接创建日志条目
            timestamp = datetime.now().isoformat()
            log_entry = f"[{timestamp}] [{level}] {message}"

            # 简化日志格式，之前内容已经包含在message中
            # 仅保留一些可能需要但未包含在message中的关键信息

            # 添加URL信息（如果提供且未包含在message中）
            if url and "URL:" not in message:
                log_entry += f" | URL: {url}"

            # 添加错误信息（如果未包含在message中）
            if error_message and "错误:" not in message and "error:" not in message.lower():
                log_entry += f" | 错误: {error_message}"

            # 在日志条目末尾添加换行符
            log_entry += "\n"

            # 直接更新内存中的日志内容
            current_log = self.task.log_content or ""
            self.task.log_content = current_log + log_entry

            # 打印日志到控制台作为备份
            print(log_entry.strip())

            # 如果在上一个事件循环中，则需要异步保存
            if hasattr(asyncio, "get_running_loop"):
                try:
                    loop = asyncio.get_running_loop()
                    if loop.is_running():
                        # 创建一个新线程来处理同步保存
                        threading.Thread(target=self._thread_safe_save_log, args=(self.task, ["log_content"])).start()
                        return
                except RuntimeError:
                    # 没有运行中的事件循环，继续使用同步方式
                    pass

            # 同步保存任务
            try:
                # 创建一个新的事件循环来处理保存
                save_loop = asyncio.new_event_loop()
                save_loop.run_until_complete(self.task.save(update_fields=["log_content"]))
                save_loop.close()
            except Exception as save_error:
                print(f"同步保存日志失败: {str(save_error)}")

        except Exception as e:
            # 任何错误都打印到控制台
            print(f"记录日志失败: {str(e)}")

    def _thread_safe_save_log(self, task, update_fields):
        """在新线程中安全地保存日志，避免事件循环冲突"""
        try:
            # 创建一个新的事件循环
            save_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(save_loop)
            # 运行保存操作
            save_loop.run_until_complete(task.save(update_fields=update_fields))
            save_loop.close()
        except Exception as e:
            print(f"线程安全保存失败: {str(e)}")

    def log_initial_summary(self):
        """初始化汇总日志记录"""
        # 只记录到数据库，不再使用日志文件

        # 创建汇总信息
        summary_info = [
            f"🚀 === 资源文件下载开始 ===",
            f"🔖 任务ID: {self.task.id if self.task else 'N/A'}",
            f"🖥️ 服务器: {self.server.name} ({self.server.base_url})",
            f"📁 输出目录: {self.output_dir}",
            f"🧵 下载线程数: {self.threads}",
            f"🔄 启用路径替换: {self.enable_path_replacement}",
        ]

        if self.enable_path_replacement:
            summary_info.append(f"📌 替换路径: {self.replacement_path}")

        summary_info.append(f"==============================")
        summary_message = "\n".join(summary_info)

        # 如果有任务，尝试记录到数据库
        if self.task:
            try:
                self.log_to_db_sync(summary_message, file_path="INIT", is_success=True)
            except Exception as e:
                print(f"记录初始日志到数据库失败: {str(e)}")


class StrmProcessor:
    """STRM处理器"""

    def __init__(
        self,
        server: MediaServer,
        output_dir: str,
        enable_path_replacement: bool = False,
        replacement_path: str = "/nas",
    ):
        """
        初始化STRM处理器

        Args:
            server: 媒体服务器配置
            output_dir: 输出目录
            enable_path_replacement: 是否启用路径替换
            replacement_path: 路径替换值
        """
        self.server = server
        self.output_dir = output_dir
        self.enable_path_replacement = enable_path_replacement
        self.replacement_path = replacement_path
        self.logger = logging.getLogger("strm_processor")

    def generate_strm(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        生成单个STRM文件

        Args:
            file_info: 文件信息，包含路径、类型等
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建STRM文件路径
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 分割路径和扩展名
            file_name_without_ext, _ = os.path.splitext(file_name)
            strm_file_name = f"{file_name_without_ext}.strm"

            # 完整的STRM文件路径
            strm_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            strm_path = os.path.join(strm_dir, strm_file_name)

            # 确保目录存在
            os.makedirs(strm_dir, exist_ok=True)

            # 写入STRM文件
            with open(strm_path, "w", encoding="utf-8") as f:
                f.write(url)

            # 创建STRM文件记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path=strm_path,
                    file_type=file_info["file_type"],
                    file_size=os.path.getsize(strm_path),
                    is_success=True,
                )
                # 文件记录需要在外部保存

            return True, None, strm_file

        except Exception as e:
            error_msg = f"生成STRM文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path,
                    target_path="",  # 失败没有目标路径
                    file_type=file_info["file_type"],
                    is_success=False,
                    error_message=error_msg,
                )
                # 文件记录需要在外部保存

            return False, error_msg, strm_file

    async def download_resource_file(
        self, file_info: Dict[str, Any], task: Optional[StrmTask] = None
    ) -> Tuple[bool, Optional[str], Optional[StrmFile]]:
        """
        下载单个资源文件，与generate_strm类似，但专用于资源文件

        Args:
            file_info: 文件信息
            task: 关联的任务

        Returns:
            (是否成功, 错误信息, 生成的文件记录)
        """
        try:
            import aiohttp
            import aiofiles
            from urllib.parse import quote

            # 构建媒体URL
            file_path = file_info["path"]

            # 应用路径替换逻辑
            processed_path = self.replace_base_path(file_path)
            url = f"{self.server.base_url}{quote(processed_path)}"

            # 构建目标文件路径，保持原始目录结构
            file_name = os.path.basename(file_path)
            file_dir = os.path.dirname(file_path)

            # 完整的资源文件目录路径
            resource_dir = os.path.join(self.output_dir, file_dir.lstrip("/"))
            resource_path = os.path.join(resource_dir, file_name)

            # 确保目录存在
            os.makedirs(resource_dir, exist_ok=True)

            # 获取文件类型
            file_type = file_info.get("file_type", FileType.OTHER)

            # 通过HTTP下载文件
            timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)

            # 禁用SSL验证，使用兼容的方式
            await self.log_to_db(f"SSL验证已禁用", file_path=file_path)

            # 创建自定义连接器，禁用SSL验证
            connector = aiohttp.TCPConnector(verify_ssl=False)

            async with aiohttp.ClientSession(timeout=timeout, connector=connector) as session:
                try:
                    async with session.get(url) as response:
                        if response.status != 200:
                            error_msg = f"下载资源文件失败: HTTP状态码 {response.status}"
                            self.logger.error(error_msg)

                            # 创建失败记录
                            strm_file = None
                            if task:
                                strm_file = StrmFile(
                                    task=task,
                                    source_path=file_path,
                                    target_path="",
                                    file_type=file_type,
                                    is_success=False,
                                    error_message=error_msg,
                                )
                            return False, error_msg, strm_file

                        # 读取响应内容
                        content = await response.read()

                        # 写入文件
                        async with aiofiles.open(resource_path, "wb") as f:
                            await f.write(content)

                        # 创建资源文件记录
                        strm_file = None
                        if task:
                            strm_file = StrmFile(
                                task=task,
                                source_path=file_path,
                                target_path=resource_path,
                                file_type=file_type,
                                file_size=len(content),
                                is_success=True,
                            )

                        return True, None, strm_file
                except aiohttp.ClientError as e:
                    error_msg = f"下载资源文件失败: {str(e)}"
                    self.logger.error(error_msg)

                    # 创建失败记录
                    strm_file = None
                    if task:
                        strm_file = StrmFile(
                            task=task,
                            source_path=file_path,
                            target_path="",
                            file_type=file_type,
                            is_success=False,
                            error_message=error_msg,
                        )
                    return False, error_msg, strm_file

        except Exception as e:
            error_msg = f"下载资源文件失败: {str(e)}"
            self.logger.error(error_msg)

            # 创建失败记录
            strm_file = None
            if task:
                strm_file = StrmFile(
                    task=task,
                    source_path=file_path if "file_path" in locals() else "",
                    target_path="",
                    file_type=file_info.get("file_type", FileType.OTHER),
                    is_success=False,
                    error_message=error_msg,
                )
            return False, error_msg, strm_file

    async def process_files(
        self,
        files: List[Dict[str, Any]],
        task: Optional[StrmTask] = None,
        download_resources: bool = False,
        download_server: Optional[MediaServer] = None,
    ) -> Dict[str, Any]:
        """
        处理文件列表，生成STRM文件

        Args:
            files: 文件列表
            task: 关联的任务
            download_resources: 是否下载资源文件（音频、图片、字幕等），默认为False
            download_server: 下载服务器，如果为None则使用媒体服务器

        Returns:
            处理结果统计
        """
        self.logger.info(f"开始处理{len(files)}个文件")
        self.logger.info(f"是否下载资源文件: {download_resources}")

        # 如果提供了下载服务器，使用下载服务器
        server_for_download = download_server or self.server
        if download_server:
            self.logger.info(f"使用指定下载服务器: {server_for_download.name}")
            # 记录下载服务器信息到任务
            if task:
                await task.log(f"使用指定下载服务器: {server_for_download.name} ({server_for_download.base_url})")
        else:
            self.logger.info(f"使用媒体服务器作为下载服务器: {server_for_download.name}")
            # 记录下载服务器信息到任务
            if task:
                await task.log(
                    f"使用媒体服务器作为下载服务器: {server_for_download.name} ({server_for_download.base_url})"
                )

        # 结果统计
        result = {
            "success": 0,  # 成功生成的STRM文件数
            "failed": 0,  # 失败的STRM文件数
            "strm_files": [],  # STRM文件记录
            "errors": [],  # 错误信息
            "resources_total": 0,  # 资源文件总数
            "resources_success": 0,  # 成功下载的资源文件数
            "resources_failed": 0,  # 失败的资源文件数
            "video_files_total": 0,  # 视频文件总数（生成STRM文件的源文件）
        }

        # 分离视频文件和资源文件
        video_files = []
        resource_files = []

        for file_info in files:
            if file_info["file_type"] == FileType.VIDEO:
                video_files.append(file_info)
            elif download_resources and file_info["file_type"] in [
                FileType.AUDIO,
                FileType.IMAGE,
                FileType.SUBTITLE,
                FileType.METADATA,
            ]:
                resource_files.append(file_info)

        # 更新文件数量统计
        result["video_files_total"] = len(video_files)
        result["resources_total"] = len(resource_files)

        # 更新任务状态
        if task:
            # 任务的总文件数只包括视频文件（STRM）和需要下载的资源文件
            task.total_files = len(video_files) + (len(resource_files) if download_resources else 0)
            task.start_time = datetime.now()
            task.status = TaskStatus.RUNNING
            await task.save()

            # 日志记录文件统计
            await task.log(
                f"总文件数: {task.total_files} (视频文件: {len(video_files)}, 资源文件: {len(resource_files) if download_resources else 0})"
            )

        # 处理视频文件，生成STRM文件
        video_count = len(video_files)
        for i, file_info in enumerate(video_files):
            success, error_msg, strm_file = self.generate_strm(file_info, task)

            if success:
                result["success"] += 1
                if strm_file:
                    await strm_file.save()
                    # 序列化对象为字典，避免JSON序列化问题
                    result["strm_files"].append(
                        {
                            "id": strm_file.id,
                            "source_path": strm_file.source_path,
                            "target_path": strm_file.target_path,
                            "file_type": strm_file.file_type,
                            "is_success": strm_file.is_success,
                            "error_message": strm_file.error_message,
                        }
                    )
            else:
                result["failed"] += 1
                result["errors"].append(error_msg)
                if strm_file:
                    await strm_file.save()

            # 更新任务进度
            if task:
                # 已处理文件数只累计STRM文件
                task.processed_files = i + 1
                task.success_files = result["success"]
                task.failed_files = result["failed"]
                await task.save()

        # 处理资源文件（如果启用了下载资源文件选项）
        if download_resources and resource_files:
            # 获取系统设置中的下载线程数
            system_settings = await SystemSettings.all().first()
            download_threads = 3  # 默认线程数

            if system_settings and system_settings.download_threads:
                download_threads = system_settings.download_threads
                self.logger.info(f"使用系统设置的下载线程数: {download_threads}")
                # 记录下载线程数到任务
                if task:
                    await task.log(f"使用系统设置的下载线程数: {download_threads}")
            else:
                self.logger.info(f"使用默认下载线程数: {download_threads}")
                # 记录下载线程数到任务
                if task:
                    await task.log(f"使用默认下载线程数: {download_threads}")

            # 创建多线程下载器
            downloader = ResourceDownloader(
                server=server_for_download,
                output_dir=self.output_dir,
                threads=download_threads,
                enable_path_replacement=self.enable_path_replacement,
                replacement_path=self.replacement_path,
                task=task,
            )

            # 添加所有资源文件到下载队列
            for file_info in resource_files:
                downloader.add_file(file_info, task)

            self.logger.info(f"开始多线程下载 {len(resource_files)} 个资源文件，使用 {download_threads} 个线程")
            if task:
                await task.log(f"开始多线程下载 {len(resource_files)} 个资源文件")

            # 开始下载
            download_result = downloader.start_download()

            # 更新统计结果
            result["resources_success"] = download_result["success"]
            result["resources_failed"] = download_result["failed"]

            # 如果有错误，添加到errors列表
            if download_result["failed"] > 0:
                for item in download_result["results"]:
                    if not item["is_success"] and item["error_message"]:
                        result["errors"].append(item["error_message"])

            self.logger.info(f"资源文件下载完成：成功 {result['resources_success']}，失败 {result['resources_failed']}")

            # 更新任务统计
            if task:
                # 更新总处理文件数和成功/失败计数
                task.processed_files = len(video_files) + len(resource_files)
                task.success_files = result["success"] + result["resources_success"]
                task.failed_files = result["failed"] + result["resources_failed"]
                await task.save()

                # 记录下载结果
                await task.log(f"STRM文件生成：成功 {result['success']}，失败 {result['failed']}")
                await task.log(f"资源文件下载：成功 {result['resources_success']}，失败 {result['resources_failed']}")

            # 记录下载总时间
            if download_result["duration"] > 0 and task:
                self.logger.info(f"下载耗时: {download_result['duration']:.2f}秒")
                # 添加下载总时间到任务详情
                task.download_duration = download_result["duration"]
                await task.save()

                # 总结日志直接记录到任务的log_content
                try:
                    # 检查任务日志中是否已包含总结信息
                    if task.log_content and "资源文件下载总结" in task.log_content:
                        self.logger.info("任务日志中已包含资源文件下载总结信息")
                except Exception as e:
                    self.logger.error(f"查询下载总结信息失败: {str(e)}")

        # 更新任务状态
        if task:
            task.end_time = datetime.now()
            task.status = TaskStatus.COMPLETED
            await task.save()

            # 最终任务统计
            total_success = result["success"] + result["resources_success"]
            total_failed = result["failed"] + result["resources_failed"]
            await task.log(f"任务完成：总成功 {total_success}，总失败 {total_failed}")

        return result

    def create_zip_archive(self, output_path: Optional[str] = None) -> str:
        """
        将生成的STRM文件打包为ZIP文件

        Args:
            output_path: 输出ZIP文件路径，如果为None则自动生成

        Returns:
            ZIP文件路径
        """
        if not output_path:
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            output_path = f"{self.output_dir.rstrip('/')}_strm_{timestamp}.zip"

        with zipfile.ZipFile(output_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(self.output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.output_dir)
                        zipf.write(file_path, arcname)

        return output_path

    def clean_output_directory(self) -> bool:
        """
        清理输出目录

        Returns:
            是否成功清理
        """
        try:
            if os.path.exists(self.output_dir):
                shutil.rmtree(self.output_dir)
                os.makedirs(self.output_dir, exist_ok=True)
            return True
        except Exception as e:
            self.logger.error(f"清理输出目录失败: {str(e)}")
            return False

    def replace_base_path(self, original_path: str) -> str:
        """
        替换路径中的主路径部分

        Args:
            original_path: 原始路径，如"/nas/动漫/一拳超人/S01E01.mkv"

        Returns:
            替换主路径后的新路径
        """
        if not self.enable_path_replacement or not original_path:
            return original_path

        try:
            # 使用'/'分割路径
            path_parts = original_path.split("/")
            if len(path_parts) > 1:
                # 替换第一层主路径（通常是第二个元素，因为第一个元素是空字符串）
                path_parts[1] = self.replacement_path.strip("/")
                # 合并路径
                new_path = "/" + "/".join(path_parts[1:])
                return new_path
            return original_path
        except Exception as e:
            self.logger.error(f"替换路径错误: {str(e)}")
            return original_path


async def process_directory_tree(
    server_id: int,
    files: List[Dict[str, Any]],
    output_dir: str,
    task: Optional[StrmTask] = None,
    download_resources: bool = False,
    download_server_id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    处理目录树，生成STRM文件

    Args:
        server_id: 媒体服务器ID
        files: 解析后的文件列表
        output_dir: 输出目录
        task: 关联的任务
        download_resources: 是否下载资源文件（音频、图片、字幕等），默认为False
        download_server_id: 下载服务器ID，默认为None（使用与媒体服务器相同的服务器）

    Returns:
        处理结果统计
    """
    logger = logging.getLogger("process_directory_tree")

    # 获取媒体服务器
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise ValueError(f"找不到ID为 {server_id} 的媒体服务器")

    # 如果指定了下载服务器，使用下载服务器进行资源下载
    download_server = server  # 默认使用相同的服务器
    if download_resources and download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)
        if not download_server:
            logger.warning(f"找不到ID为 {download_server_id} 的下载服务器，将使用媒体服务器 {server.name} 下载资源文件")
            download_server = server
        else:
            logger.info(f"将使用下载服务器 {download_server.name} 下载资源文件")

    # 获取系统设置
    system_settings = await SystemSettings.all().first()
    enable_path_replacement = False
    replacement_path = "/nas"
    log_dir = None  # 默认日志目录

    if system_settings:
        enable_path_replacement = system_settings.enable_path_replacement
        replacement_path = system_settings.replacement_path or "/nas"

        # 获取默认输出目录作为日志目录
        log_dir = system_settings.output_directory
        if log_dir:
            # 确保日志目录存在
            import os

            if not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir, exist_ok=True)
                    logger.info(f"创建日志目录: {log_dir}")
                except Exception as e:
                    logger.error(f"创建日志目录失败: {str(e)}")
                    log_dir = None  # 如果创建失败，不使用日志目录
            else:
                # 创建资源下载日志子目录
                logs_subdir = os.path.join(log_dir, "download_logs")
                try:
                    os.makedirs(logs_subdir, exist_ok=True)
                    log_dir = logs_subdir  # 使用子目录作为日志目录
                    logger.info(f"使用下载日志目录: {log_dir}")
                except Exception as e:
                    logger.error(f"创建下载日志子目录失败: {str(e)}")
                    # 继续使用主日志目录

        logger.info(
            f"系统设置: 路径替换={enable_path_replacement}, "
            f"替换路径={replacement_path}, "
            f"下载线程数={system_settings.download_threads}, "
            f"日志目录={log_dir or '未指定'}"
        )

    # 创建处理器，使用适当的服务器（媒体服务器用于STRM，下载服务器用于资源）
    processor = StrmProcessor(
        server, output_dir, enable_path_replacement=enable_path_replacement, replacement_path=replacement_path
    )

    # 如果使用不同的下载服务器，记录信息
    if download_server.id != server.id:
        logger.info(f"资源文件将从服务器 {download_server.name} ({download_server.base_url}) 下载")

    # 处理文件
    result = await processor.process_files(
        files,
        task,
        download_resources=download_resources,
        download_server=download_server,
    )

    return result
