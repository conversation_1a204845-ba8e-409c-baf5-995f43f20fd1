"""
115目录树解析器，用于解析115导出的目录树文件
"""
import os
import re
from typing import List, Dict, Any, Optional, Tuple
import chardet

from app.models.strm import FileType


class TreeParser:
    """115目录树解析器"""
    
    def __init__(self):
        # 视频文件扩展名
        self.video_extensions = [
            "mkv", "iso", "ts", "mp4", "avi", "rmvb", "wmv", 
            "m2ts", "mpg", "flv", "rm", "mov"
        ]
        
        # 音频文件扩展名
        self.audio_extensions = [
            "mp3", "flac", "wav", "aac", "ape", "ogg", "m4a"
        ]
        
        # 图片文件扩展名
        self.image_extensions = [
            "jpg", "jpeg", "png", "gif", "bmp", "tiff", "svg", 
            "webp", "heic"
        ]
        
        # 字幕文件扩展名
        self.subtitle_extensions = [
            "srt", "ass", "ssa", "sub", "idx", "sup"
        ]
        
        # 元数据文件扩展名
        self.metadata_extensions = [
            "nfo", "xml", "json"
        ]
    
    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析115导出的目录树文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            解析后的文件列表，每项包含文件路径、类型等信息
        """
        try:
            result = []
            current_path_stack = []
            
            # Detect file encoding
            with open(file_path, 'rb') as f:
                raw_data = f.read()
                encoding_result = chardet.detect(raw_data)
                encoding = encoding_result['encoding'] or 'utf-8'

            # Decode the file content and process line by line
            file_content = raw_data.decode(encoding)
            for line in file_content.splitlines():
                # 移除 BOM 和多余空白
                line = line.lstrip('\ufeff').rstrip()
                
                # 计算目录级别
                line_depth = line.count('|')
                
                # 获取当前项名称
                item_name = line.split('|-')[-1].strip()
                if not item_name:
                    continue
                
                # 维护路径栈
                while len(current_path_stack) > line_depth:
                    current_path_stack.pop()
                
                if len(current_path_stack) == line_depth:
                    if current_path_stack:
                        current_path_stack.pop()
                
                current_path_stack.append(item_name)
                
                # 构建完整路径
                full_path = '/' + '/'.join(current_path_stack)
                
                # 检查是否为目录
                filename = os.path.basename(full_path)
                if "." not in filename:
                    continue
                
                # 格式化路径 (移除第一段路径)
                formatted_path = self._format_file_path(full_path)
                
                # 获取文件类型
                file_type, extension = self._get_file_type(filename)
                
                if file_type is not None:
                    result.append({
                        "path": formatted_path,
                        "file_type": file_type,
                        "extension": extension,
                        "file_name": filename,
                        "directory": os.path.dirname(formatted_path)
                    })
            
            return result
        except Exception as e:
            raise ValueError(f"解析115目录树文件失败: {str(e)}")
    
    def _format_file_path(self, file_path: str) -> str:
        """
        格式化文件路径，移除第一段路径（通常是"根目录/nas"等）
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            格式化后的文件路径
        """
        first_slash = file_path.find('/')
        if first_slash != -1:
            second_slash = file_path.find('/', first_slash + 1)
            if second_slash != -1:
                return file_path[second_slash:]
        return file_path
    
    def _get_file_type(self, filename: str) -> Tuple[Optional[str], str]:
        """
        根据文件名判断文件类型
        
        Args:
            filename: 文件名
            
        Returns:
            (文件类型, 文件扩展名)
        """
        _, extension = os.path.splitext(filename)
        extension = extension[1:].lower() if extension else ""
        
        if extension in self.video_extensions:
            return FileType.VIDEO, extension
        elif extension in self.audio_extensions:
            return FileType.AUDIO, extension
        elif extension in self.image_extensions:
            return FileType.IMAGE, extension
        elif extension in self.subtitle_extensions:
            return FileType.SUBTITLE, extension
        elif extension in self.metadata_extensions:
            return FileType.METADATA, extension
        else:
            return FileType.OTHER, extension
    
    def filter_files(self, files: List[Dict[str, Any]], file_type: Optional[str] = None, 
                    keyword: Optional[str] = None, path_pattern: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        根据条件过滤文件
        
        Args:
            files: 文件列表
            file_type: 文件类型
            keyword: 关键词
            path_pattern: 路径模式
            
        Returns:
            过滤后的文件列表
        """
        result = files
        
        if file_type:
            result = [f for f in result if f["file_type"] == file_type]
        
        if keyword:
            result = [f for f in result if keyword.lower() in f["path"].lower()]
        
        if path_pattern:
            regex = re.compile(path_pattern)
            result = [f for f in result if regex.search(f["path"])]
        
        return result 