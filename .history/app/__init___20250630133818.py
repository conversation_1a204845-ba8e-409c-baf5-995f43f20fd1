from contextlib import asynccontextmanager
from datetime import datetime
import asyncio

from fastapi import <PERSON><PERSON><PERSON>
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from redis import asyncio as aioredis
from starlette.staticfiles import StaticFiles

from app.api.v1.utils import refresh_api_list
from app.core.exceptions import SettingNotFound
from app.core.init_app import (
    init_menus,
    init_users,
    make_middlewares,
    modify_db,
    register_db,
    register_exceptions,
    register_routers,
    init_system_settings,
)
from app.log import log
from app.models.system import Log
from app.models.system import LogType, LogDetailType

try:
    from app.settings import APP_SETTINGS
except ImportError:
    raise SettingNotFound("Can not import settings")


def create_app() -> FastAPI:
    if APP_SETTINGS.DEBUG:
        _app = FastAPI(
            title=APP_SETTINGS.APP_TITLE,
            description=APP_SETTINGS.APP_DESCRIPTION,
            version=APP_SETTINGS.VERSION,
            openapi_url="/openapi.json",
            middleware=make_middlewares(),
            lifespan=lifespan,
        )
    else:
        _app = FastAPI(
            title=APP_SETTINGS.APP_TITLE,
            description=APP_SETTINGS.APP_DESCRIPTION,
            version=APP_SETTINGS.VERSION,
            openapi_url=None,
            middleware=make_middlewares(),
            lifespan=lifespan,
        )
    register_db(_app)
    register_exceptions(_app)
    register_routers(_app, prefix="/api")

    redis = aioredis.from_url(url=APP_SETTINGS.REDIS_URL)
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    return _app


@asynccontextmanager
async def lifespan(_app: FastAPI):
    start_time = datetime.now()
    try:
        from app.core.bgtask import BgTasks

        log.info("开始初始化应用...")
        await modify_db()
        log.info("数据库迁移和升级已完成")

        await init_menus()
        log.info("菜单初始化完成")

        await refresh_api_list()
        log.info("API列表刷新完成")

        await init_users()
        log.info("用户初始化完成")

        await init_system_settings()
        log.info("系统设置初始化完成")

        # 初始化后台任务系统
        log.info("正在初始化后台任务系统...")
        await BgTasks.init_bg_tasks_obj()

        # 验证后台任务系统是否正常运行
        try:
            health_status = await BgTasks.get_health_status()
            log.info(f"后台任务系统健康状态: {health_status}")

            # 额外检查：尝试获取队列大小，确保任务队列已正确初始化
            queue_size = BgTasks._task_queue.qsize()
            log.info(f"后台任务队列初始化成功，当前队列大小: {queue_size}")

            # 额外检查：确保工作线程正在运行
            if not BgTasks._worker_running:
                log.warning("工作线程标记为未运行，正在尝试重新启动...")
                await BgTasks._ensure_worker_running()

            # 确认工作线程状态
            if BgTasks._worker_running and BgTasks._worker_task and not BgTasks._worker_task.done():
                log.info("后台任务工作线程正在运行")
            else:
                log.error("后台任务工作线程未正常运行，尝试重新启动...")
                BgTasks._worker_running = False
                if BgTasks._worker_task:
                    try:
                        BgTasks._worker_task.cancel()
                    except:
                        pass
                BgTasks._worker_task = None
                await BgTasks._ensure_worker_running()
        except Exception as e:
            import traceback

            error_detail = traceback.format_exc()
            log.error(f"验证后台任务系统时发生错误: {str(e)}\n{error_detail}")
            # 尝试重新初始化
            log.info("尝试重新初始化后台任务系统...")
            await BgTasks.init_bg_tasks_obj()

        log.info("应用初始化完成，等待请求...")
        await Log.create(log_type=LogType.SystemLog, log_detail_type=LogDetailType.SystemStart)
        yield

    finally:
        end_time = datetime.now()
        runtime = (end_time - start_time).total_seconds() / 60
        log.info(f"App {_app.title} runtime: {runtime} min")  # noqa
        await Log.create(log_type=LogType.SystemLog, log_detail_type=LogDetailType.SystemStop)


app = create_app()

app.mount("/static", StaticFiles(directory=APP_SETTINGS.STATIC_ROOT), name="static")
