"""
STRM任务控制器，用于管理STRM文件生成任务

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

import os
import shutil
import tempfile
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

from fastapi import HTTPException
from fastapi.responses import FileResponse

from app.models.strm import StrmTask, StrmFile, MediaServer, TaskStatus, SystemSettings
from app.models.system import User
from app.utils.strm.processor import StrmProcessor, process_directory_tree
from app.controllers.strm.upload import get_parse_result
from app.settings import APP_SETTINGS

# 定义STRM文件生成的临时目录
STRM_OUTPUT_DIR = Path(APP_SETTINGS.BASE_DIR) / "strm_output"
STRM_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


async def create_strm_task(
    record_id: int,
    server_id: int,
    user: User,
    output_dir: Optional[str] = None,
    custom_name: Optional[str] = None,
    download_server_id: Optional[int] = None,
    download_resources: bool = False,
) -> StrmTask:
    """
    创建STRM文件生成任务

    Args:
        record_id: 上传记录ID
        server_id: 媒体服务器ID
        user: 当前用户
        output_dir: 自定义输出目录，默认为临时目录
        custom_name: 自定义任务名称
        download_server_id: 下载服务器ID，默认为None（使用与媒体服务器相同的服务器）
        download_resources: 是否下载资源文件，默认为False

    Returns:
        创建的任务对象
    """
    # 检查媒体服务器是否存在
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise HTTPException(status_code=404, detail=f"找不到ID为{server_id}的媒体服务器")

    # 检查下载服务器是否存在（如果提供）
    download_server = None
    if download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)
        if not download_server:
            raise HTTPException(status_code=404, detail=f"找不到ID为{download_server_id}的下载服务器")

    # 获取解析结果
    parse_result = await get_parse_result(record_id, user, "all", 1, 10, True)
    if not parse_result:
        raise HTTPException(status_code=404, detail=f"找不到ID为{record_id}的文件解析结果")

    # 确定输出目录
    if not output_dir:
        # 获取系统设置中的默认输出目录
        system_settings = await SystemSettings.all().first()
        if system_settings and system_settings.output_directory:
            # 使用系统设置中的默认输出目录
            output_dir = system_settings.output_directory
        else:
            # 使用默认的临时目录
            output_dir = str(STRM_OUTPUT_DIR / f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user.id}")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 创建任务名称
    if not custom_name:
        custom_name = (
            f"STRM生成-{parse_result.get('file_name', '未命名')}-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    # 创建任务记录
    task = await StrmTask.create(
        name=custom_name,
        server=server,  # 媒体服务器
        source_file=str(record_id),  # 存储上传记录ID
        output_dir=output_dir,
        total_files=parse_result.get("stats", {}).get("video", 0),  # 只计算视频文件
        status=TaskStatus.PENDING,
        created_by=user,
    )

    # 如果提供了下载服务器ID或需要下载资源文件，更新日志字段存储额外信息
    log_info = {}
    if download_server_id:
        log_info["download_server_id"] = download_server_id
    if download_resources:
        log_info["download_resources"] = "true"

    if log_info:
        task.log_file = repr(log_info)
        await task.save()

    return task


async def start_strm_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    启动STRM文件生成任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        任务启动结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务状态
    if task.status == TaskStatus.RUNNING:
        return {"success": False, "message": "任务已在运行中"}

    # 获取上传记录ID和服务器
    record_id = int(task.source_file)
    server = await MediaServer.get(id=task.server_id)

    # 检查是否有下载服务器和资源文件下载选项
    download_server_id = None
    download_resources = False

    # 解析log_file字段中的额外信息
    if task.log_file:
        try:
            # 尝试将log_file解析为Python字典
            log_info = eval(task.log_file)
            if isinstance(log_info, dict):
                # 获取下载服务器ID
                if "download_server_id" in log_info:
                    download_server_id = log_info["download_server_id"]

                # 获取是否下载资源文件
                if "download_resources" in log_info and log_info["download_resources"] == "true":
                    download_resources = True
        except (SyntaxError, ValueError, TypeError) as e:
            # 兼容旧版本格式（例如 "download_server_id:123"）
            if task.log_file.startswith("download_server_id:"):
                try:
                    download_server_id = int(task.log_file.split(":")[-1])
                except ValueError:
                    pass

    # 如果有下载服务器ID，获取下载服务器
    download_server = None
    if download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)

    # 获取解析结果
    parse_result = await get_parse_result(record_id, user, "all", 1, 10, True)
    if not parse_result:
        raise HTTPException(status_code=404, detail=f"找不到ID为{record_id}的文件解析结果")

    # 获取视频文件和资源文件（如需下载）
    files = []
    if download_resources:
        # 如果启用了下载资源文件，包括所有类型的文件
        files = parse_result.get("parsed_files", [])
    else:
        # 只处理视频文件
        files = [f for f in parse_result.get("parsed_files", []) if f["file_type"] == "video"]

    # 更新任务状态
    task.status = TaskStatus.RUNNING
    task.start_time = datetime.now()
    await task.save()

    # 启动处理过程
    try:
        # 使用下载服务器ID（如果有）
        process_server_id = download_server.id if download_server else server.id
        result = await process_directory_tree(
            server_id=process_server_id,
            files=files,
            output_dir=task.output_dir,
            task=task,
            download_resources=download_resources,
            download_server_id=download_server.id if download_server else None,
        )

        return {"success": True, "message": "任务启动成功", "task_id": task.id, "result": result}
    except Exception as e:
        # 更新任务状态为失败
        task.status = TaskStatus.FAILED
        await task.save()
        raise HTTPException(status_code=500, detail=f"任务启动失败: {str(e)}")


async def get_task_status(task_id: int, user: User) -> Dict[str, Any]:
    """
    获取任务状态

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        任务状态信息
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此任务")

    # 获取关联的文件记录
    files = await StrmFile.filter(task_id=task_id).all()

    # 计算进度百分比
    progress = 0
    if task.total_files > 0:
        progress = min(100, int(task.processed_files * 100 / task.total_files))

    # 计算资源文件数 (音频、图片、字幕、元数据文件的总和)
    resource_files_count = 0

    # 获取上传记录和解析结果
    try:
        from app.models.strm.upload import UploadRecord

        # 任务的source_file存储的是上传记录ID
        record_id = int(task.source_file)
        record = await UploadRecord.get_or_none(id=record_id)

        if record and record.parsed_result:
            # 从解析结果中获取文件统计信息
            stats = record.parsed_result.get("stats", {})

            # 计算资源文件数
            resource_files_count = (
                stats.get("audio", 0) + stats.get("image", 0) + stats.get("subtitle", 0) + stats.get("metadata", 0)
            )
    except Exception as e:
        # 如果获取失败，记录错误但不影响其他功能
        print(f"计算资源文件数出错: {str(e)}")

    return {
        "id": task.id,
        "name": task.name,
        "status": task.status,
        "total_files": task.total_files,
        "processed_files": task.processed_files,
        "success_files": task.success_files,
        "failed_files": task.failed_files,
        "progress": progress,
        "start_time": task.start_time.isoformat() if task.start_time else None,
        "end_time": task.end_time.isoformat() if task.end_time else None,
        "output_dir": task.output_dir,
        "files": [
            {
                "id": f.id,
                "source_path": f.source_path,
                "target_path": f.target_path,
                "file_type": f.file_type,
                "file_size": f.file_size,
                "is_success": f.is_success,
                "error_message": f.error_message,
            }
            for f in files[:10]  # 只返回前10条记录，避免数据过大
        ],
        "file_count": len(files),
        "resource_files_count": resource_files_count,  # 添加资源文件数
    }


async def get_user_tasks(
    user: User,
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户的任务列表，支持搜索和过滤

    Args:
        user: 当前用户
        page: 页码
        page_size: 每页数量
        search: 按名称搜索
        status: 按状态过滤
        start_date: 开始日期过滤
        end_date: 结束日期过滤

    Returns:
        任务列表
    """
    # 计算偏移量
    offset = (page - 1) * page_size

    # 构建基本查询
    query = StrmTask.filter(created_by=user)

    # 添加搜索条件
    if search:
        query = query.filter(name__icontains=search)

    # 添加状态过滤
    if status:
        # 状态值映射，处理前端传入的状态值
        status_mapping = {
            "SUCCESS": "completed",  # 前端使用SUCCESS，后端是completed
            "CANCELED": "canceled",  # 前端是大写，后端是小写
            "FAILED": "failed",  # 前端是大写，后端是小写
            "PENDING": "pending",  # 前端是大写，后端是小写
            "RUNNING": "running",  # 前端是大写，后端是小写
        }
        # 将前端状态值转换为后端状态值
        backend_status = status_mapping.get(status, status.lower())
        query = query.filter(status=backend_status)

    # 添加日期范围过滤
    if start_date:
        # 将日期字符串转换为datetime对象
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query = query.filter(create_time__gte=start_datetime)

    if end_date:
        # 将日期字符串转换为datetime对象，并设置为当天的结束时间
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
        query = query.filter(create_time__lte=end_datetime)

    # 查询任务并计算总数
    tasks = await query.offset(offset).limit(page_size).order_by("-create_time")
    total = await query.count()

    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "tasks": [
            {
                "id": t.id,
                "name": t.name,
                "status": t.status,
                "total_files": t.total_files,
                "processed_files": t.processed_files,
                "success_files": t.success_files,
                "failed_files": t.failed_files,
                "progress": min(100, int(t.processed_files * 100 / t.total_files)) if t.total_files > 0 else 0,
                "start_time": t.start_time.isoformat() if t.start_time else None,
                "end_time": t.end_time.isoformat() if t.end_time else None,
            }
            for t in tasks
        ],
    }


async def download_strm_files(task_id: int, user: User) -> FileResponse:
    """
    下载生成的STRM文件包

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        文件响应对象
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限下载此任务的文件")

    # 检查任务状态
    if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        raise HTTPException(status_code=400, detail="任务尚未完成，无法下载")

    # 检查输出目录是否存在
    output_dir = Path(task.output_dir)
    if not output_dir.exists():
        raise HTTPException(status_code=404, detail="输出目录不存在，可能已被删除")

    # 创建ZIP文件
    zip_file_name = f"strm_files_{task_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
    zip_path = STRM_OUTPUT_DIR / zip_file_name

    try:
        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, output_dir)
                        zipf.write(file_path, arcname)

        # 返回文件响应
        return FileResponse(zip_path, media_type="application/zip", filename=zip_file_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建ZIP文件失败: {str(e)}")


async def cancel_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    取消任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        操作结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务状态
    if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
        return {"success": False, "message": "任务已完成或已取消，无法取消"}

    # 更新任务状态
    task.status = TaskStatus.CANCELED
    task.end_time = datetime.now()
    await task.save()

    return {"success": True, "message": "任务已取消"}


async def delete_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    删除任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        删除结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此任务")

    # 获取任务关联的文件记录
    task_files = await StrmFile.filter(task_id=task_id)

    # 删除任务及关联的文件记录
    await StrmFile.filter(task_id=task_id).delete()
    await task.delete()

    return {"success": True, "message": "任务删除成功", "task_id": task_id}


async def get_task_logs(
    task_id: int,
    user: User,
    page: int = 1,
    page_size: int = 50,
    level: Optional[str] = None,
    search: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取任务日志

    Args:
        task_id: 任务ID
        user: 当前用户
        page: 页码
        page_size: 每页数量
        level: 日志级别过滤
        search: 日志内容搜索

    Returns:
        任务日志列表
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此任务日志")

    # 构建查询
    query = DownloadLog.filter(task_id=task_id)

    # 应用日志级别过滤
    if level:
        query = query.filter(log_level=level.upper())

    # 应用搜索过滤
    if search:
        query = query.filter(log_message__contains=search)

    # 获取总记录数
    total = await query.count()

    # 计算分页
    skip = (page - 1) * page_size

    # 查询日志记录
    logs = await query.order_by("-create_time").offset(skip).limit(page_size).prefetch_related("task")

    # 格式化日志记录
    log_list = []
    for log in logs:
        log_list.append(
            {
                "id": log.id,
                "task_id": log.task_id,
                "timestamp": log.create_time.isoformat(),
                "level": log.log_level,
                "message": log.log_message,
                "file_path": log.file_path,
                "target_path": log.target_path,
                "file_type": log.file_type.value if log.file_type else None,
                "file_size": log.file_size,
                "download_time": log.download_time,
                "download_speed": log.download_speed,
                "is_success": log.is_success,
                "error_message": log.error_message,
            }
        )

    return {"logs": log_list, "total": total, "page": page, "page_size": page_size}
