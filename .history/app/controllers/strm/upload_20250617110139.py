import shutil
import time
import os
from pathlib import Path
from fastapi import UploadFile, HTTPException
from app.settings import APP_SETTINGS
from app.utils.strm.parser import TreeParser
from typing import Dict, Any, Union
from app.models.system import User
from app.models.strm.upload import UploadRecord, UploadStatus
from fastapi.responses import FileResponse

UPLOAD_DIR = Path(APP_SETTINGS.BASE_DIR) / "strm_uploads"
UPLOAD_DIR.mkdir(parents=True, exist_ok=True)

# 不再需要生成唯一文件名，因为文件内容直接存储在数据库中


async def handle_file_upload(file: UploadFile, user: User) -> UploadRecord:
    """
    处理文件上传，验证并将文件内容保存至数据库，同时创建上传记录。
    :param file: 上传的文件对象.
    :param user: 当前用户.
    :return: 创建的上传记录对象.
    """
    if not file.filename.endswith(".txt"):
        raise HTTPException(status_code=400, detail="无效的文件类型, 只支持 .txt 文件。")

    file_size = file.file.seek(0, 2)
    file.file.seek(0)
    if file_size > 10 * 1024 * 1024:  # 10MB
        raise HTTPException(status_code=413, detail="文件太大, 超过 10MB。")

    # 读取文件内容到内存
    try:
        file_content = await file.read()
    finally:
        await file.close()
    
    # 创建上传记录，直接保存文件内容到数据库
    upload_record = await UploadRecord.create(
        filename=file.filename,  # 保存原始文件名，用于显示和下载
        filesize=file_size,
        file_content=file_content,  # 保存文件内容到数据库
        uploader=user,
        status=UploadStatus.UPLOADED
    )

    return upload_record


async def parse_uploaded_file(record_id: int) -> Dict[str, Any]:
    """
    解析已上传的115目录树文件并更新记录
    :param record_id: 上传记录的ID
    :return: 解析结果，包含文件统计和部分文件列表
    """
    # 获取上传记录
    record = await UploadRecord.get_or_none(id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="上传记录不存在")
    
    # 获取文件内容
    content = None
    if record.file_content:
        content = record.file_content
    elif record.file_path:
        # 处理旧数据：如果数据库中没有文件内容，则尝试从文件系统读取
        path = Path(record.file_path)
        if path.exists():
            content = path.read_bytes()
        else:
            raise HTTPException(status_code=404, detail="文件不存在")
    else:
        raise HTTPException(status_code=404, detail="文件内容不存在")
    
    # 创建临时文件
    temp_file_path = UPLOAD_DIR / f"temp_{record_id}.txt"
    
    await record.update_from_dict({"status": UploadStatus.PARSING})
    await record.save()
    
    # 写入临时文件用于解析
    with temp_file_path.open("wb") as f:
        f.write(content)

    try:
        # 解析文件
        parser = TreeParser()
        parsed_files = parser.parse_file(str(temp_file_path))
        
        # 添加详细日志，查看文件类型分布
        file_types = {}
        subtitle_files = []
        
        for f in parsed_files:
            file_type = f["file_type"]
            file_types[file_type] = file_types.get(file_type, 0) + 1
            
            # 记录所有被识别为字幕的文件
            if file_type == "subtitle":
                subtitle_files.append(f["path"])
        
        print(f"文件类型统计: {file_types}")
        print(f"字幕文件列表: {subtitle_files}")
        
        # 计算统计信息
        stats = {
            "total": len(parsed_files),
            "video": len([f for f in parsed_files if f["file_type"] == "video"]),
            "audio": len([f for f in parsed_files if f["file_type"] == "audio"]),
            "image": len([f for f in parsed_files if f["file_type"] == "image"]),
            "subtitle": len([f for f in parsed_files if f["file_type"] == "subtitle"]),
            "metadata": len([f for f in parsed_files if f["file_type"] == "metadata"]),
            "other": len([f for f in parsed_files if f["file_type"] == "other"])
        }
        
                # 打印最终统计结果
        print(f"最终统计结果: {stats}")
        
        # 手动修正统计数据，确保与实际文件类型一致
        # 如果没有特定类型的文件，将统计值设为0
        for key in ["video", "audio", "image", "subtitle", "metadata", "other"]:
            if key not in file_types:
                stats[key] = 0

        result = {
            "file_name": record.filename,  # 使用数据库中保存的原始文件名
            "parsed_files": parsed_files,  # 存储完整的解析结果，不再限制为100条
            "total_files": len(parsed_files),
            "stats": stats
        }
        
        # 更新记录，记录解析时间
        from datetime import datetime
        await record.update_from_dict({
            "status": UploadStatus.PARSED, 
            "parsed_result": result,
            "parse_time": datetime.now()
        })
        await record.save()
        
        # 解析完成后删除临时文件
        if temp_file_path.exists():
            temp_file_path.unlink()
        
        return result
    except Exception as e:
        await record.update_from_dict({"status": UploadStatus.FAILED})
        await record.save()
        
        # 发生错误时也删除临时文件
        if temp_file_path.exists():
            temp_file_path.unlink()
        
        raise HTTPException(status_code=500, detail=f"解析文件失败: {str(e)}")


async def delete_upload_record(record_id: int, user: User) -> None:
    """
    删除上传记录
    :param record_id: 上传记录的ID
    :param user: 当前用户，用于验证权限
    :return: None
    """
    # 获取上传记录
    record = await UploadRecord.get_or_none(id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="上传记录不存在")
    
    # 检查权限，只能删除自己上传的文件
    if record.uploader_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此记录")
    
    # 处理旧记录：尝试删除物理文件（如果存在）
    if record.file_path:
        file_path = Path(record.file_path)
        if file_path.exists():
            try:
                file_path.unlink()
            except Exception as e:
                # 如果文件删除失败，记录错误但继续删除数据库记录
                print(f"删除文件失败: {str(e)}")
    
    # 删除数据库记录
    await record.delete()


from fastapi.responses import Response

async def download_upload_file(record_id: int, user: User) -> Response:
    """
    下载已上传的文件
    :param record_id: 上传记录的ID
    :param user: 当前用户，用于验证权限
    :return: 文件响应
    """
    # 获取上传记录
    record = await UploadRecord.get_or_none(id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="上传记录不存在")
    
    # 检查权限，只能下载自己上传的文件
    if record.uploader_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限下载此文件")
    
    # 获取文件内容
    if record.file_content is None:
        # 处理旧数据：如果数据库中没有文件内容，但有file_path，则尝试从文件系统读取
        if record.file_path:
            file_path = Path(record.file_path)
            if file_path.exists():
                content = file_path.read_bytes()
            else:
                raise HTTPException(status_code=404, detail="文件不存在")
        else:
            raise HTTPException(status_code=404, detail="文件内容不存在")
    else:
        # 从数据库获取文件内容
        content = record.file_content
    
    # 返回文件响应
    return Response(
        content=content,
        media_type="text/plain",
        headers={
            "Content-Disposition": f'attachment; filename="{record.filename}"'
        }
    )

async def get_parse_result(
    record_id: int, 
    user: User, 
    file_type: str = "all", 
    page: int = 1, 
    page_size: int = 10
) -> Dict[str, Any]:
    """
    获取已解析文件的解析结果，支持按文件类型过滤和分页
    :param record_id: 上传记录的ID
    :param user: 当前用户，用于验证权限
    :param file_type: 文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)
    :param page: 页码
    :param page_size: 每页数量
    :return: 包含过滤和分页后的解析结果
    """
    # 获取上传记录
    record = await UploadRecord.get_or_none(id=record_id)
    if not record:
        raise HTTPException(status_code=404, detail="上传记录不存在")
    
    # 检查权限，只能查看自己上传的文件的解析结果
    if record.uploader_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此记录")
    
    # 检查文件状态
    if record.status != UploadStatus.PARSED:
        raise HTTPException(status_code=400, detail="文件尚未成功解析")
    
    # 检查并返回解析结果
    if not record.parsed_result:
        raise HTTPException(status_code=404, detail="解析结果不存在")
    
    # 复制解析结果，不修改原始数据
    result = dict(record.parsed_result)
    
    # 过滤文件
    all_files = result["parsed_files"]
    if file_type != "all":
        filtered_files = [f for f in all_files if f["file_type"] == file_type]
    else:
        filtered_files = all_files
    
    # 计算分页
    total = len(filtered_files)
    start = (page - 1) * page_size
    end = min(start + page_size, total)
    
    # 分页处理
    paginated_files = filtered_files[start:end] if start < total else []
    
    # 更新结果
    result["parsed_files"] = paginated_files
    result["pagination"] = {
        "page": page,
        "page_size": page_size,
        "total": total
    }
    
    return result