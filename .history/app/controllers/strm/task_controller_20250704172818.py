"""
STRM任务控制器，用于管理STRM文件生成任务

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

import os
import shutil
import tempfile
import zipfile
import json
import asyncio
import logging
import traceback
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, AsyncGenerator
import re

from fastapi import HTTPException
from fastapi.responses import FileResponse

from app.models.strm import StrmTask, StrmFile, MediaServer, TaskStatus, SystemSettings, FileType, ProcessType
from app.models.strm import DownloadTask, DownloadTaskStatus
from app.models.system import User
from app.utils.strm.processor import process_directory_tree
from app.controllers.strm.upload import get_parse_result
from app.settings import APP_SETTINGS

# 创建日志记录器
logger = logging.getLogger("strm_task_controller")


async def create_strm_task(
    record_id: int,
    server_id: int,
    user: User,
    output_dir: Optional[str] = None,
    custom_name: Optional[str] = None,
    download_server_id: Optional[int] = None,
    threads: int = 1,
) -> StrmTask:
    """
    创建STRM处理任务，将同时处理STRM文件生成和资源文件下载

    Args:
        record_id: 上传记录ID
        server_id: 媒体服务器ID
        user: 当前用户
        output_dir: 自定义输出目录，默认为临时目录
        custom_name: 自定义任务名称
        download_server_id: 下载服务器ID，用于下载资源文件
        threads: 下载线程数

    Returns:
        创建的任务对象
    """
    # 检查媒体服务器是否存在
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise HTTPException(code=404, msg=f"找不到ID为{server_id}的媒体服务器")

    # 检查下载服务器是否存在
    download_server = None
    if download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)
        if not download_server:
            raise HTTPException(code=404, msg=f"找不到ID为{download_server_id}的下载服务器")

    # 确定输出目录
    if not output_dir:
        # 从系统设置中获取默认输出目录
        settings = await SystemSettings.all().first()
        base_output_dir = settings.output_directory if settings and settings.output_directory else "strm_output"

        # 确保基础输出目录是Path对象
        base_output_dir = Path(base_output_dir)

        # 创建带有时间戳和用户ID的任务输出目录
        task_dir_name = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user.id}"
        output_dir = str(base_output_dir / task_dir_name)

    # 创建任务名称
    if not custom_name:
        custom_name = f"STRM任务-记录{record_id}-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    # 创建任务记录
    task = await StrmTask.create(
        name=custom_name,
        server=server,  # 媒体服务器
        download_server=download_server,  # 下载服务器
        source_file=str(record_id),  # 存储上传记录ID
        output_dir=output_dir,
        total_files=0,  # 初始化为0，后续更新
        status=TaskStatus.PENDING,
        created_by=user,
        threads=threads,
    )

    return task


async def get_task_status(task_id: int, user: User) -> Dict[str, Any]:
    """
    获取任务状态

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        包含任务状态信息的字典
    """
    # 验证任务存在并属于当前用户
    task = await StrmTask.get_or_none(id=task_id, created_by=user)
    if not task:
        raise HTTPException(code=404, msg=f"找不到ID为{task_id}的任务或无权访问")

    from app.log.log import log
    from app.models.strm import DownloadTask, DownloadTaskStatus

    try:
        # --- 改进的统计逻辑 ---
        # 1. 获取所有与该任务相关的下载任务记录
        all_download_tasks = await DownloadTask.filter(task_id=task.id).all()

        # 2. 分类统计
        strm_tasks = [dt for dt in all_download_tasks if dt.process_type == "strm_generation"]
        resource_tasks = [dt for dt in all_download_tasks if dt.file_type == "resource_download"]

        # 统计STRM文件
        strm_files_count = len(strm_tasks)
        strm_success = sum(1 for dt in strm_tasks if dt.status == DownloadTaskStatus.COMPLETED)
        strm_failed = sum(1 for dt in strm_tasks if dt.status == DownloadTaskStatus.FAILED)
        strm_pending = sum(1 for dt in strm_tasks if dt.status in [DownloadTaskStatus.PENDING])

        # 统计资源文件
        resource_files_count = len(resource_tasks)
        resource_success = sum(1 for dt in resource_tasks if dt.status == DownloadTaskStatus.COMPLETED)
        resource_failed = sum(1 for dt in resource_tasks if dt.status == DownloadTaskStatus.FAILED)
        resource_pending = sum(1 for dt in resource_tasks if dt.status in [DownloadTaskStatus.PENDING])

        # 3. 记录详细统计信息
        file_stats = {
            "strm_files_count": strm_files_count,
            "strm_success": strm_success,
            "strm_failed": strm_failed,
            "strm_pending": strm_pending,
            "resource_files_count": resource_files_count,
            "resource_success": resource_success,
            "resource_failed": resource_failed,
            "resource_pending": resource_pending,
        }

        # 4. 计算总数和进度
        total_files = strm_files_count + resource_files_count
        progress = min(
            100, round(((strm_success + strm_failed + resource_success + resource_failed) / total_files) * 100)
        )
        elapsed_time = None
        if task.start_time:
            end_time = task.end_time or datetime.now().astimezone()
            elapsed_seconds = int((end_time - task.start_time).total_seconds())
            minutes, seconds = divmod(elapsed_seconds, 60)
            hours, minutes = divmod(minutes, 60)
            elapsed_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

        # 10. 构建完整的任务状态信息
        result = {
            "id": task.id,
            "name": task.name,
            "status": task.status,
            "total_files": total_files,
            # 进度百分比
            "progress": progress,
            "create_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S") if task.create_time else None,
            "start_time": task.start_time.strftime("%Y-%m-%d %H:%M:%S") if task.start_time else None,
            "end_time": task.end_time.strftime("%Y-%m-%d %H:%M:%S") if task.end_time else None,
            "elapsed_time": elapsed_time,
            "output_dir": task.output_dir,
            **file_stats,
        }

        return result

    except Exception as e:
        # 确保即使出现异常也能返回任务基本状态
        log.error(f"[任务状态] 获取任务 {task_id} 状态时发生异常: {str(e)}")
        # 记录详细错误信息和堆栈跟踪
        log.error(f"[任务状态] 异常堆栈: {traceback.format_exc()}")

        # 返回基本信息
        return {
            "id": task.id,
            "name": task.name,
            "status": task.status,
            "total_files": task.total_files,
            "processed_files": task.processed_files,
            "success_files": task.success_files,
            "failed_files": task.failed_files,
            "progress": 0
            if task.total_files == 0
            else min(100, round((task.processed_files / task.total_files) * 100)),
            "create_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S") if task.create_time else None,
            "start_time": task.start_time.strftime("%Y-%m-%d %H:%M:%S") if task.start_time else None,
            "end_time": task.end_time.strftime("%Y-%m-%d %H:%M:%S") if task.end_time else None,
            "output_dir": task.output_dir,
            "error": f"获取详细状态时发生错误: {str(e)}",
            "strm_files_count": 0,
            "strm_success": 0,
            "strm_failed": 0,
            "resource_files_count": 0,
            "resource_success": 0,
            "resource_failed": 0,
        }


async def get_user_tasks(
    user: User,
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户的任务列表，支持搜索和过滤

    Args:
        user: 当前用户
        page: 页码
        page_size: 每页数量
        search: 按名称搜索
        status: 按状态过滤
        start_date: 开始日期过滤
        end_date: 结束日期过滤

    Returns:
        任务列表
    """
    # 计算偏移量
    offset = (page - 1) * page_size

    # 构建基本查询
    query = StrmTask.filter(created_by=user)

    # 添加搜索条件
    if search:
        query = query.filter(name__icontains=search)

    # 添加状态过滤
    if status:
        # 状态值映射，处理前端传入的状态值
        status_mapping = {
            "SUCCESS": "completed",  # 前端使用SUCCESS，后端是completed
            "CANCELED": "canceled",  # 前端是大写，后端是小写
            "FAILED": "failed",  # 前端是大写，后端是小写
            "PENDING": "pending",  # 前端是大写，后端是小写
            "RUNNING": "running",  # 前端是大写，后端是小写
        }
        # 将前端状态值转换为后端状态值
        backend_status = status_mapping.get(status, status.lower())
        query = query.filter(status=backend_status)

    # 添加日期范围过滤
    if start_date:
        # 将日期字符串转换为datetime对象
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query = query.filter(create_time__gte=start_datetime)

    if end_date:
        # 将日期字符串转换为datetime对象，并设置为当天的结束时间
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
        query = query.filter(create_time__lte=end_datetime)

    # 查询任务并计算总数
    tasks = await query.offset(offset).limit(page_size).order_by("-create_time")
    total = await query.count()

    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "tasks": [
            {
                "id": t.id,
                "name": t.name,
                "status": t.status,
                "total_files": t.total_files,
                "processed_files": t.processed_files,
                "success_files": t.success_files,
                "failed_files": t.failed_files,
                "progress": min(100, int(t.processed_files * 100 / t.total_files)) if t.total_files > 0 else 0,
                "start_time": t.start_time.isoformat() if t.start_time else None,
                "end_time": t.end_time.isoformat() if t.end_time else None,
            }
            for t in tasks
        ],
    }


async def delete_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    删除任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        删除结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(code=404, msg=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(code=403, msg="没有权限删除此任务")

    # 获取任务关联的文件记录
    task_files = await StrmFile.filter(task_id=task_id)

    # 删除任务及关联的文件记录
    await StrmFile.filter(task_id=task_id).delete()
    await task.delete()

    return {"success": True, "message": "任务删除成功", "task_id": task_id}


async def process_strm_task(task_id: int):
    """
    处理STRM任务，创建下载任务记录并启动处理

    Args:
        task_id: STRM任务ID
    """
    from app.log.log import log

    try:
        # 获取任务信息
        task = await StrmTask.get_or_none(id=task_id)
        if not task:
            log.error(f"找不到ID为{task_id}的任务")
            return

        # 获取解析结果
        record_id = int(task.source_file)
        parse_result = await get_parse_result(record_id, task.created_by, all_files=True)
        if not parse_result or not parse_result.get("files"):
            log.error(f"任务 {task_id} 无法获取解析结果或文件列表为空")
            task.status = TaskStatus.FAILED
            task.log_content = "无法获取解析结果或文件列表为空"
            await task.save()
            return

        # 更新任务状态为运行中
        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()
        await task.save()

        # 处理文件，创建下载任务
        files = parse_result.get("files", [])
        created_task_count = 0

        for file in files:
            # 确定处理类型
            file_type_str = file.get("file_type", "other")
            try:
                file_type = FileType(file_type_str)
            except ValueError:
                file_type = FileType.OTHER

            # 确定是生成STRM还是下载资源
            process_type = ProcessType.STRM_GENERATION if file_type == FileType.VIDEO else ProcessType.RESOURCE_DOWNLOAD

            # 创建下载任务记录
            await DownloadTask.create(
                task=task,
                source_path=file.get("path", ""),
                file_type=file_type,
                process_type=process_type,
                status=DownloadTaskStatus.PENDING,
                file_size=file.get("size", 0),
            )
            created_task_count += 1

        # 更新任务总文件数
        task.total_files = created_task_count
        await task.save()

        log.info(f"任务 {task_id} 已创建 {created_task_count} 个下载任务")

        # 调用处理函数开始实际处理
        await process_directory_tree(
            server_id=task.server_id,
            files=files,
            output_dir=task.output_dir,
            task_id=task.id,
            download_server_id=task.download_server_id if task.download_server else None,
            threads=task.threads,
        )

    except Exception as e:
        log.error(f"处理STRM任务 {task_id} 时发生错误: {str(e)}")
        log.error(traceback.format_exc())

        # 更新任务状态为失败
        try:
            if task:
                task.status = TaskStatus.FAILED
                task.log_content = f"处理任务时发生错误: {str(e)}"
                await task.save()
        except Exception as save_error:
            log.error(f"更新任务状态失败: {str(save_error)}")
