"""
媒体服务器控制器，用于管理媒体服务器配置
"""
from typing import List, Optional, Dict, Any

from app.models.strm import MediaServer
from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID


class ServerController(CRUDBase):
    """媒体服务器控制器"""
    
    async def create_server(self, data: Dict[str, Any]) -> MediaServer:
        """
        创建媒体服务器
        
        Args:
            data: 服务器数据
            
        Returns:
            创建的服务器对象
        """
        # 获取当前用户ID
        user_id = CTX_USER_ID.get()
        
        # 如果设置为默认服务器，则将其他服务器设为非默认
        if data.get("is_default"):
            await MediaServer.filter(is_default=True).update(is_default=False)
        
        # 创建服务器
        server = await MediaServer.create(
            **data,
            created_by_id=user_id
        )
        
        return server
    
    async def update_server(self, server_id: int, data: Dict[str, Any]) -> MediaServer:
        """
        更新媒体服务器
        
        Args:
            server_id: 服务器ID
            data: 更新数据
            
        Returns:
            更新后的服务器对象
        """
        server = await self.get(id=server_id)
        
        # 如果设置为默认服务器，则将其他服务器设为非默认
        if data.get("is_default"):
            await MediaServer.filter(is_default=True).exclude(id=server_id).update(is_default=False)
        
        # 更新服务器
        for field, value in data.items():
            setattr(server, field, value)
        
        await server.save()
        
        return server
    
    async def get_default_server(self) -> Optional[MediaServer]:
        """
        获取默认媒体服务器
        
        Returns:
            默认服务器对象，如果没有则返回None
        """
        return await MediaServer.filter(is_default=True).first()
    
    async def test_connection(self, server_id: int) -> Dict[str, Any]:
        """
        测试服务器连接
        
        Args:
            server_id: 服务器ID
            
        Returns:
            测试结果
        """
        server = await self.get(id=server_id)
        
        # TODO: 实现实际的连接测试逻辑
        
        return {
            "success": True,
            "message": "连接成功",
            "server": server
        }


# 创建服务器控制器实例
server_controller = ServerController(MediaServer) 