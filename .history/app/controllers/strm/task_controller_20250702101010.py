"""
STRM任务控制器，用于管理STRM文件生成任务

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

import os
import shutil
import tempfile
import zipfile
import json
import asyncio
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, AsyncGenerator
import re

from fastapi import HTTPException
from fastapi.responses import FileResponse

from app.models.strm import StrmTask, StrmFile, MediaServer, TaskStatus, SystemSettings, FileType
from app.models.system import User
from app.utils.strm.processor import StrmProcessor, process_directory_tree
from app.controllers.strm.upload import get_parse_result
from app.settings import APP_SETTINGS

# 创建日志记录器
logger = logging.getLogger("strm_task_controller")


async def create_strm_task(
    record_id: int,
    server_id: int,
    user: User,
    output_dir: Optional[str] = None,
    custom_name: Optional[str] = None,
    download_server_id: Optional[int] = None,
    download_resources: bool = False,
) -> StrmTask:
    """
    创建STRM文件生成任务

    Args:
        record_id: 上传记录ID
        server_id: 媒体服务器ID
        user: 当前用户
        output_dir: 自定义输出目录，默认为临时目录
        custom_name: 自定义任务名称
        download_server_id: 下载服务器ID，默认为None（使用与媒体服务器相同的服务器）
        download_resources: 是否下载资源文件，默认为False

    Returns:
        创建的任务对象
    """
    # 仅执行基本验证检查，其他操作推迟到后台任务
    # 检查媒体服务器是否存在
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise HTTPException(status_code=404, detail=f"找不到ID为{server_id}的媒体服务器")

    # 检查下载服务器是否存在（如果提供）
    if download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)
        if not download_server:
            raise HTTPException(status_code=404, detail=f"找不到ID为{download_server_id}的下载服务器")

    # 确定输出目录 - 简化处理，详细设置推迟到后台任务中
    if not output_dir:
        # 从系统设置中获取默认输出目录
        settings = await SystemSettings.all().first()
        base_output_dir = settings.output_directory if settings and settings.output_directory else "strm_output"

        # 确保基础输出目录是Path对象
        base_output_dir = Path(base_output_dir)

        # 创建带有时间戳和用户ID的任务输出目录
        task_dir_name = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user.id}"
        output_dir = str(base_output_dir / task_dir_name)

    # 创建任务名称
    if not custom_name:
        custom_name = f"STRM生成-记录{record_id}-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    # 创建任务记录 - 使用较少的初始参数，其他信息在后台任务中填充
    task = await StrmTask.create(
        name=custom_name,
        server=server,  # 媒体服务器
        source_file=str(record_id),  # 存储上传记录ID
        output_dir=output_dir,
        total_files=0,  # 初始化为0，后续更新
        status=TaskStatus.PENDING,
        created_by=user,
    )

    # 如果提供了下载服务器ID或需要下载资源文件，更新日志字段存储额外信息
    log_info = {}
    if download_server_id:
        log_info["download_server_id"] = download_server_id
    if download_resources:
        log_info["download_resources"] = "true"

    if log_info:
        task.log_file = repr(log_info)
        await task.save()

    return task


async def start_strm_task(task_id: int, user_id: int) -> Dict[str, Any]:
    """
    启动STRM文件生成任务

    Args:
        task_id: 任务ID
        user_id: 当前用户ID

    Returns:
        任务启动结果
    """
    # 添加日志表示该函数已被调用
    logger.info(f"开始执行任务 {task_id}, 用户ID: {user_id}")

    # 获取用户
    try:
        user = await User.get_or_none(id=user_id)
        if not user:
            logger.error(f"错误: 找不到ID为{user_id}的用户")
            return {"success": False, "message": f"找不到ID为{user_id}的用户"}

        # 获取任务
        task = await StrmTask.get_or_none(id=task_id)
        if not task:
            logger.error(f"错误: 找不到ID为{task_id}的任务")
            return {"success": False, "message": f"找不到ID为{task_id}的任务"}

        # 检查权限
        if task.created_by_id != user.id:
            logger.error(f"错误: 用户{user.id}没有权限操作任务{task_id}")
            return {"success": False, "message": "没有权限操作此任务"}

        # 检查任务状态
        if task.status == TaskStatus.RUNNING:
            return {"success": False, "message": "任务已在运行中"}

        # 获取上传记录ID和服务器
        record_id = int(task.source_file)
        server = await MediaServer.get(id=task.server_id)

        # 确保输出目录存在
        os.makedirs(task.output_dir, exist_ok=True)

        # 检查是否有下载服务器和资源文件下载选项
        download_server_id = None
        download_resources = False

        # 解析log_file字段中的额外信息
        if task.log_file:
            try:
                # 尝试将log_file解析为Python字典
                log_info = eval(task.log_file)
                if isinstance(log_info, dict):
                    # 获取下载服务器ID
                    if "download_server_id" in log_info:
                        download_server_id = log_info["download_server_id"]

                    # 获取是否下载资源文件
                    if "download_resources" in log_info and log_info["download_resources"] == "true":
                        download_resources = True
            except (SyntaxError, ValueError, TypeError) as e:
                # 兼容旧版本格式（例如 "download_server_id:123"）
                if task.log_file.startswith("download_server_id:"):
                    try:
                        download_server_id = int(task.log_file.split(":")[-1])
                    except ValueError:
                        pass

        # 如果有下载服务器ID，获取下载服务器
        download_server = None
        if download_server_id:
            download_server = await MediaServer.get_or_none(id=download_server_id)

        # 获取解析结果
        parse_result = await get_parse_result(record_id, user, "all", 1, 10, True)
        if not parse_result:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.log_content = f"找不到ID为{record_id}的文件解析结果"
            await task.save()
            return {"success": False, "message": f"找不到ID为{record_id}的文件解析结果"}

        # 获取视频文件和资源文件（如需下载）
        files = []
        if download_resources:
            # 如果启用了下载资源文件，包括所有类型的文件
            files = parse_result.get("parsed_files", [])
            # 更新任务总文件数 - 所有文件（包括视频和资源文件）
            task.total_files = len(files)
        else:
            # 只处理视频文件
            files = [f for f in parse_result.get("parsed_files", []) if f["file_type"] == "video"]
            # 更新任务总文件数 - 只有视频文件
            task.total_files = parse_result.get("stats", {}).get("video", 0)

        await task.save()

        # 更新任务状态
        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()
        await task.save()

        # 启动处理过程
        try:
            # 使用下载服务器ID（如果有）
            process_server_id = download_server.id if download_server else server.id
            result = await process_directory_tree(
                server_id=process_server_id,
                files=files,
                output_dir=task.output_dir,
                task_id=task.id,
                download_resources=download_resources,
                download_server_id=download_server.id if download_server else None,
            )

            return {"success": True, "message": "任务启动成功", "task_id": task.id, "result": result}
        except Exception as e:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.log_content = f"任务处理失败: {str(e)}"
            await task.save()
            logger.error(f"任务{task_id}处理失败: {str(e)}")
            return {"success": False, "message": f"任务处理失败: {str(e)}"}
    except Exception as e:
        # 捕获所有异常，确保后台任务不会中断
        logger.error(f"启动任务{task_id}时发生意外错误: {str(e)}")
        # 尝试更新任务状态
        try:
            task = await StrmTask.get_or_none(id=task_id)
            if task:
                task.status = TaskStatus.FAILED
                task.log_content = f"任务执行发生意外错误: {str(e)}"
                await task.save()
        except Exception as inner_e:
            logger.error(f"更新任务{task_id}状态失败: {str(inner_e)}")
        return {"success": False, "message": f"启动任务时发生错误: {str(e)}"}


async def get_task_status(task_id: int, user: User) -> Dict[str, Any]:
    """
    获取任务状态

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        包含任务状态信息的字典
    """
    # 验证任务存在并属于当前用户
    task = await StrmTask.get_or_none(id=task_id, created_by=user)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务或无权访问")

    from app.log.log import log
    from app.models.strm import DownloadTask, DownloadTaskStatus

    log.debug(f"[调试日志] 获取任务 {task_id} 状态")
    log.debug(f"[调试日志] 任务状态: {task.status}, 数据库记录总文件数: {task.total_files}")

    # --- 新的统计逻辑 ---
    # 完全依赖 DownloadTask 表进行统计

    # 统计STRM文件
    strm_files_count = await DownloadTask.filter(task_id=task.id, file_type="strm").count()
    strm_success = await DownloadTask.filter(
        task_id=task.id, file_type="strm", status=DownloadTaskStatus.COMPLETED
    ).count()
    strm_failed = await DownloadTask.filter(task_id=task.id, file_type="strm", status=DownloadTaskStatus.FAILED).count()

    # 统计资源文件
    resource_files_count = await DownloadTask.filter(task_id=task.id).exclude(file_type="strm").count()
    resource_success = (
        await DownloadTask.filter(task_id=task.id, status=DownloadTaskStatus.COMPLETED)
        .exclude(file_type="strm")
        .count()
    )
    resource_failed = (
        await DownloadTask.filter(task_id=task.id, status=DownloadTaskStatus.FAILED).exclude(file_type="strm").count()
    )

    file_stats = {
        "strm_files_count": strm_files_count,
        "strm_success": strm_success,
        "strm_failed": strm_failed,
        "resource_files_count": resource_files_count,
        "resource_success": resource_success,
        "resource_failed": resource_failed,
    }
    log.debug(f"[调试日志] 文件统计: {file_stats}")

    # 总文件数是两者的和
    total_files = strm_files_count + resource_files_count

    # 任务的核心进度由STRM文件的生成状态决定
    processed_files = strm_success + strm_failed
    success_files = strm_success
    failed_files = strm_failed
    log.debug(f"[调试日志] 已处理文件数: {processed_files}, 成功: {success_files}, 失败: {failed_files}")

    # 计算进度百分比
    progress = 0
    # 使用 strm_files_count 作为进度计算的基数
    if strm_files_count > 0:
        progress = min(100, round((processed_files / strm_files_count) * 100))
    elif task.status == TaskStatus.COMPLETED:
        progress = 100

    # 如果任务已完成但进度不是100%，修正为100%
    if task.status in [TaskStatus.COMPLETED, TaskStatus.CANCELED] and progress < 100:
        # 如果已处理文件数大于0，说明有进度
        if processed_files > 0:
            pass  # 保持实际进度
        else:
            progress = 100

    # 计算任务运行时长
    elapsed_time = None
    if task.start_time:
        end_time = task.end_time or datetime.now().astimezone()
        elapsed_seconds = int((end_time - task.start_time).total_seconds())
        minutes, seconds = divmod(elapsed_seconds, 60)
        hours, minutes = divmod(minutes, 60)
        elapsed_time = f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    # 检查任务失败状态
    error_message = None
    if task.status == TaskStatus.FAILED:
        error_message = task.log_content
        log.error(f"[错误] 任务 {task.id} 失败: {error_message}")

    # 如果数据库中记录的总数与计算得到的总数不符，更新它
    if task.total_files != total_files:
        log.info(f"[调试日志] 任务 {task.id} 的总文件数记录不一致，从 {task.total_files} 更新为 {total_files}")
        task.total_files = total_files
        # 同时更新处理和成功计数
        task.processed_files = processed_files
        task.success_files = success_files
        task.failed_files = failed_files
        await task.save()

    # 构建完整的任务状态信息
    result = {
        "id": task.id,
        "name": task.name,
        "status": task.status,
        "total_files": total_files,
        "processed_files": processed_files,
        "success_files": success_files,
        "failed_files": failed_files,
        "progress": progress,
        "create_time": task.create_time.strftime("%Y-%m-%d %H:%M:%S") if task.create_time else None,
        "start_time": task.start_time.strftime("%Y-%m-%d %H:%M:%S") if task.start_time else None,
        "end_time": task.end_time.strftime("%Y-%m-%d %H:%M:%S") if task.end_time else None,
        "elapsed_time": elapsed_time,
        "output_dir": task.output_dir,
        "error": error_message,
        **file_stats,
    }

    log.debug(f"[调试日志] 返回任务状态: {result}")
    return result


async def get_user_tasks(
    user: User,
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户的任务列表，支持搜索和过滤

    Args:
        user: 当前用户
        page: 页码
        page_size: 每页数量
        search: 按名称搜索
        status: 按状态过滤
        start_date: 开始日期过滤
        end_date: 结束日期过滤

    Returns:
        任务列表
    """
    # 计算偏移量
    offset = (page - 1) * page_size

    # 构建基本查询
    query = StrmTask.filter(created_by=user)

    # 添加搜索条件
    if search:
        query = query.filter(name__icontains=search)

    # 添加状态过滤
    if status:
        # 状态值映射，处理前端传入的状态值
        status_mapping = {
            "SUCCESS": "completed",  # 前端使用SUCCESS，后端是completed
            "CANCELED": "canceled",  # 前端是大写，后端是小写
            "FAILED": "failed",  # 前端是大写，后端是小写
            "PENDING": "pending",  # 前端是大写，后端是小写
            "RUNNING": "running",  # 前端是大写，后端是小写
        }
        # 将前端状态值转换为后端状态值
        backend_status = status_mapping.get(status, status.lower())
        query = query.filter(status=backend_status)

    # 添加日期范围过滤
    if start_date:
        # 将日期字符串转换为datetime对象
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query = query.filter(create_time__gte=start_datetime)

    if end_date:
        # 将日期字符串转换为datetime对象，并设置为当天的结束时间
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
        query = query.filter(create_time__lte=end_datetime)

    # 查询任务并计算总数
    tasks = await query.offset(offset).limit(page_size).order_by("-create_time")
    total = await query.count()

    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "tasks": [
            {
                "id": t.id,
                "name": t.name,
                "status": t.status,
                "total_files": t.total_files,
                "processed_files": t.processed_files,
                "success_files": t.success_files,
                "failed_files": t.failed_files,
                "progress": min(100, int(t.processed_files * 100 / t.total_files)) if t.total_files > 0 else 0,
                "start_time": t.start_time.isoformat() if t.start_time else None,
                "end_time": t.end_time.isoformat() if t.end_time else None,
            }
            for t in tasks
        ],
    }


async def download_strm_files(task_id: int, user: User) -> FileResponse:
    """
    下载生成的STRM文件包

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        文件响应对象
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限下载此任务的文件")

    # 检查任务状态
    if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        raise HTTPException(status_code=400, detail="任务尚未完成，无法下载")

    # 检查输出目录是否存在
    output_dir = Path(task.output_dir)
    if not output_dir.exists():
        raise HTTPException(status_code=404, detail="输出目录不存在，可能已被删除")

    # 创建ZIP文件
    zip_file_name = f"strm_files_{task_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
    zip_path = STRM_OUTPUT_DIR / zip_file_name

    try:
        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, output_dir)
                        zipf.write(file_path, arcname)

        # 返回文件响应
        return FileResponse(zip_path, media_type="application/zip", filename=zip_file_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建ZIP文件失败: {str(e)}")


async def cancel_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    取消任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        操作结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务状态
    if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
        return {"success": False, "message": "任务已完成或已取消，无法取消"}

    # 更新任务状态
    task.status = TaskStatus.CANCELED
    task.end_time = datetime.now()
    await task.save()

    return {"success": True, "message": "任务已取消"}


async def delete_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    删除任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        删除结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此任务")

    # 获取任务关联的文件记录
    task_files = await StrmFile.filter(task_id=task_id)

    # 删除任务及关联的文件记录
    await StrmFile.filter(task_id=task_id).delete()
    await task.delete()

    return {"success": True, "message": "任务删除成功", "task_id": task_id}


async def get_task_logs(
    task_id: int,
    user: User,
    page: int = 1,
    page_size: int = 50,
    level: Optional[str] = None,
    search: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取任务日志

    Args:
        task_id: 任务ID
        user: 当前用户
        page: 页码
        page_size: 每页数量
        level: 日志级别过滤
        search: 日志内容搜索

    Returns:
        任务日志文本
    """
    from app.log.log import log

    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此任务日志")

    # 获取日志数据
    all_log_lines = []

    # 从任务日志内容获取日志
    if task.log_content:
        # 获取原始日志内容
        raw_content = task.log_content
        # 按行分割并解析日志
        task_log_lines = raw_content.strip().split("\n")

        # 将任务日志行添加到总日志列表
        for line in task_log_lines:
            log_entry = parse_log_entry(line)
            if log_entry:
                # 添加来源标记
                log_entry["source"] = "task"
                all_log_lines.append(log_entry)

    # 确保所有日志条目都有可用于排序的时间戳
    for entry in all_log_lines:
        # 确保时间戳字段存在且不为None
        if entry.get("timestamp") is None or entry.get("timestamp") == "":
            # 尝试从消息中再次提取时间戳，如果还是失败，使用空字符串
            entry["timestamp"] = ""

    # 按时间戳排序所有日志，确保处理None和空字符串值
    # 正序排列（最早的在前面）
    all_log_lines.sort(key=lambda x: x.get("timestamp", "") or "")

    # 过滤日志
    filtered_logs = all_log_lines
    if level or search:
        filtered_logs = []
        for log_entry in all_log_lines:
            if level and log_entry.get("level", "").upper() != level.upper():
                continue
            if search:
                # 在消息、文件路径和错误信息中搜索
                search_text = f"{log_entry.get('message', '')} {log_entry.get('file_path', '')} {log_entry.get('error_message', '')}"
                if search.lower() not in search_text.lower():
                    continue
            filtered_logs.append(log_entry)

    # 返回所有日志，不进行分页
    total_logs = len(filtered_logs)

    # 生成原始日志文本
    raw_content_lines = []
    for log_entry in filtered_logs:
        timestamp = log_entry.get("timestamp", "")
        level = log_entry.get("level", "INFO")
        message = log_entry.get("message", "")

        # 构建基本日志行
        log_line = f"[{timestamp}] [{level}] {message}"

        # 添加文件路径信息（如果有）
        if log_entry.get("file_path"):
            log_line += f" | 文件: {log_entry.get('file_path')}"

        # 添加目标路径信息（如果有）
        if log_entry.get("target_path"):
            log_line += f" | 目标: {log_entry.get('target_path')}"

        # 添加错误信息（如果有）
        if log_entry.get("error_message"):
            log_line += f" | 错误: {log_entry.get('error_message')}"

        raw_content_lines.append(log_line)

    return {
        "raw_content": "\n".join(raw_content_lines),  # 原始日志文本
        "total": total_logs,
    }


def parse_log_entry(log_line: str) -> Optional[Dict[str, Any]]:
    """
    解析日志行，提取时间戳和消息

    Args:
        log_line: 原始日志行

    Returns:
        解析后的日志条目字典，如果解析失败则返回None
    """
    # 日志格式示例：[2023-04-15 14:30:45] [INFO] 处理文件: movie.mp4
    timestamp_pattern = r"\[([\d\- :]+)\]"
    level_pattern = r"\[(INFO|ERROR|WARNING|DEBUG)\]"
    # 新增：ISO格式时间戳正则表达式 [2025-06-26T14:51:56.619824]
    iso_timestamp_pattern = r"\[([\d\-]+T[\d:\.]+)\]"

    # 尝试提取时间戳
    timestamp_match = re.search(timestamp_pattern, log_line)
    timestamp = timestamp_match.group(1) if timestamp_match else None

    # 尝试提取日志级别
    level_match = re.search(level_pattern, log_line)
    level = level_match.group(1) if level_match else "INFO"

    # 提取消息内容 (移除时间戳和日志级别部分)
    message = log_line
    if timestamp_match:
        message = message.replace(timestamp_match.group(0), "", 1).strip()
    if level_match:
        message = message.replace(level_match.group(0), "", 1).strip()

    if not message:
        return None

    # 尝试从消息中提取ISO格式的时间戳
    iso_timestamp = None
    iso_match = re.search(iso_timestamp_pattern, message)
    if iso_match:
        iso_timestamp = iso_match.group(1)
        # 不从消息中删除ISO时间戳，保留完整消息

    # 优先使用外部时间戳，如果没有则使用消息中的ISO时间戳
    result_timestamp = timestamp if timestamp else iso_timestamp

    return {"timestamp": result_timestamp, "level": level, "message": message}


async def restart_stuck_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    重启卡住的任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        重启结果
    """
    # 检查任务是否存在
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务是否真的卡住
    if task.status not in [TaskStatus.RUNNING, TaskStatus.PENDING]:
        return {"success": False, "message": f"任务状态为 {task.status}, 无需重启"}

    # 重置任务状态
    task.status = TaskStatus.PENDING
    task.start_time = None
    task.end_time = None
    task.log_content = f"任务由用户 {user.user_name} 重启"
    await task.save()

    # 添加后台任务以重新启动它
    from app.core.bgtask import BgTasks

    await BgTasks.add_task(start_strm_task, task_id=task.id, user_id=user.id)

    return {"success": True, "message": "任务已重新启动"}
