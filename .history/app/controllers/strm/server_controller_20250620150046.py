"""
媒体服务器控制器，用于管理媒体服务器配置
"""

import httpx
from typing import List, Optional, Dict, Any
import asyncio

from app.models.strm import MediaServer, ServerStatus
from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID
from app.log.log import log


class ServerController(CRUDBase):
    """媒体服务器控制器"""

    async def create_server(self, data: Dict[str, Any]) -> MediaServer:
        """
        创建媒体服务器

        Args:
            data: 服务器数据

        Returns:
            创建的服务器对象
        """
        # 获取当前用户ID
        user_id = CTX_USER_ID.get()

        # 如果没有提供状态，设置为未知
        if "status" not in data:
            data["status"] = ServerStatus.UNKNOWN

        # 创建服务器
        server = await MediaServer.create(**data, created_by_id=user_id)

        return server

    async def update_server(self, server_id: int, data: Dict[str, Any]) -> MediaServer:
        """
        更新媒体服务器

        Args:
            server_id: 服务器ID
            data: 更新数据

        Returns:
            更新后的服务器对象
        """
        server = await self.get(id=server_id)

        # 更新服务器
        for field, value in data.items():
            setattr(server, field, value)

        await server.save()

        return server

    async def get_default_server(self) -> Optional[MediaServer]:
        """
        获取默认媒体服务器

        注意: 此方法已废弃，保留是为了兼容现有代码，将始终返回None
        后续版本可能会完全移除此方法

        Returns:
            始终返回None
        """
        log.warning("调用已废弃的get_default_server方法，此方法将在未来版本中移除")
        return None

    async def test_connection_without_save(self, server_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        测试未保存的服务器连接

        Args:
            server_data: 服务器数据

        Returns:
            测试结果
        """
        # 实现实际的连接测试逻辑
        success = False
        message = "未知错误"
        status = "error"

        try:
            # 获取服务器类型和URL
            server_type = str(server_data.get("server_type", "http")).lower()
            base_url = server_data.get("base_url", "")
            auth_required = server_data.get("auth_required", False)
            username = server_data.get("username")
            password = server_data.get("password")

            # 根据服务器类型进行不同的连接测试
            if server_type in ["http", "https"]:
                # 对HTTP/HTTPS服务器进行连接测试
                url = base_url
                if not url.startswith(("http://", "https://")):
                    url = f"{'https://' if server_type == 'https' else 'http://'}{url}"

                # 设置HTTP客户端
                timeout = 10.0  # 10秒超时
                headers = {}
                auth = None

                # 如果需要认证
                if auth_required and username:
                    auth = (username, password or "")

                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(url, headers=headers, auth=auth)
                    response.raise_for_status()

                    success = True
                    message = f"连接成功 (HTTP {response.status_code})"
                    status = "success"

            elif server_type in ["cd2host", "xiaoyahost"]:
                # 对特殊服务器类型进行连接测试
                # 这里可以根据具体的服务器类型实现不同的测试逻辑
                # 暂时模拟成功
                await asyncio.sleep(1)  # 模拟测试延迟
                success = True
                message = f"连接成功 ({server_type})"
                status = "success"

            else:
                message = f"不支持的服务器类型: {server_type}"
                status = "warning"

        except httpx.HTTPStatusError as e:
            message = f"HTTP错误 {e.response.status_code}: {e.response.reason_phrase}"
            status = "error"
        except httpx.RequestError as e:
            message = f"请求错误: {str(e)}"
            status = "error"
        except httpx.TimeoutException:
            message = "连接超时"
            status = "error"
        except Exception as e:
            message = f"连接错误: {str(e)}"
            status = "error"

        return {
            "success": success,
            "message": message,
            "status": status,
            "server": {
                "name": server_data.get("name", "未命名服务器"),
                "server_type": server_type,
                "base_url": base_url,
                "description": server_data.get("description"),
                "auth_required": auth_required,
            },
        }

    async def test_connection(self, server_id: int) -> Dict[str, Any]:
        """
        测试服务器连接

        Args:
            server_id: 服务器ID

        Returns:
            测试结果
        """
        server = await self.get(id=server_id)

        # 实现实际的连接测试逻辑
        success = False
        message = "未知错误"
        status = "error"

        try:
            # 获取服务器类型的字符串值（确保是小写）
            server_type_str = str(server.server_type).lower()

            # 根据服务器类型进行不同的连接测试
            if server_type_str in ["http", "https"]:
                # 对HTTP/HTTPS服务器进行连接测试
                url = server.base_url
                if not url.startswith(("http://", "https://")):
                    url = f"{'https://' if server_type_str == 'https' else 'http://'}{url}"

                # 设置HTTP客户端
                timeout = 10.0  # 10秒超时
                headers = {}
                auth = None

                # 如果需要认证
                if server.auth_required and server.username:
                    auth = (server.username, server.password or "")

                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.get(url, headers=headers, auth=auth)
                    response.raise_for_status()

                    success = True
                    message = f"连接成功 (HTTP {response.status_code})"
                    status = "success"

            elif server_type_str in ["cd2host", "xiaoyahost"]:
                # 对特殊服务器类型进行连接测试
                # 这里可以根据具体的服务器类型实现不同的测试逻辑
                # 暂时模拟成功
                await asyncio.sleep(1)  # 模拟测试延迟
                success = True
                message = f"连接成功 ({server_type_str})"
                status = "success"

            else:
                message = f"不支持的服务器类型: {server_type_str}"
                status = "warning"

        except httpx.HTTPStatusError as e:
            message = f"HTTP错误 {e.response.status_code}: {e.response.reason_phrase}"
            status = "error"
        except httpx.RequestError as e:
            message = f"请求错误: {str(e)}"
            status = "error"
        except httpx.TimeoutException:
            message = "连接超时"
            status = "error"
        except Exception as e:
            message = f"连接错误: {str(e)}"
            status = "error"

        # 更新服务器状态并保存到数据库
        server.status = status
        await server.save()

        return {
            "success": success,
            "message": message,
            "status": status,
            "server": {
                "id": server.id,
                "name": server.name,
                "server_type": server.server_type,
                "base_url": server.base_url,
                "description": server.description,
                "auth_required": server.auth_required,
                "status": status,
            },
        }


# 创建服务器控制器实例
server_controller = ServerController(MediaServer)
