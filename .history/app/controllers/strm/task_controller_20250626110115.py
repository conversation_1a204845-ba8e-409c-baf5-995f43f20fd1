"""
STRM任务控制器，用于管理STRM文件生成任务

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

import os
import shutil
import tempfile
import zipfile
import json
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple, AsyncGenerator
import re

from fastapi import HTTPException
from fastapi.responses import FileResponse

from app.models.strm import StrmTask, StrmFile, MediaServer, TaskStatus, SystemSettings, DownloadLog
from app.models.system import User
from app.utils.strm.processor import StrmProcessor, process_directory_tree
from app.controllers.strm.upload import get_parse_result
from app.settings import APP_SETTINGS

# 定义STRM文件生成的临时目录
STRM_OUTPUT_DIR = Path(APP_SETTINGS.BASE_DIR) / "strm_output"
STRM_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


async def create_strm_task(
    record_id: int,
    server_id: int,
    user: User,
    output_dir: Optional[str] = None,
    custom_name: Optional[str] = None,
    download_server_id: Optional[int] = None,
    download_resources: bool = False,
) -> StrmTask:
    """
    创建STRM文件生成任务

    Args:
        record_id: 上传记录ID
        server_id: 媒体服务器ID
        user: 当前用户
        output_dir: 自定义输出目录，默认为临时目录
        custom_name: 自定义任务名称
        download_server_id: 下载服务器ID，默认为None（使用与媒体服务器相同的服务器）
        download_resources: 是否下载资源文件，默认为False

    Returns:
        创建的任务对象
    """
    # 仅执行基本验证检查，其他操作推迟到后台任务
    # 检查媒体服务器是否存在
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise HTTPException(status_code=404, detail=f"找不到ID为{server_id}的媒体服务器")

    # 检查下载服务器是否存在（如果提供）
    if download_server_id:
        download_server = await MediaServer.get_or_none(id=download_server_id)
        if not download_server:
            raise HTTPException(status_code=404, detail=f"找不到ID为{download_server_id}的下载服务器")

    # 确定输出目录 - 简化处理，详细设置推迟到后台任务中
    if not output_dir:
        # 使用默认的临时目录
        output_dir = str(STRM_OUTPUT_DIR / f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user.id}")

    # 创建任务名称
    if not custom_name:
        custom_name = f"STRM生成-记录{record_id}-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    # 创建任务记录 - 使用较少的初始参数，其他信息在后台任务中填充
    task = await StrmTask.create(
        name=custom_name,
        server=server,  # 媒体服务器
        source_file=str(record_id),  # 存储上传记录ID
        output_dir=output_dir,
        total_files=0,  # 初始化为0，后续更新
        status=TaskStatus.PENDING,
        created_by=user,
    )

    # 如果提供了下载服务器ID或需要下载资源文件，更新日志字段存储额外信息
    log_info = {}
    if download_server_id:
        log_info["download_server_id"] = download_server_id
    if download_resources:
        log_info["download_resources"] = "true"

    if log_info:
        task.log_file = repr(log_info)
        await task.save()

    return task


async def start_strm_task(task_id: int, user_id: int) -> Dict[str, Any]:
    """
    启动STRM文件生成任务

    Args:
        task_id: 任务ID
        user_id: 当前用户ID

    Returns:
        任务启动结果
    """
    # 获取用户
    try:
        user = await User.get_or_none(id=user_id)
        if not user:
            print(f"错误: 找不到ID为{user_id}的用户")
            return {"success": False, "message": f"找不到ID为{user_id}的用户"}

        # 获取任务
        task = await StrmTask.get_or_none(id=task_id)
        if not task:
            print(f"错误: 找不到ID为{task_id}的任务")
            return {"success": False, "message": f"找不到ID为{task_id}的任务"}

        # 检查权限
        if task.created_by_id != user.id:
            print(f"错误: 用户{user.id}没有权限操作任务{task_id}")
            return {"success": False, "message": "没有权限操作此任务"}

        # 检查任务状态
        if task.status == TaskStatus.RUNNING:
            return {"success": False, "message": "任务已在运行中"}

        # 获取上传记录ID和服务器
        record_id = int(task.source_file)
        server = await MediaServer.get(id=task.server_id)

        # 确保输出目录存在
        os.makedirs(task.output_dir, exist_ok=True)

        # 检查是否有下载服务器和资源文件下载选项
        download_server_id = None
        download_resources = False

        # 解析log_file字段中的额外信息
        if task.log_file:
            try:
                # 尝试将log_file解析为Python字典
                log_info = eval(task.log_file)
                if isinstance(log_info, dict):
                    # 获取下载服务器ID
                    if "download_server_id" in log_info:
                        download_server_id = log_info["download_server_id"]

                    # 获取是否下载资源文件
                    if "download_resources" in log_info and log_info["download_resources"] == "true":
                        download_resources = True
            except (SyntaxError, ValueError, TypeError) as e:
                # 兼容旧版本格式（例如 "download_server_id:123"）
                if task.log_file.startswith("download_server_id:"):
                    try:
                        download_server_id = int(task.log_file.split(":")[-1])
                    except ValueError:
                        pass

        # 如果有下载服务器ID，获取下载服务器
        download_server = None
        if download_server_id:
            download_server = await MediaServer.get_or_none(id=download_server_id)

        # 获取解析结果
        parse_result = await get_parse_result(record_id, user, "all", 1, 10, True)
        if not parse_result:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.log_content = f"找不到ID为{record_id}的文件解析结果"
            await task.save()
            return {"success": False, "message": f"找不到ID为{record_id}的文件解析结果"}

        # 更新任务总文件数
        task.total_files = parse_result.get("stats", {}).get("video", 0)
        await task.save()

        # 获取视频文件和资源文件（如需下载）
        files = []
        if download_resources:
            # 如果启用了下载资源文件，包括所有类型的文件
            files = parse_result.get("parsed_files", [])
        else:
            # 只处理视频文件
            files = [f for f in parse_result.get("parsed_files", []) if f["file_type"] == "video"]

        # 更新任务状态
        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()
        await task.save()

        # 启动处理过程
        try:
            # 使用下载服务器ID（如果有）
            process_server_id = download_server.id if download_server else server.id
            result = await process_directory_tree(
                server_id=process_server_id,
                files=files,
                output_dir=task.output_dir,
                task=task,
                download_resources=download_resources,
                download_server_id=download_server.id if download_server else None,
            )

            return {"success": True, "message": "任务启动成功", "task_id": task.id, "result": result}
        except Exception as e:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.log_content = f"任务处理失败: {str(e)}"
            await task.save()
            print(f"任务{task_id}处理失败: {str(e)}")
            return {"success": False, "message": f"任务处理失败: {str(e)}"}
    except Exception as e:
        # 捕获所有异常，确保后台任务不会中断
        print(f"启动任务{task_id}时发生意外错误: {str(e)}")
        # 尝试更新任务状态
        try:
            task = await StrmTask.get_or_none(id=task_id)
            if task:
                task.status = TaskStatus.FAILED
                task.log_content = f"任务执行发生意外错误: {str(e)}"
                await task.save()
        except Exception as inner_e:
            print(f"更新任务{task_id}状态失败: {str(inner_e)}")
        return {"success": False, "message": f"启动任务时发生错误: {str(e)}"}


async def get_task_status(task_id: int, user: User) -> Dict[str, Any]:
    """
    获取任务状态

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        任务状态信息
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此任务")

    # 获取关联的文件记录
    files = await StrmFile.filter(task_id=task_id).all()

    # 计算进度百分比
    progress = 0
    if task.total_files > 0:
        progress = min(100, int(task.processed_files * 100 / task.total_files))

    # 计算资源文件数 (音频、图片、字幕、元数据文件的总和)
    resource_files_count = 0

    # 获取上传记录和解析结果
    try:
        from app.models.strm.upload import UploadRecord

        # 任务的source_file存储的是上传记录ID
        record_id = int(task.source_file)
        record = await UploadRecord.get_or_none(id=record_id)

        if record and record.parsed_result:
            # 从解析结果中获取文件统计信息
            stats = record.parsed_result.get("stats", {})

            # 计算资源文件数
            resource_files_count = (
                stats.get("audio", 0) + stats.get("image", 0) + stats.get("subtitle", 0) + stats.get("metadata", 0)
            )
    except Exception as e:
        # 如果获取失败，记录错误但不影响其他功能
        print(f"计算资源文件数出错: {str(e)}")

    return {
        "id": task.id,
        "name": task.name,
        "status": task.status,
        "total_files": task.total_files,
        "processed_files": task.processed_files,
        "success_files": task.success_files,
        "failed_files": task.failed_files,
        "progress": progress,
        "start_time": task.start_time.isoformat() if task.start_time else None,
        "end_time": task.end_time.isoformat() if task.end_time else None,
        "output_dir": task.output_dir,
        "files": [
            {
                "id": f.id,
                "source_path": f.source_path,
                "target_path": f.target_path,
                "file_type": f.file_type,
                "file_size": f.file_size,
                "is_success": f.is_success,
                "error_message": f.error_message,
            }
            for f in files[:10]  # 只返回前10条记录，避免数据过大
        ],
        "file_count": len(files),
        "resource_files_count": resource_files_count,  # 添加资源文件数
    }


async def get_user_tasks(
    user: User,
    page: int = 1,
    page_size: int = 10,
    search: Optional[str] = None,
    status: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取用户的任务列表，支持搜索和过滤

    Args:
        user: 当前用户
        page: 页码
        page_size: 每页数量
        search: 按名称搜索
        status: 按状态过滤
        start_date: 开始日期过滤
        end_date: 结束日期过滤

    Returns:
        任务列表
    """
    # 计算偏移量
    offset = (page - 1) * page_size

    # 构建基本查询
    query = StrmTask.filter(created_by=user)

    # 添加搜索条件
    if search:
        query = query.filter(name__icontains=search)

    # 添加状态过滤
    if status:
        # 状态值映射，处理前端传入的状态值
        status_mapping = {
            "SUCCESS": "completed",  # 前端使用SUCCESS，后端是completed
            "CANCELED": "canceled",  # 前端是大写，后端是小写
            "FAILED": "failed",  # 前端是大写，后端是小写
            "PENDING": "pending",  # 前端是大写，后端是小写
            "RUNNING": "running",  # 前端是大写，后端是小写
        }
        # 将前端状态值转换为后端状态值
        backend_status = status_mapping.get(status, status.lower())
        query = query.filter(status=backend_status)

    # 添加日期范围过滤
    if start_date:
        # 将日期字符串转换为datetime对象
        start_datetime = datetime.strptime(start_date, "%Y-%m-%d")
        query = query.filter(create_time__gte=start_datetime)

    if end_date:
        # 将日期字符串转换为datetime对象，并设置为当天的结束时间
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
        query = query.filter(create_time__lte=end_datetime)

    # 查询任务并计算总数
    tasks = await query.offset(offset).limit(page_size).order_by("-create_time")
    total = await query.count()

    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "tasks": [
            {
                "id": t.id,
                "name": t.name,
                "status": t.status,
                "total_files": t.total_files,
                "processed_files": t.processed_files,
                "success_files": t.success_files,
                "failed_files": t.failed_files,
                "progress": min(100, int(t.processed_files * 100 / t.total_files)) if t.total_files > 0 else 0,
                "start_time": t.start_time.isoformat() if t.start_time else None,
                "end_time": t.end_time.isoformat() if t.end_time else None,
            }
            for t in tasks
        ],
    }


async def download_strm_files(task_id: int, user: User) -> FileResponse:
    """
    下载生成的STRM文件包

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        文件响应对象
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限下载此任务的文件")

    # 检查任务状态
    if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        raise HTTPException(status_code=400, detail="任务尚未完成，无法下载")

    # 检查输出目录是否存在
    output_dir = Path(task.output_dir)
    if not output_dir.exists():
        raise HTTPException(status_code=404, detail="输出目录不存在，可能已被删除")

    # 创建ZIP文件
    zip_file_name = f"strm_files_{task_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
    zip_path = STRM_OUTPUT_DIR / zip_file_name

    try:
        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, output_dir)
                        zipf.write(file_path, arcname)

        # 返回文件响应
        return FileResponse(zip_path, media_type="application/zip", filename=zip_file_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建ZIP文件失败: {str(e)}")


async def cancel_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    取消任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        操作结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务状态
    if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
        return {"success": False, "message": "任务已完成或已取消，无法取消"}

    # 更新任务状态
    task.status = TaskStatus.CANCELED
    task.end_time = datetime.now()
    await task.save()

    return {"success": True, "message": "任务已取消"}


async def delete_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    删除任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        删除结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此任务")

    # 获取任务关联的文件记录
    task_files = await StrmFile.filter(task_id=task_id)

    # 删除任务及关联的文件记录
    await StrmFile.filter(task_id=task_id).delete()
    await task.delete()

    return {"success": True, "message": "任务删除成功", "task_id": task_id}


async def get_task_logs(
    task_id: int,
    user: User,
    page: int = 1,
    page_size: int = 50,
    level: Optional[str] = None,
    search: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取任务日志

    Args:
        task_id: 任务ID
        user: 当前用户
        page: 页码
        page_size: 每页数量
        level: 日志级别过滤
        search: 日志内容搜索

    Returns:
        任务日志列表
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此任务日志")

    # 检查日志内容是否存在
    if not task.log_content:
        return {"logs": [], "total": 0, "page": page, "page_size": page_size}

    # 获取原始日志内容
    raw_content = task.log_content

    # 按行分割并解析日志
    log_lines = raw_content.strip().split("\n")
    total_logs = len(log_lines)

    # 过滤日志
    if level or search:
        filtered_lines = []
        for line in log_lines:
            if level and f"[{level.upper()}]" not in line:
                continue
            if search and search.lower() not in line.lower():
                continue
            filtered_lines.append(line)
        log_lines = filtered_lines
        total_logs = len(log_lines)

    # 分页
    start_idx = (page - 1) * page_size
    end_idx = min(start_idx + page_size, total_logs)
    paged_lines = log_lines[start_idx:end_idx]

    # 解析日志行
    logs = []
    for line in paged_lines:
        log_entry = parse_log_entry(line)
        if log_entry:
            logs.append(log_entry)

    return {
        "logs": logs,  # 结构化日志数据（向后兼容）
        "raw_content": "\n".join(paged_lines),  # 原始日志文本
        "total": total_logs,
        "page": page,
        "page_size": page_size,
    }


def parse_log_entry(log_line: str) -> Optional[Dict[str, Any]]:
    """
    解析日志行，提取时间戳和消息

    Args:
        log_line: 原始日志行

    Returns:
        解析后的日志条目字典，如果解析失败则返回None
    """
    # 日志格式示例：[2023-04-15 14:30:45] [INFO] 处理文件: movie.mp4
    timestamp_pattern = r"\[([\d\- :]+)\]"
    level_pattern = r"\[(INFO|ERROR|WARNING|DEBUG)\]"

    # 尝试提取时间戳
    timestamp_match = re.search(timestamp_pattern, log_line)
    timestamp = timestamp_match.group(1) if timestamp_match else None

    # 尝试提取日志级别
    level_match = re.search(level_pattern, log_line)
    level = level_match.group(1) if level_match else "INFO"

    # 提取消息内容 (移除时间戳和日志级别部分)
    message = log_line
    if timestamp_match:
        message = message.replace(timestamp_match.group(0), "", 1).strip()
    if level_match:
        message = message.replace(level_match.group(0), "", 1).strip()

    if not timestamp and not message:
        return None

    return {"timestamp": timestamp, "level": level, "message": message}


def has_significant_changes(old_status: Dict[str, Any], new_status: Dict[str, Any]) -> bool:
    """
    检查两个任务状态之间是否有重要变化

    Args:
        old_status: 旧状态
        new_status: 新状态

    Returns:
        如果有重要变化返回True，否则返回False
    """
    # 检查重要字段是否发生变化
    important_fields = ["status", "progress", "processed_files", "success_files", "failed_files"]

    for field in important_fields:
        if field in new_status and field in old_status:
            if new_status[field] != old_status[field]:
                return True

    # 检查是否有状态变化或者处理文件数变化
    if "status" in new_status and "status" in old_status:
        if new_status["status"] != old_status["status"]:
            return True

    # 检查进度变化是否显著 (超过1%)
    if "progress" in new_status and "progress" in old_status:
        if abs(new_status["progress"] - old_status["progress"]) >= 1:
            return True

    return False


async def task_status_event_generator(task_id: int, user_id: int) -> AsyncGenerator[Dict[str, Any], None]:
    """
    为SSE连接生成任务状态事件的异步生成器

    Args:
        task_id: 任务ID
        user_id: 用户ID

    Yields:
        SSE事件数据
    """
    from app.log.log import log

    # SSE初始化日志
    log.info(f"SSE事件流: 初始化任务 {task_id} 的事件生成器, 用户ID: {user_id}")

    # 发送连接建立消息
    yield {
        "event": "connection",
        "data": json.dumps({"status": "established", "timestamp": datetime.now().isoformat()}),
    }

    try:
        # 获取用户对象
        user = await User.get_or_none(id=user_id)
        if not user:
            log.error(f"SSE事件流: 找不到ID为 {user_id} 的用户")
            yield {"event": "error", "data": json.dumps({"error": f"用户验证失败"})}
            return

        # 验证任务是否存在并属于该用户
        task = await StrmTask.get_or_none(id=task_id, created_by_id=user_id)
        if not task:
            log.error(f"SSE事件流: 用户 {user_id} 尝试访问不存在或不属于他的任务 {task_id}")
            yield {"event": "error", "data": json.dumps({"error": "任务不存在或没有访问权限"})}
            return

        # 获取初始任务状态
        try:
            task_status = await get_task_status(task_id, user)
            log.info(f"SSE事件流: 用户 {user_id} 建立了任务 {task_id} 的连接, 初始状态: {task_status['status']}")
        except Exception as e:
            log.error(f"SSE事件流: 获取任务 {task_id} 初始状态失败: {str(e)}")
            yield {"event": "error", "data": json.dumps({"error": f"获取任务状态失败: {str(e)}"})}
            return

        # 发送初始状态
        yield {"event": "initial", "data": json.dumps(task_status)}

        # 存储上一次发送的状态，用于比较变化
        last_status = task_status.copy()

        # 轮询参数设置
        poll_interval = 1.0  # 初始轮询间隔(秒)
        heartbeat_interval = 15.0  # 心跳间隔(秒)
        last_heartbeat = datetime.now()
        event_count = 0  # 事件计数器，用于诊断

        # 持续监控状态变化
        while True:
            try:
                # 读取最新状态
                current_status = await get_task_status(task_id, user)

                # 检查是否有重要变化
                if has_significant_changes(last_status, current_status):
                    log.debug(
                        f"SSE事件流: 任务 {task_id} 状态变化: {current_status.get('status', 'unknown')}, 进度: {current_status.get('progress', 0)}%"
                    )
                    event_count += 1
                    yield {"event": "update", "data": json.dumps(current_status)}
                    last_status = current_status.copy()

                    # 状态更新时重置轮询间隔
                    poll_interval = 1.0

                # 检查是否需要发送心跳
                now = datetime.now()
                if (now - last_heartbeat).total_seconds() >= heartbeat_interval:
                    log.debug(f"SSE事件流: 发送心跳, 任务 {task_id}, 已发送 {event_count} 个事件")
                    event_count += 1
                    yield {
                        "event": "heartbeat",
                        "data": json.dumps({"time": now.isoformat(), "event_count": event_count}),
                    }
                    last_heartbeat = now

                # 检查任务是否已结束
                if current_status.get("status") in ["completed", "failed", "canceled"]:
                    log.info(
                        f"SSE事件流: 任务 {task_id} 已结束，状态为 {current_status.get('status')}, 共发送 {event_count} 个事件"
                    )
                    # 发送最终状态
                    event_count += 1
                    yield {"event": "complete", "data": json.dumps(current_status)}
                    # 结束事件流
                    break

                # 根据任务状态调整轮询间隔
                if current_status.get("status") == "running":
                    # 运行中的任务保持较短的轮询间隔
                    poll_interval = min(2.0, poll_interval)
                else:
                    # 非运行状态可以稍微延长间隔
                    poll_interval = min(3.0, poll_interval * 1.2)

                # 暂停一段时间再检查
                await asyncio.sleep(poll_interval)

            except Exception as e:
                # 记录错误但继续尝试
                log.error(f"SSE事件流: 获取任务 {task_id} 状态时出错: {str(e)}")
                # 发送错误事件，但不中断流
                event_count += 1
                yield {"event": "error", "data": json.dumps({"error": f"获取状态出错: {str(e)}"})}
                # 增加延迟，避免在错误情况下的高频请求
                await asyncio.sleep(5.0)

    except asyncio.CancelledError:
        log.info(f"SSE事件流: 用户 {user_id} 取消了任务 {task_id} 的SSE连接")
        raise

    except Exception as e:
        # 记录错误并发送错误事件
        import traceback

        log.error(f"SSE事件流错误: 任务 {task_id}, 用户 {user_id}: {str(e)}\n{traceback.format_exc()}")
        yield {"event": "error", "data": json.dumps({"error": str(e)})}

    finally:
        # 确保记录连接关闭
        log.info(f"SSE事件流: 关闭用户 {user_id} 的任务 {task_id} 的事件流")
