"""
STRM任务控制器，用于管理STRM生成任务
"""
import os
import tempfile
from typing import List, Optional, Dict, Any, BinaryIO
from datetime import datetime

from fastapi import UploadFile

from app.models.strm import MediaServer, TaskStatus, FileType
from app.models.strm.task import StrmTask
from app.models.strm.file import StrmFile
from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID
from app.utils.strm import TreeParser, StrmProcessor


class TaskController(CRUDBase):
    """STRM任务控制器"""
    
    async def create_task(self, data: Dict[str, Any]) -> StrmTask:
        """
        创建STRM生成任务
        
        Args:
            data: 任务数据
            
        Returns:
            创建的任务对象
        """
        # 获取当前用户ID
        user_id = CTX_USER_ID.get()
        
        # 创建任务
        task = await StrmTask.create(
            **data,
            status=TaskStatus.PENDING,
            created_by_id=user_id
        )
        
        return task
    
    async def upload_tree_file(self, task_id: int, file: UploadFile) -> Dict[str, Any]:
        """
        上传并解析115目录树文件
        
        Args:
            task_id: 任务ID
            file: 上传的文件
            
        Returns:
            解析结果
        """
        # 获取任务
        task = await self.get(id=task_id)
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".txt")
        temp_path = temp_file.name
        
        try:
            # 写入临时文件
            content = await file.read()
            with open(temp_path, "wb") as f:
                f.write(content)
            
            # 更新任务的源文件路径
            task.source_file = os.path.basename(file.filename)
            await task.save()
            
            # 解析文件
            parser = TreeParser()
            parsed_files = parser.parse_file(temp_path)
            
            # 计算统计信息
            stats = {
                "total": len(parsed_files),
                "video": len([f for f in parsed_files if f["file_type"] == FileType.VIDEO]),
                "audio": len([f for f in parsed_files if f["file_type"] == FileType.AUDIO]),
                "image": len([f for f in parsed_files if f["file_type"] == FileType.IMAGE]),
                "subtitle": len([f for f in parsed_files if f["file_type"] == FileType.SUBTITLE]),
                "other": len([f for f in parsed_files if f["file_type"] == FileType.OTHER])
            }
            
            return {
                "success": True,
                "task_id": task.id,
                "file_name": file.filename,
                "parsed_files": parsed_files[:100],  # 只返回前100个文件，避免响应过大
                "total_files": len(parsed_files),
                "stats": stats
            }
            
        except Exception as e:
            return {
                "success": False,
                "task_id": task.id,
                "file_name": file.filename,
                "error": str(e)
            }
        finally:
            # 删除临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    async def process_task(self, task_id: int, files: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        处理STRM生成任务
        
        Args:
            task_id: 任务ID
            files: 文件列表，如果为空则处理所有解析出的文件
            
        Returns:
            处理结果
        """
        # 获取任务和服务器
        task = await self.get(id=task_id)
        server = await MediaServer.get(id=task.server_id)
        
        # 更新任务状态
        task.status = TaskStatus.RUNNING
        task.start_time = datetime.now()
        await task.save()
        
        try:
            # 创建输出目录
            os.makedirs(task.output_dir, exist_ok=True)
            
            # 创建处理器
            processor = StrmProcessor(server, task.output_dir)
            
            # 处理文件
            result = await processor.process_files(files, task)
            
            # 更新任务状态
            task.status = TaskStatus.COMPLETED
            task.end_time = datetime.now()
            await task.save()
            
            return {
                "success": True,
                "task_id": task.id,
                "result": result
            }
            
        except Exception as e:
            # 更新任务状态为失败
            task.status = TaskStatus.FAILED
            task.end_time = datetime.now()
            await task.save()
            
            return {
                "success": False,
                "task_id": task.id,
                "error": str(e)
            }
    
    async def get_task_files(self, task_id: int, success_only: bool = False) -> List[StrmFile]:
        """
        获取任务生成的文件
        
        Args:
            task_id: 任务ID
            success_only: 是否只返回成功的文件
            
        Returns:
            文件列表
        """
        query = StrmFile.filter(task_id=task_id)
        
        if success_only:
            query = query.filter(is_success=True)
        
        return await query.all()
    
    async def create_zip_archive(self, task_id: int) -> Dict[str, Any]:
        """
        创建ZIP归档
        
        Args:
            task_id: 任务ID
            
        Returns:
            ZIP文件路径
        """
        # 获取任务和服务器
        task = await self.get(id=task_id)
        server = await MediaServer.get(id=task.server_id)
        
        try:
            # 创建处理器
            processor = StrmProcessor(server, task.output_dir)
            
            # 创建ZIP归档
            zip_path = processor.create_zip_archive()
            
            return {
                "success": True,
                "task_id": task.id,
                "zip_path": zip_path
            }
            
        except Exception as e:
            return {
                "success": False,
                "task_id": task.id,
                "error": str(e)
            }


# 创建任务控制器实例
task_controller = TaskController(StrmTask) 