"""
系统设置控制器，用于管理系统范围的设置
"""

from typing import Dict, Any, Optional

from app.models.strm import SystemSettings, MediaServer
from app.core.crud import CRUDBase
from app.core.ctx import CTX_USER_ID
from app.core.exceptions import HTTPException


class SystemSettingsController(CRUDBase):
    """系统设置控制器"""

    async def get_settings(self) -> Optional[Dict[str, Any]]:
        """
        获取系统设置

        如果不存在设置记录，返回None

        Returns:
            系统设置字典或None
        """
        # 获取第一条系统设置记录，通常只会有一条
        settings = await SystemSettings.all().first()
        if not settings:
            return None

        # 获取默认服务器信息（兼容旧版本）
        default_server = None
        if settings.default_server_id:
            server = await MediaServer.filter(id=settings.default_server_id).first()
            if server:
                default_server = {
                    "id": server.id,
                    "name": server.name,
                    "server_type": server.server_type,
                    "base_url": server.base_url,
                    "description": server.description,
                }

        # 获取默认下载服务器信息
        default_download_server = None
        if settings.default_download_server_id:
            server = await MediaServer.filter(id=settings.default_download_server_id).first()
            if server:
                default_download_server = {
                    "id": server.id,
                    "name": server.name,
                    "server_type": server.server_type,
                    "base_url": server.base_url,
                    "description": server.description,
                }

        # 获取默认媒体服务器信息
        default_media_server = None
        if settings.default_media_server_id:
            server = await MediaServer.filter(id=settings.default_media_server_id).first()
            if server:
                default_media_server = {
                    "id": server.id,
                    "name": server.name,
                    "server_type": server.server_type,
                    "base_url": server.base_url,
                    "description": server.description,
                }

        return {
            "id": settings.id,
            "default_server_id": settings.default_server_id,
            "default_server": default_server,
            "default_download_server_id": settings.default_download_server_id,
            "default_download_server": default_download_server,
            "default_media_server_id": settings.default_media_server_id,
            "default_media_server": default_media_server,
            "enable_path_replacement": settings.enable_path_replacement,
            "replacement_path": settings.replacement_path,
            "download_threads": settings.download_threads,
            "output_directory": settings.output_directory,
            "update_time": settings.update_time,
        }

    async def create_or_update_settings(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建或更新系统设置

        如果不存在设置记录，创建新记录；如果存在，则更新现有记录

        Args:
            data: 系统设置数据

        Returns:
            更新后的系统设置字典
        """
        # 获取当前用户ID
        user_id = CTX_USER_ID.get()

        # 检查各种服务器ID是否存在
        server_id_fields = ["default_server_id", "default_download_server_id", "default_media_server_id"]
        for field in server_id_fields:
            if data.get(field):
                server = await MediaServer.filter(id=data[field]).first()
                if not server:
                    raise HTTPException(code=404, msg=f"指定的服务器ID不存在: {data[field]}")

        # 查找现有设置
        settings = await SystemSettings.all().first()

        if settings:
            # 更新现有设置
            for field, value in data.items():
                setattr(settings, field, value)
            settings.updated_by_id = user_id
            await settings.save()
        else:
            # 创建新设置
            settings = await SystemSettings.create(**data, updated_by_id=user_id)

        # 获取更新后的设置
        return await self.get_settings()


system_settings_controller = SystemSettingsController(SystemSettings)
