"""
STRM任务控制器，用于管理STRM文件生成任务
"""

import os
import shutil
import tempfile
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

from fastapi import HTTPException
from fastapi.responses import FileResponse

from app.models.strm import StrmTask, StrmFile, MediaServer, TaskStatus
from app.models.system import User
from app.utils.strm.processor import StrmProcessor, process_directory_tree
from app.controllers.strm.upload import get_parse_result
from app.settings import APP_SETTINGS

# 定义STRM文件生成的临时目录
STRM_OUTPUT_DIR = Path(APP_SETTINGS.BASE_DIR) / "strm_output"
STRM_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)


async def create_strm_task(
    record_id: int, server_id: int, user: User, output_dir: Optional[str] = None, custom_name: Optional[str] = None
) -> StrmTask:
    """
    创建STRM文件生成任务

    Args:
        record_id: 上传记录ID
        server_id: 媒体服务器ID
        user: 当前用户
        output_dir: 自定义输出目录，默认为临时目录
        custom_name: 自定义任务名称

    Returns:
        创建的任务对象
    """
    # 检查服务器是否存在
    server = await MediaServer.get_or_none(id=server_id)
    if not server:
        raise HTTPException(status_code=404, detail=f"找不到ID为{server_id}的媒体服务器")

    # 获取解析结果
    parse_result = await get_parse_result(record_id, user, "all", 1, 10, True)
    if not parse_result:
        raise HTTPException(status_code=404, detail=f"找不到ID为{record_id}的文件解析结果")

    # 确定输出目录
    if not output_dir:
        output_dir = str(STRM_OUTPUT_DIR / f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{user.id}")

    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 创建任务名称
    if not custom_name:
        custom_name = (
            f"STRM生成-{parse_result.get('file_name', '未命名')}-{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        )

    # 创建任务记录
    task = await StrmTask.create(
        name=custom_name,
        server=server,
        source_file=str(record_id),  # 存储上传记录ID
        output_dir=output_dir,
        total_files=parse_result.get("stats", {}).get("video", 0),  # 只计算视频文件
        status=TaskStatus.PENDING,
        created_by=user,
    )

    return task


async def start_strm_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    启动STRM文件生成任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        任务启动结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务状态
    if task.status == TaskStatus.RUNNING:
        return {"success": False, "message": "任务已在运行中"}

    # 获取上传记录ID和服务器
    record_id = int(task.source_file)
    server = await MediaServer.get(id=task.server_id)

    # 获取解析结果
    parse_result = await get_parse_result(record_id, user, "all", 1, 10, True)
    if not parse_result:
        raise HTTPException(status_code=404, detail=f"找不到ID为{record_id}的文件解析结果")

    # 只处理视频文件
    files = [f for f in parse_result.get("parsed_files", []) if f["file_type"] == "video"]

    # 更新任务状态
    task.status = TaskStatus.RUNNING
    task.start_time = datetime.now()
    await task.save()

    # 启动处理过程
    try:
        result = await process_directory_tree(server_id=server.id, files=files, output_dir=task.output_dir, task=task)

        return {"success": True, "message": "任务启动成功", "task_id": task.id, "result": result}
    except Exception as e:
        # 更新任务状态为失败
        task.status = TaskStatus.FAILED
        await task.save()
        raise HTTPException(status_code=500, detail=f"任务启动失败: {str(e)}")


async def get_task_status(task_id: int, user: User) -> Dict[str, Any]:
    """
    获取任务状态

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        任务状态信息
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此任务")

    # 获取关联的文件记录
    files = await StrmFile.filter(task_id=task_id).all()

    # 计算进度百分比
    progress = 0
    if task.total_files > 0:
        progress = min(100, int(task.processed_files * 100 / task.total_files))

    return {
        "id": task.id,
        "name": task.name,
        "status": task.status,
        "total_files": task.total_files,
        "processed_files": task.processed_files,
        "success_files": task.success_files,
        "failed_files": task.failed_files,
        "progress": progress,
        "start_time": task.start_time.isoformat() if task.start_time else None,
        "end_time": task.end_time.isoformat() if task.end_time else None,
        "output_dir": task.output_dir,
        "files": [
            {
                "id": f.id,
                "source_path": f.source_path,
                "target_path": f.target_path,
                "file_type": f.file_type,
                "file_size": f.file_size,
                "is_success": f.is_success,
                "error_message": f.error_message,
            }
            for f in files[:10]  # 只返回前10条记录，避免数据过大
        ],
        "file_count": len(files),
    }


async def get_user_tasks(user: User, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
    """
    获取用户的任务列表

    Args:
        user: 当前用户
        page: 页码
        page_size: 每页数量

    Returns:
        任务列表
    """
    # 计算偏移量
    offset = (page - 1) * page_size

    # 查询任务
    tasks = await StrmTask.filter(created_by=user).offset(offset).limit(page_size).order_by("-create_time")
    total = await StrmTask.filter(created_by=user).count()

    return {
        "total": total,
        "page": page,
        "page_size": page_size,
        "tasks": [
            {
                "id": t.id,
                "name": t.name,
                "status": t.status,
                "total_files": t.total_files,
                "processed_files": t.processed_files,
                "success_files": t.success_files,
                "failed_files": t.failed_files,
                "progress": min(100, int(t.processed_files * 100 / t.total_files)) if t.total_files > 0 else 0,
                "start_time": t.start_time.isoformat() if t.start_time else None,
                "end_time": t.end_time.isoformat() if t.end_time else None,
            }
            for t in tasks
        ],
    }


async def download_strm_files(task_id: int, user: User) -> FileResponse:
    """
    下载生成的STRM文件包

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        文件响应对象
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限下载此任务的文件")

    # 检查任务状态
    if task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        raise HTTPException(status_code=400, detail="任务尚未完成，无法下载")

    # 检查输出目录是否存在
    output_dir = Path(task.output_dir)
    if not output_dir.exists():
        raise HTTPException(status_code=404, detail="输出目录不存在，可能已被删除")

    # 创建ZIP文件
    zip_file_name = f"strm_files_{task_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.zip"
    zip_path = STRM_OUTPUT_DIR / zip_file_name

    try:
        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for root, _, files in os.walk(output_dir):
                for file in files:
                    if file.endswith(".strm"):
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, output_dir)
                        zipf.write(file_path, arcname)

        # 返回文件响应
        return FileResponse(zip_path, media_type="application/zip", filename=zip_file_name)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建ZIP文件失败: {str(e)}")


async def cancel_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    取消任务

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        操作结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 检查任务状态
    if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
        return {"success": False, "message": "任务已完成或已取消，无法取消"}

    # 更新任务状态
    task.status = TaskStatus.CANCELED
    task.end_time = datetime.now()
    await task.save()

    return {"success": True, "message": "任务已取消"}


async def delete_task(task_id: int, user: User) -> Dict[str, Any]:
    """
    删除任务及相关文件

    Args:
        task_id: 任务ID
        user: 当前用户

    Returns:
        操作结果
    """
    # 获取任务
    task = await StrmTask.get_or_none(id=task_id)
    if not task:
        raise HTTPException(status_code=404, detail=f"找不到ID为{task_id}的任务")

    # 检查权限
    if task.created_by_id != user.id:
        raise HTTPException(status_code=403, detail="没有权限操作此任务")

    # 删除输出目录
    output_dir = Path(task.output_dir)
    if output_dir.exists():
        try:
            shutil.rmtree(output_dir)
        except Exception as e:
            # 记录错误但继续删除任务记录
            print(f"删除任务输出目录失败: {str(e)}")

    # 删除相关文件记录
    await StrmFile.filter(task_id=task_id).delete()

    # 删除任务记录
    await task.delete()

    return {"success": True, "message": "任务已删除"}
