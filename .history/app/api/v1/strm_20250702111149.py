"""
STRM管理模块API

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

from fastapi import (
    APIRouter,
    Depends,
    UploadFile,
    File,
    Body,
    Query,
    Path,
    Request,
    status,
)

import json
from app.controllers.strm.upload import (
    handle_file_upload,
    parse_uploaded_file,
    delete_upload_record,
    download_upload_file,
    get_parse_result,
    handle_url_upload,
    get_directory_content,
    search_files,
)
from app.controllers.strm.task_controller import (
    create_strm_task,
    start_strm_task,
    get_task_status,
    get_user_tasks,
    download_strm_files,
    cancel_task,
    delete_task,
    get_task_logs,
    create_resource_download_task,
    start_resource_download_task,
    get_resource_task_status,
    cancel_resource_task,
    delete_resource_task,
    get_resource_task_logs,
    get_user_resource_tasks,
)
from app.core.dependency import get_current_user, check_token, AuthControl
from app.models.system import User
from app.schemas.base import Success, SuccessExtra
from app.schemas.strm.schemas import (
    ParseRequest,
    UploadHistoryResponse,
    UrlUploadRequest,
    MediaServerResponse,
    StrmTaskCreate,
    StrmTaskResponse,
    SystemSettingsUpdate,
    SystemSettingsResponse,
    MediaServerBase,
    ResourceDownloadTaskCreate,
    ResourceDownloadTaskResponse,
)
from app.models.strm.upload import UploadRecord
from app.models.strm import MediaServer
from typing import List, Optional
from fastapi.responses import FileResponse, JSONResponse
from app.core.exceptions import HTTPException
from app.controllers.strm import system_settings_controller
from app.controllers.strm.server_controller import server_controller
import asyncio
from datetime import datetime
from app.models.strm import TaskStatus, TaskType
from app.core.bgtask import BgTasks

router_strm = APIRouter()


# 认证用户从请求中
async def authenticate_user_from_request(request: Request, token: Optional[str] = None):
    """
    从请求中认证用户

    支持两种方式：
    1. 从授权头获取Bearer令牌
    2. 从URL查询参数获取令牌

    Args:
        request: FastAPI请求对象
        token: 可选令牌，如果已从其他地方获取

    Returns:
        认证的用户或None
    """
    from app.log.log import log

    current_user = None

    # 如果提供了token，直接使用
    if token:
        try:
            status, code, decode_data = check_token(token)
            if status and decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()
                if current_user:
                    log.debug(f"使用提供的token认证成功: 用户 {current_user.user_name}")
                else:
                    log.warning(f"使用提供的token认证失败: 用户ID {user_id} 不存在")
            else:
                log.warning(f"使用提供的token认证失败: 状态码 {code}")
        except Exception as e:
            log.error(f"处理提供的token时出错: {str(e)}")
            return None

    # 如果没有提供token或认证失败，尝试从授权头获取
    if not current_user:
        authorization = request.headers.get("authorization")
        if authorization and authorization.startswith("Bearer "):
            token = authorization.replace("Bearer ", "")
            try:
                status, code, decode_data = check_token(token)
                if status and decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()
                    if current_user:
                        log.debug(f"使用授权头token认证成功: 用户 {current_user.user_name}")
                    else:
                        log.warning(f"使用授权头token认证失败: 用户ID {user_id} 不存在")
                else:
                    log.warning(f"使用授权头token认证失败: 状态码 {code}")
            except Exception as e:
                log.error(f"处理授权头token时出错: {str(e)}")

    # 如果仍未认证，尝试从URL查询参数获取令牌
    if not current_user:
        params = dict(request.query_params)
        token = params.get("token")
        if token:
            try:
                status, code, decode_data = check_token(token)
                if status and decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()
                    if current_user:
                        log.debug(f"使用URL查询参数token认证成功: 用户 {current_user.user_name}")
                    else:
                        log.warning(f"使用URL查询参数token认证失败: 用户ID {user_id} 不存在")
                else:
                    log.warning(f"使用URL查询参数token认证失败: 状态码 {code}")
            except Exception as e:
                log.error(f"处理URL查询参数token时出错: {str(e)}")

    return current_user


@router_strm.post("/upload", summary="上传115目录树文件")
async def upload_115_directory_tree_file(current_user: User = Depends(get_current_user), file: UploadFile = File(...)):
    """
    上传115目录树文件。
    - **current_user**: 当前已登录用户。
    - **file**: 要上传的文件。
    """
    upload_record = await handle_file_upload(file, current_user)

    # 将 ORM 对象转换为字典，以便可以序列化为 JSON
    result = {
        "id": upload_record.id,
        "filename": upload_record.filename,
        "filesize": upload_record.filesize,
        "status": upload_record.status.value if upload_record.status else None,
        "create_time": upload_record.create_time.isoformat() if upload_record.create_time else None,
        "uploader_id": upload_record.uploader_id,
        "parse_time": upload_record.parse_time.isoformat() if upload_record.parse_time else None,
    }

    return Success(data=result)


@router_strm.post("/upload-url", summary="通过URL上传115目录树文件")
async def upload_115_directory_tree_from_url(
    current_user: User = Depends(get_current_user), data: UrlUploadRequest = Body(...)
):
    """
    通过URL上传115目录树文件。
    - **current_user**: 当前已登录用户。
    - **data**: 包含URL的请求数据。
    """
    # 获取URL
    url = data.url
    # 移除可能的引号（前端有时会带引号）
    if url.startswith('"') and url.endswith('"'):
        url = url[1:-1]
    if url.startswith("'") and url.endswith("'"):
        url = url[1:-1]

    # 使用控制器处理URL上传
    upload_record = await handle_url_upload(url, current_user)

    # 将 ORM 对象转换为字典，以便可以序列化为 JSON
    result = {
        "id": upload_record.id,
        "filename": upload_record.filename,
        "filesize": upload_record.filesize,
        "status": upload_record.status.value if upload_record.status else None,
        "create_time": upload_record.create_time.isoformat() if upload_record.create_time else None,
        "uploader_id": upload_record.uploader_id,
        "parse_time": upload_record.parse_time.isoformat() if upload_record.parse_time else None,
    }

    return Success(data=result)


@router_strm.post("/parse", summary="解析已上传的115目录树文件")
async def parse_directory_tree_file(current_user: User = Depends(get_current_user), data: ParseRequest = Body(...)):
    """
    解析已上传的115目录树文件。
    - **current_user**: 当前已登录用户。
    - **data**: 包含记录ID的请求数据。
    """
    result_data = await parse_uploaded_file(data.record_id)

    # 确保返回的是可序列化的字典
    if isinstance(result_data, UploadRecord):
        result = {
            "id": result_data.id,
            "filename": result_data.filename,
            "filesize": result_data.filesize,
            "status": result_data.status.value if result_data.status else None,
            "create_time": result_data.create_time.isoformat() if result_data.create_time else None,
            "uploader_id": result_data.uploader_id,
            "parse_time": result_data.parse_time.isoformat() if result_data.parse_time else None,
            "parsed_result": result_data.parsed_result,
        }
    else:
        result = result_data

    return Success(data=result)


@router_strm.get("/history", summary="获取上传历史记录", response_model=UploadHistoryResponse)
async def get_upload_history(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    """
    获取当前用户的文件上传历史记录。
    """
    skip = (page - 1) * page_size
    records = await UploadRecord.filter(uploader=current_user).offset(skip).limit(page_size).order_by("-create_time")
    total = await UploadRecord.filter(uploader=current_user).count()

    # 将 ORM 对象转换为字典以便可序列化为 JSON
    record_dicts = []
    for record in records:
        record_dict = {
            "id": record.id,
            "filename": record.filename,
            "filesize": record.filesize,
            "status": record.status.value,
            "create_time": record.create_time.isoformat(),
            "uploader_id": record.uploader_id,
            "parse_time": record.parse_time.isoformat() if record.parse_time else None,
        }
        record_dicts.append(record_dict)

    return Success(data={"total": total, "page": page, "page_size": page_size, "records": record_dicts})


@router_strm.delete("/history/{record_id}", summary="删除上传记录")
async def delete_history_record(
    record_id: int = Path(..., description="记录ID"), current_user: User = Depends(get_current_user)
):
    """
    删除上传记录及对应的文件。
    - **record_id**: 要删除的记录ID.
    - **current_user**: 当前已登录用户.
    """
    await delete_upload_record(record_id, current_user)
    return Success(data={"message": "记录已成功删除"})


@router_strm.get("/download/{record_id}", summary="下载已上传的文件", response_class=FileResponse)
async def download_file(
    request: Request,
    record_id: int = Path(..., description="记录ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载已上传的文件。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **record_id**: 要下载的记录ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="Authentication failed, valid token required")

    return await download_upload_file(record_id, current_user)


@router_strm.get("/result/{record_id}", summary="获取文件解析结果")
async def get_file_parse_result(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    all_files: bool = Query(False, description="是否获取所有文件（不分页）"),
):
    """
    获取已解析文件的解析结果，支持按文件类型过滤和分页。
    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为10。
    - **all_files**: 是否返回所有文件（不进行分页），默认为false。
    """
    result = await get_parse_result(record_id, current_user, file_type, page, page_size, all_files)
    return Success(data=result)


@router_strm.get("/directory/{record_id}", summary="获取目录内容")
async def get_directory(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    directory_path: str = Query("/", description="目录路径，默认为根目录"),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
):
    """
    获取指定目录下的文件和子目录。
    此API采用懒加载方式，每次只返回指定目录下的直接文件和子目录，不会递归获取所有内容。

    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **directory_path**: 目录路径，默认为根目录"/"。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为20。
    """
    result = await get_directory_content(record_id, current_user, directory_path, file_type, page, page_size)
    return Success(data=result)


@router_strm.get("/search/{record_id}", summary="搜索文件")
async def search_parsed_files(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    search_text: str = Query(..., description="搜索文本"),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    ignore_case: bool = Query(True, description="是否忽略大小写"),
):
    """
    搜索已解析文件中的匹配项
    - **record_id**: 记录ID
    - **current_user**: 当前用户
    - **search_text**: 要搜索的文本
    - **file_type**: 文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)
    - **ignore_case**: 是否忽略大小写，默认为True
    """
    result = await search_files(record_id, current_user, search_text, file_type, ignore_case)
    return Success(data=result)


# === STRM生成相关API端点 ===


@router_strm.get("/servers", summary="获取媒体服务器列表")
async def get_media_servers(current_user: User = Depends(get_current_user)):
    """
    获取当前用户可用的媒体服务器列表
    """
    servers = await MediaServer.all()
    # 将 ORM 对象转换为字典
    server_list = []
    for server in servers:
        server_dict = {
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
        server_list.append(server_dict)

    return Success(data=server_list)


@router_strm.post("/generate", summary="创建STRM生成任务")
async def generate_strm(
    current_user: User = Depends(get_current_user),
    data: StrmTaskCreate = Body(...),
):
    """
    创建STRM生成任务。
    - **current_user**: 当前已登录用户。
    - **data**: 任务创建数据，包括解析记录ID、服务器ID等。
    """
    from app.log.log import log

    try:
        # 创建一个STRM任务
        task = await create_strm_task(
            record_id=data.record_id,
            server_id=data.server_id,
            user=current_user,
            output_dir=data.output_dir,
            custom_name=data.name,
            download_server_id=data.download_server_id,
            download_resources=data.download_resources,
        )

        # 启动后台任务来处理STRM文件生成
        log.info(f"将任务 {task.id} 添加到后台队列")
        # 使用'date'触发器立即执行任务
        await BgTasks.add_job(
            start_strm_task,
            "date",
            args=[task.id, current_user.id],
            id=f"strm_task_{task.id}",
            name=f"STRM Task {task.id}",
            replace_existing=True,  # 如果因意外重启而存在相同ID的任务，则替换它
        )
        log.info(f"任务 {task.id} 已成功添加到后台队列")

        return Success(data={"task_id": task.id, "message": "任务已成功创建并开始处理"})

    except HTTPException as http_exc:
        # 重新抛出HTTP异常，以便FastAPI可以处理
        raise http_exc
    except Exception as e:
        # 记录将任务添加到后台队列时的错误
        import traceback

        log.error(f"将任务添加到后台队列失败: {e}\n{traceback.format_exc()}")

        # 对于所有其他异常，返回一个通用错误
        # 尝试更新任务状态为失败
        if "task" in locals() and task:
            try:
                task.status = TaskStatus.FAILED
                task.log_content = f"任务创建或排队失败: {str(e)}\n这是一个严重错误，表明后台任务系统可能无法正常工作。"
                await task.save()
            except Exception as db_exc:
                log.error(f"更新任务状态为失败时发生数据库错误: {db_exc}")

        # 返回通用错误响应
        log.error(f"STRM任务创建失败: {e}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"code": 5000, "msg": f"创建任务时发生严重错误，请检查后台日志: {str(e)}"},
        )


@router_strm.get("/tasks", summary="获取用户任务列表")
async def get_tasks(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    search: Optional[str] = Query(None, description="按名称搜索"),
    status: Optional[str] = Query(None, description="按状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期过滤 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期过滤 (YYYY-MM-DD)"),
):
    """
    获取当前用户的STRM生成任务列表，支持按名称搜索和按状态/日期过滤
    """
    result = await get_user_tasks(
        current_user, page, page_size, search=search, status=status, start_date=start_date, end_date=end_date
    )
    return Success(data=result)


@router_strm.get("/task/{task_id}", summary="获取任务状态")
async def check_task_status(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    获取指定任务的状态和进度
    """
    result = await get_task_status(task_id, current_user)
    return Success(data=result)


@router_strm.post("/task/{task_id}/cancel", summary="取消任务")
async def cancel_task_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    取消正在进行的任务
    """
    result = await cancel_task(task_id, current_user)
    return Success(data=result)


@router_strm.delete("/task/{task_id}", summary="删除任务")
async def delete_task_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    删除任务。
    - **task_id**: 任务ID.
    - **current_user**: 当前已登录用户.
    """
    result = await delete_task(task_id, current_user)
    return Success(data=result)


@router_strm.get("/task/{task_id}/logs", summary="获取任务日志")
async def get_task_logs_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(50, description="每页数量"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    search: Optional[str] = Query(None, description="日志内容搜索"),
):
    """
    获取任务的详细日志记录。
    - **task_id**: 任务ID.
    - **current_user**: 当前已登录用户.
    - **page**: 页码，默认为1.
    - **page_size**: 每页数量，默认为50.
    - **level**: 日志级别过滤，可选值：INFO、ERROR等.
    - **search**: 日志内容搜索关键词.
    """
    logs = await get_task_logs(task_id, current_user, page, page_size, level, search)
    return Success(data=logs)


@router_strm.get("/download-strm/{task_id}", summary="下载生成的STRM文件", response_class=FileResponse)
async def download_strm_files_endpoint(
    request: Request,
    task_id: int = Path(..., description="任务ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载生成的STRM文件（ZIP压缩包）。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **task_id**: 要下载文件的任务ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="认证失败，需要有效的令牌")

    return await download_strm_files(task_id, current_user)


# === 系统设置相关API端点 ===

# === 服务器管理API端点 ===


@router_strm.post("/server", summary="创建媒体服务器")
async def create_media_server(data: MediaServerBase = Body(...), current_user: User = Depends(get_current_user)):
    """
    创建新的媒体服务器

    - **current_user**: 当前已登录用户
    - **data**: 服务器数据
    """
    server = await server_controller.create_server(data.dict())
    return Success(
        data={
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
    )


@router_strm.put("/server/{server_id}", summary="更新媒体服务器")
async def update_media_server(
    server_id: int = Path(..., description="服务器ID"),
    data: MediaServerBase = Body(...),
    current_user: User = Depends(get_current_user),
):
    """
    更新媒体服务器信息

    - **server_id**: 要更新的服务器ID
    - **data**: 更新的服务器数据
    - **current_user**: 当前已登录用户
    """
    server = await server_controller.update_server(server_id, data.dict())
    return Success(
        data={
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
    )


@router_strm.delete("/server/{server_id}", summary="删除媒体服务器")
async def delete_media_server(
    server_id: int = Path(..., description="服务器ID"), current_user: User = Depends(get_current_user)
):
    """
    删除媒体服务器

    - **server_id**: 要删除的服务器ID
    - **current_user**: 当前已登录用户
    """
    await server_controller.remove(id=server_id)
    return Success(data={"message": "服务器已成功删除"})


@router_strm.post("/server/{server_id}/test", summary="测试服务器连接")
async def test_server_connection(
    server_id: int = Path(..., description="服务器ID"), current_user: User = Depends(get_current_user)
):
    """
    测试媒体服务器连接

    - **server_id**: 要测试的服务器ID
    - **current_user**: 当前已登录用户
    """
    result = await server_controller.test_connection(server_id)
    return Success(data=result)


@router_strm.post("/server/test", summary="测试未保存的服务器连接")
async def test_server_connection_without_save(
    data: MediaServerBase = Body(...), current_user: User = Depends(get_current_user)
):
    """
    测试未保存的媒体服务器连接

    - **data**: 服务器数据
    - **current_user**: 当前已登录用户
    """
    result = await server_controller.test_connection_without_save(data.dict())
    return Success(data=result)


@router_strm.get("/test-file-type", summary="测试文件类型检测")
async def test_file_type_detection_endpoint(current_user: User = Depends(get_current_user)):
    """
    测试文件类型检测功能，用于验证系统设置是否正确应用于文件类型识别。

    - **current_user**: 当前登录用户，必须拥有管理权限。

    返回不同配置下NFO文件的类型检测结果。
    """
    from app.utils.strm.parser import test_file_type_detection

    # 运行测试并返回结果
    results = await test_file_type_detection()
    return Success(data=results)


@router_strm.get("/debug/settings", summary="调试系统设置")
async def debug_system_settings(current_user: User = Depends(get_current_user)):
    """
    用于调试系统设置，特别是文件类型配置。
    此接口返回当前系统设置并提供建议的默认值。
    """
    from app.models.strm import SystemSettings

    # 获取当前系统设置
    settings = await SystemSettings.all().first()
    if not settings:
        return Success(data={"message": "系统设置不存在"})

    # 准备建议的文件类型配置
    suggested_video_types = "mkv,mp4,avi,mov,wmv,flv,webm,m4v,mpg,mpeg"
    suggested_audio_types = "mp3,wav,flac,aac,ogg,wma,m4a"
    suggested_image_types = "jpg,jpeg,png,gif,bmp,webp,tiff"
    suggested_subtitle_types = "srt,ass,ssa,vtt,sub"
    suggested_metadata_types = "nfo,xml,json"

    # 当前配置
    current_settings = {
        "video_file_types": settings.video_file_types,
        "audio_file_types": settings.audio_file_types,
        "image_file_types": settings.image_file_types,
        "subtitle_file_types": settings.subtitle_file_types,
        "metadata_file_types": settings.metadata_file_types,
        "settings_version": settings.settings_version,
    }

    # 建议配置
    suggested_settings = {
        "video_file_types": suggested_video_types,
        "audio_file_types": suggested_audio_types,
        "image_file_types": suggested_image_types,
        "subtitle_file_types": suggested_subtitle_types,
        "metadata_file_types": suggested_metadata_types,
    }

    return Success(
        data={
            "current_settings": current_settings,
            "suggested_settings": suggested_settings,
            "is_video_types_empty": not settings.video_file_types,
            "update_url": "/strm/debug/update-settings",
        }
    )


@router_strm.post("/debug/update-settings", summary="更新系统设置")
async def update_system_settings(current_user: User = Depends(get_current_user)):
    """
    更新系统设置，使用建议的默认值。
    此接口用于调试，会将文件类型配置更新为推荐值。
    """
    from app.models.strm import SystemSettings

    # 获取当前系统设置
    settings = await SystemSettings.all().first()
    if not settings:
        # 如果系统设置不存在，创建一个新的
        settings = SystemSettings()

    # 更新文件类型配置
    settings.video_file_types = "mkv,mp4,avi,mov,wmv,flv,webm,m4v,mpg,mpeg"
    settings.audio_file_types = "mp3,wav,flac,aac,ogg,wma,m4a"
    settings.image_file_types = "jpg,jpeg,png,gif,bmp,webp,tiff"
    settings.subtitle_file_types = "srt,ass,ssa,vtt,sub"
    settings.metadata_file_types = "nfo,xml,json"

    # 更新版本号
    settings.settings_version = settings.settings_version + 1

    # 保存更新
    await settings.save()

    return Success(
        data={
            "message": "系统设置已更新",
            "new_settings": {
                "video_file_types": settings.video_file_types,
                "audio_file_types": settings.audio_file_types,
                "image_file_types": settings.image_file_types,
                "subtitle_file_types": settings.subtitle_file_types,
                "metadata_file_types": settings.metadata_file_types,
                "settings_version": settings.settings_version,
            },
        }
    )


@router_strm.get("/debug/check-settings", summary="直接检查系统设置表内容")
async def check_system_settings(current_user: User = Depends(get_current_user)):
    """
    直接检查系统设置表内容，用于调试系统设置加载问题
    """
    try:
        from app.models.strm import SystemSettings
        from tortoise.connections import connections

        # 直接使用SQL查询系统设置表
        conn = connections.get("conn_system")
        results = await conn.execute_query("SELECT * FROM strm_system_settings")

        # 检查是否有结果
        if results and results[1]:
            rows = results[1]
            print(f"[调试] 直接SQL查询到系统设置记录: {len(rows)}条")

            # 提取第一条记录的所有字段
            if rows:
                record = rows[0]
                print(f"[调试] 第一条记录: {record}")

                # 返回查询结果
                return Success(data={"sql_query": {"record_count": len(rows), "first_record": record}})
        else:
            print("[调试] 未找到系统设置记录")

            # 检查是否存在表
            table_check = await conn.execute_query(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='strm_system_settings'"
            )
            tables_exist = table_check and table_check[1]

            # 创建一条默认记录
            try:
                settings = await SystemSettings.create(
                    video_file_types="mkv,mp4,avi,rmvb,wmv,mov,m2ts,ts,iso,flv",
                    audio_file_types="mp3,flac,wav,aac,ogg,m4a,wma",
                    image_file_types="jpg,jpeg,png,gif,bmp,tiff,webp",
                    subtitle_file_types="srt,ass,ssa,vtt,sub,idx",
                    metadata_file_types="nfo,xml,json,txt",
                )
                print(f"[调试] 成功创建默认系统设置: ID={settings.id}")
                return Success(
                    data={
                        "message": "未找到系统设置记录，已创建默认记录",
                        "new_settings": {
                            "id": settings.id,
                            "video_file_types": settings.video_file_types,
                            "audio_file_types": settings.audio_file_types,
                            "image_file_types": settings.image_file_types,
                            "subtitle_file_types": settings.subtitle_file_types,
                            "metadata_file_types": settings.metadata_file_types,
                        },
                        "table_exists": tables_exist,
                    }
                )
            except Exception as create_error:
                print(f"[调试] 创建默认记录失败: {str(create_error)}")
                return Success(
                    data={
                        "message": "未找到系统设置记录，创建默认记录失败",
                        "error": str(create_error),
                        "table_exists": tables_exist,
                    }
                )
    except Exception as e:
        import traceback

        error_detail = traceback.format_exc()
        print(f"[调试] 检查系统设置表失败: {str(e)}")
        print(f"[调试] 错误详情: {error_detail}")
        return Success(data={"error": str(e), "traceback": error_detail})


@router_strm.get("/debug/file-types", summary="调试文件类型检测")
async def debug_file_types(current_user: User = Depends(get_current_user)):
    """
    调试接口：测试文件类型检测配置是否正确加载
    """
    # 获取系统设置
    settings = await system_settings_controller.get_settings()

    # 创建TreeParser实例
    from app.utils.strm.parser import TreeParser

    parser = TreeParser(settings)

    # 构造测试文件列表
    test_files = [
        "/test/video.mp4",
        "/test/video.mkv",
        "/test/audio.mp3",
        "/test/audio.flac",
        "/test/image.jpg",
        "/test/image.png",
        "/test/subtitle.srt",
        "/test/metadata.nfo",
        "/test/other.txt",
    ]

    # 测试文件类型检测
    results = []
    for file_path in test_files:
        file_type = parser.get_file_type(file_path)
        results.append(
            {
                "file_path": file_path,
                "detected_type": file_type,
            }
        )

    # 返回测试结果和设置信息
    return Success(
        data={
            "settings": settings,
            "parser_config": {
                "video_extensions": parser.video_extensions,
                "audio_extensions": parser.audio_extensions,
                "image_extensions": parser.image_extensions,
                "subtitle_extensions": parser.subtitle_extensions,
                "metadata_extensions": parser.metadata_extensions,
                "settings_version": parser.settings_version,
            },
            "detection_results": results,
        }
    )


# 添加资源下载任务相关的API端点
@router_strm.post("/resource-download", summary="创建资源下载任务")
async def create_resource_download(
    current_user: User = Depends(get_current_user),
    data: ResourceDownloadTaskCreate = Body(...),
):
    """
    创建资源下载任务

    - **current_user**: 当前已登录用户
    - **data**: 资源下载任务创建数据
    """
    # 创建资源下载任务
    task = await create_resource_download_task(
        strm_task_id=data.strm_task_id,
        server_id=data.server_id,
        user=current_user,
        file_types=data.file_types,
        threads=data.threads,
        custom_name=data.custom_name,
    )

    # 异步启动任务
    BgTasks.add_task_safe(
        start_resource_download_task,
        id=f"resource_task_{task.id}",
        name=f"Resource Download Task {task.id}",
        task_id=task.id,
        user_id=current_user.id,
    )

    return Success(
        data={
            "message": "资源下载任务已创建",
            "task_id": task.id,
            "name": task.name,
            "status": task.status,
        }
    )


@router_strm.get("/resource-tasks", summary="获取用户资源下载任务列表")
async def get_resource_tasks(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    search: Optional[str] = Query(None, description="按名称搜索"),
    status: Optional[str] = Query(None, description="按状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期过滤 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期过滤 (YYYY-MM-DD)"),
    strm_task_id: Optional[int] = Query(None, description="按STRM任务ID过滤"),
):
    """
    获取当前用户的资源下载任务列表
    """
    result = await get_user_resource_tasks(
        user=current_user,
        page=page,
        page_size=page_size,
        search=search,
        status=status,
        start_date=start_date,
        end_date=end_date,
        strm_task_id=strm_task_id,
    )
    return Success(data=result)


@router_strm.get("/resource-task/{task_id}", summary="获取资源下载任务状态")
async def check_resource_task_status(
    task_id: int = Path(..., description="资源下载任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    获取指定资源下载任务的状态和进度
    """
    result = await get_resource_task_status(task_id, current_user)
    return Success(data=result)


@router_strm.post("/resource-task/{task_id}/cancel", summary="取消资源下载任务")
async def cancel_resource_task_endpoint(
    task_id: int = Path(..., description="资源下载任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    取消正在进行的资源下载任务
    """
    result = await cancel_resource_task(task_id, current_user)
    return Success(data=result)


@router_strm.delete("/resource-task/{task_id}", summary="删除资源下载任务")
async def delete_resource_task_endpoint(
    task_id: int = Path(..., description="资源下载任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    删除资源下载任务。
    - **task_id**: 资源下载任务ID.
    - **current_user**: 当前已登录用户.
    """
    result = await delete_resource_task(task_id, current_user)
    return Success(data=result)


@router_strm.get("/resource-task/{task_id}/logs", summary="获取资源下载任务日志")
async def get_resource_task_logs_endpoint(
    task_id: int = Path(..., description="资源下载任务ID"),
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(50, description="每页数量"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    search: Optional[str] = Query(None, description="日志内容搜索"),
):
    """
    获取资源下载任务的详细日志记录。
    - **task_id**: 资源下载任务ID.
    - **current_user**: 当前已登录用户.
    - **page**: 页码，默认为1.
    - **page_size**: 每页数量，默认为50.
    - **level**: 日志级别过滤，可选值：INFO、ERROR等.
    - **search**: 日志内容搜索关键词.
    """
    logs = await get_resource_task_logs(task_id, current_user, page, page_size, level, search)
    return Success(data=logs)
