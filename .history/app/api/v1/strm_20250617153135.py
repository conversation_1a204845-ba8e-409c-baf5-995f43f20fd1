from fastapi import APIRouter, Depends, UploadFile, File, Body, Query, Path, Request
from app.controllers.strm.upload import (
    handle_file_upload,
    parse_uploaded_file,
    delete_upload_record,
    download_upload_file,
    get_parse_result,
    handle_url_upload,
)
from app.core.dependency import get_current_user, check_token, AuthControl
from app.models.system import User
from app.schemas.base import Success, SuccessExtra
from app.schemas.strm.schemas import ParseRequest, UploadHistoryResponse, UrlUploadRequest
from app.models.strm.upload import UploadRecord
from typing import List, Optional
from fastapi.responses import FileResponse
from app.core.exceptions import HTTPException

router_strm = APIRouter()


@router_strm.post("/upload", summary="上传115目录树文件")
async def upload_115_directory_tree_file(current_user: User = Depends(get_current_user), file: UploadFile = File(...)):
    """
    上传并处理115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **file**: 上传的文件，必须是.txt格式.
    """
    record = await handle_file_upload(file, current_user)
    return Success(data={"filename": record.filename, "path": record.file_path, "record_id": record.id})


@router_strm.post("/upload-url", summary="通过URL上传115目录树文件")
async def upload_115_directory_tree_from_url(
    current_user: User = Depends(get_current_user), data: UrlUploadRequest = Body(...)
):
    """
    通过URL上传并处理115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **data**: 包含URL地址的请求体.
    """
    from app.log.log import log

    try:
        log.info(f"接收到URL上传请求，用户ID: {current_user.id}, URL: {data.url[:50]}...")
        record = await handle_url_upload(data.url, current_user)
        log.info(f"URL上传成功, 记录ID: {record.id}, 文件名: {record.filename}")
        return Success(data={"filename": record.filename, "path": record.file_path, "record_id": record.id})
    except HTTPException as e:
        log.error(f"URL上传失败，返回HTTP异常, 异常码: {e.code}, 详情: {e.msg}")
        raise
    except Exception as e:
        log.error(f"URL上传过程中发生未处理的异常: {str(e)}", exc_info=True)
        raise HTTPException(code=500, msg=f"URL上传处理失败: {str(e)}")


@router_strm.post("/parse", summary="解析已上传的115目录树文件")
async def parse_directory_tree_file(current_user: User = Depends(get_current_user), data: ParseRequest = Body(...)):
    """
    解析已上传的115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **data**: 包含上传记录ID的请求体.
    """
    result = await parse_uploaded_file(data.record_id)
    return Success(data=result)


@router_strm.get("/history", summary="获取上传历史记录", response_model=UploadHistoryResponse)
async def get_upload_history(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    """
    获取当前用户的文件上传历史记录。
    """
    skip = (page - 1) * page_size
    records = await UploadRecord.filter(uploader=current_user).offset(skip).limit(page_size).order_by("-create_time")
    total = await UploadRecord.filter(uploader=current_user).count()

    # 将 ORM 对象转换为字典以便可序列化为 JSON
    record_dicts = []
    for record in records:
        record_dict = {
            "id": record.id,
            "filename": record.filename,
            "filesize": record.filesize,
            "status": record.status.value,
            "create_time": record.create_time.isoformat(),
            "uploader_id": record.uploader_id,
            "parse_time": record.parse_time.isoformat() if record.parse_time else None,
        }
        record_dicts.append(record_dict)

    return Success(data={"total": total, "page": page, "page_size": page_size, "records": record_dicts})


@router_strm.delete("/history/{record_id}", summary="删除上传记录")
async def delete_history_record(
    record_id: int = Path(..., description="记录ID"), current_user: User = Depends(get_current_user)
):
    """
    删除上传记录及对应的文件。
    - **record_id**: 要删除的记录ID.
    - **current_user**: 当前已登录用户.
    """
    await delete_upload_record(record_id, current_user)
    return Success(data={"message": "记录已成功删除"})


@router_strm.get("/download/{record_id}", summary="下载已上传的文件", response_class=FileResponse)
async def download_file(
    request: Request,
    record_id: int = Path(..., description="记录ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载已上传的文件。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **record_id**: 要下载的记录ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="Authentication failed, valid token required")

    return await download_upload_file(record_id, current_user)


@router_strm.get("/result/{record_id}", summary="获取文件解析结果")
async def get_file_parse_result(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    """
    获取已解析文件的解析结果，支持按文件类型过滤和分页。
    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为10。
    """
    result = await get_parse_result(record_id, current_user, file_type, page, page_size)
    return Success(data=result)
