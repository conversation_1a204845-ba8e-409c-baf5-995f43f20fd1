"""
STRM管理模块API

注意：异常处理最佳实践
-------------------------------
本项目推荐使用自定义的HTTPException类而不是FastAPI的HTTPException类。

推荐用法:
```python
from app.core.exceptions import HTTPException
raise HTTPException(code="4001", msg="认证失败")
```

而不是:
```python
from fastapi import HTTPException
raise HTTPException(status_code=401, detail="认证失败")
```

系统已添加兼容层处理两种类型的异常，但为保持一致性，请尽量使用自定义HTTPException。
"""

from fastapi import APIRouter, Depends, UploadFile, File, Body, Query, Path, Request
from app.controllers.strm.upload import (
    handle_file_upload,
    parse_uploaded_file,
    delete_upload_record,
    download_upload_file,
    get_parse_result,
    handle_url_upload,
    get_directory_content,
    search_files,
)
from app.controllers.strm.task_controller import (
    create_strm_task,
    start_strm_task,
    get_task_status,
    get_user_tasks,
    download_strm_files,
    cancel_task,
    delete_task,
    get_task_logs,
)
from app.core.dependency import get_current_user, check_token, AuthControl
from app.models.system import User
from app.schemas.base import Success, SuccessExtra
from app.schemas.strm.schemas import (
    ParseRequest,
    UploadHistoryResponse,
    UrlUploadRequest,
    MediaServerResponse,
    StrmTaskCreate,
    StrmTaskResponse,
    SystemSettingsUpdate,
    SystemSettingsResponse,
    MediaServerBase,
)
from app.models.strm.upload import UploadRecord
from app.models.strm import MediaServer
from typing import List, Optional
from fastapi.responses import FileResponse
from app.core.exceptions import HTTPException
from app.controllers.strm import system_settings_controller
from app.controllers.strm.server_controller import server_controller

router_strm = APIRouter()


@router_strm.post("/upload", summary="上传115目录树文件")
async def upload_115_directory_tree_file(current_user: User = Depends(get_current_user), file: UploadFile = File(...)):
    """
    上传并处理115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **file**: 上传的文件，必须是.txt格式.
    """
    record = await handle_file_upload(file, current_user)
    return Success(data={"filename": record.filename, "path": record.file_path, "record_id": record.id})


@router_strm.post("/upload-url", summary="通过URL上传115目录树文件")
async def upload_115_directory_tree_from_url(
    current_user: User = Depends(get_current_user), data: UrlUploadRequest = Body(...)
):
    """
    通过URL上传并处理115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **data**: 包含URL地址的请求体.
    """
    from app.log.log import log

    try:
        log.info(f"接收到URL上传请求，用户ID: {current_user.id}, URL: {data.url[:50]}...")
        record = await handle_url_upload(data.url, current_user)
        log.info(f"URL上传成功, 记录ID: {record.id}, 文件名: {record.filename}")
        return Success(data={"filename": record.filename, "path": record.file_path, "record_id": record.id})
    except HTTPException as e:
        log.error(f"URL上传失败，返回HTTP异常, 异常码: {e.code}, 详情: {e.msg}")
        raise
    except Exception as e:
        log.error(f"URL上传过程中发生未处理的异常: {str(e)}", exc_info=True)
        raise HTTPException(code=500, msg=f"URL上传处理失败: {str(e)}")


@router_strm.post("/parse", summary="解析已上传的115目录树文件")
async def parse_directory_tree_file(current_user: User = Depends(get_current_user), data: ParseRequest = Body(...)):
    """
    解析已上传的115网盘导出的目录树文件 (.txt).
    - **current_user**: 当前已登录用户.
    - **data**: 包含上传记录ID的请求体.
    """
    result = await parse_uploaded_file(data.record_id)
    return Success(data=result)


@router_strm.get("/history", summary="获取上传历史记录", response_model=UploadHistoryResponse)
async def get_upload_history(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
):
    """
    获取当前用户的文件上传历史记录。
    """
    skip = (page - 1) * page_size
    records = await UploadRecord.filter(uploader=current_user).offset(skip).limit(page_size).order_by("-create_time")
    total = await UploadRecord.filter(uploader=current_user).count()

    # 将 ORM 对象转换为字典以便可序列化为 JSON
    record_dicts = []
    for record in records:
        record_dict = {
            "id": record.id,
            "filename": record.filename,
            "filesize": record.filesize,
            "status": record.status.value,
            "create_time": record.create_time.isoformat(),
            "uploader_id": record.uploader_id,
            "parse_time": record.parse_time.isoformat() if record.parse_time else None,
        }
        record_dicts.append(record_dict)

    return Success(data={"total": total, "page": page, "page_size": page_size, "records": record_dicts})


@router_strm.delete("/history/{record_id}", summary="删除上传记录")
async def delete_history_record(
    record_id: int = Path(..., description="记录ID"), current_user: User = Depends(get_current_user)
):
    """
    删除上传记录及对应的文件。
    - **record_id**: 要删除的记录ID.
    - **current_user**: 当前已登录用户.
    """
    await delete_upload_record(record_id, current_user)
    return Success(data={"message": "记录已成功删除"})


@router_strm.get("/download/{record_id}", summary="下载已上传的文件", response_class=FileResponse)
async def download_file(
    request: Request,
    record_id: int = Path(..., description="记录ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载已上传的文件。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **record_id**: 要下载的记录ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="Authentication failed, valid token required")

    return await download_upload_file(record_id, current_user)


@router_strm.get("/result/{record_id}", summary="获取文件解析结果")
async def get_file_parse_result(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    all_files: bool = Query(False, description="是否获取所有文件（不分页）"),
):
    """
    获取已解析文件的解析结果，支持按文件类型过滤和分页。
    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为10。
    - **all_files**: 是否返回所有文件（不进行分页），默认为false。
    """
    result = await get_parse_result(record_id, current_user, file_type, page, page_size, all_files)
    return Success(data=result)


@router_strm.get("/directory/{record_id}", summary="获取目录内容")
async def get_directory(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    directory_path: str = Query("/", description="目录路径，默认为根目录"),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
):
    """
    获取指定目录下的文件和子目录。
    此API采用懒加载方式，每次只返回指定目录下的直接文件和子目录，不会递归获取所有内容。

    - **record_id**: 要获取结果的记录ID。
    - **current_user**: 当前已登录用户。
    - **directory_path**: 目录路径，默认为根目录"/"。
    - **file_type**: 可选的文件类型过滤器，默认为"all"。
    - **page**: 页码，默认为1。
    - **page_size**: 每页数量，默认为20。
    """
    result = await get_directory_content(record_id, current_user, directory_path, file_type, page, page_size)
    return Success(data=result)


@router_strm.get("/search/{record_id}", summary="搜索文件")
async def search_parsed_files(
    record_id: int = Path(..., description="记录ID"),
    current_user: User = Depends(get_current_user),
    search_text: str = Query(..., description="搜索文本"),
    file_type: str = Query("all", description="文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)"),
    ignore_case: bool = Query(True, description="是否忽略大小写"),
):
    """
    搜索已解析文件中的匹配项
    - **record_id**: 记录ID
    - **current_user**: 当前用户
    - **search_text**: 要搜索的文本
    - **file_type**: 文件类型过滤器 (all, video, audio, image, subtitle, metadata, other)
    - **ignore_case**: 是否忽略大小写，默认为True
    """
    result = await search_files(record_id, current_user, search_text, file_type, ignore_case)
    return Success(data=result)


# === STRM生成相关API端点 ===


@router_strm.get("/servers", summary="获取媒体服务器列表")
async def get_media_servers(current_user: User = Depends(get_current_user)):
    """
    获取当前用户可用的媒体服务器列表
    """
    servers = await MediaServer.all()
    # 将 ORM 对象转换为字典
    server_list = []
    for server in servers:
        server_dict = {
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
        server_list.append(server_dict)

    return Success(data=server_list)


@router_strm.post("/generate", summary="创建STRM生成任务")
async def generate_strm(
    current_user: User = Depends(get_current_user),
    data: StrmTaskCreate = Body(...),
):
    """
    创建STRM生成任务。
    - **current_user**: 当前已登录用户。
    - **data**: 任务创建数据，包括解析记录ID、服务器ID等。
    """
    from app.log.log import log

    log.info(f"用户 {current_user.user_name} 请求创建STRM生成任务: {data.dict()}")
    # 首先创建任务记录
    try:
        # 检查服务器是否存在
        from app.models.strm import MediaServer

        server = await MediaServer.get_or_none(id=data.server_id)
        if not server:
            log.error(f"STRM任务创建失败: 找不到ID为{data.server_id}的媒体服务器")
            return Success(
                data={
                    "task_id": None,
                    "status": "failed",
                    "name": "创建失败",
                    "error": f"找不到ID为{data.server_id}的媒体服务器",
                }
            )

        # 如果指定了下载服务器，检查下载服务器是否存在
        if data.download_server_id:
            download_server = await MediaServer.get_or_none(id=data.download_server_id)
            if not download_server:
                log.error(f"STRM任务创建失败: 找不到ID为{data.download_server_id}的下载服务器")
                return Success(
                    data={
                        "task_id": None,
                        "status": "failed",
                        "name": "创建失败",
                        "error": f"找不到ID为{data.download_server_id}的下载服务器",
                    }
                )

        task = await create_strm_task(
            record_id=data.record_id,
            server_id=data.server_id,
            user=current_user,
            output_dir=data.output_dir,
            custom_name=data.name,
            download_server_id=data.download_server_id,
            download_resources=data.download_resources,
        )
        log.info(f"STRM任务创建成功，ID: {task.id}")

        # 异步启动任务，但不等待任务完成
        from app.core.bgtask import BgTasks

        # 将任务添加到后台队列，不等待执行结果
        await BgTasks.add_task(start_strm_task, task.id, current_user.id)
        log.info(f"已将STRM任务 {task.id} 添加到后台队列")

        # 立即返回任务信息，而不等待任务执行
        return Success(
            data={"task_id": task.id, "name": task.name, "status": task.status, "message": "任务已创建，正在后台处理中"}
        )
    except Exception as e:
        import traceback

        error_detail = traceback.format_exc()
        log.error(f"STRM任务创建失败: {str(e)}\n{error_detail}")
        # 返回错误信息，但确保有task_id字段，防止前端出错
        return Success(data={"task_id": None, "status": "failed", "name": "创建失败", "error": str(e)})


@router_strm.get("/tasks", summary="获取用户任务列表")
async def get_tasks(
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    search: Optional[str] = Query(None, description="按名称搜索"),
    status: Optional[str] = Query(None, description="按状态过滤"),
    start_date: Optional[str] = Query(None, description="开始日期过滤 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期过滤 (YYYY-MM-DD)"),
):
    """
    获取当前用户的STRM生成任务列表，支持按名称搜索和按状态/日期过滤
    """
    result = await get_user_tasks(
        current_user, page, page_size, search=search, status=status, start_date=start_date, end_date=end_date
    )
    return Success(data=result)


@router_strm.get("/task/{task_id}", summary="获取任务状态")
async def check_task_status(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    获取指定任务的状态和进度
    """
    result = await get_task_status(task_id, current_user)
    return Success(data=result)


@router_strm.post("/task/{task_id}/cancel", summary="取消任务")
async def cancel_task_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    取消正在进行的任务
    """
    result = await cancel_task(task_id, current_user)
    return Success(data=result)


@router_strm.delete("/task/{task_id}", summary="删除任务")
async def delete_task_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
):
    """
    删除任务。
    - **task_id**: 任务ID.
    - **current_user**: 当前已登录用户.
    """
    result = await delete_task(task_id, current_user)
    return Success(data=result)


@router_strm.get("/task/{task_id}/logs", summary="获取任务日志")
async def get_task_logs_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(50, description="每页数量"),
    level: Optional[str] = Query(None, description="日志级别过滤"),
    search: Optional[str] = Query(None, description="日志内容搜索"),
):
    """
    获取任务的详细日志记录。
    - **task_id**: 任务ID.
    - **current_user**: 当前已登录用户.
    - **page**: 页码，默认为1.
    - **page_size**: 每页数量，默认为50.
    - **level**: 日志级别过滤，可选值：INFO、ERROR等.
    - **search**: 日志内容搜索关键词.
    """
    logs = await get_task_logs(task_id, current_user, page, page_size, level, search)
    return Success(data=logs)


@router_strm.get("/download-strm/{task_id}", summary="下载生成的STRM文件", response_class=FileResponse)
async def download_strm_files_endpoint(
    request: Request,
    task_id: int = Path(..., description="任务ID"),
    token: Optional[str] = Query(None, description="认证令牌，可通过查询参数传递"),
):
    """
    下载生成的STRM文件（ZIP压缩包）。支持两种认证方式：
    1. 通过Authorization头传递Bearer token (标准方式)
    2. 通过URL查询参数传递token (便于直接下载)

    - **task_id**: 要下载文件的任务ID.
    - **token**: 可选参数，通过查询参数传递认证令牌.
    """
    # 尝试获取用户
    current_user = None

    # 方法1: 从请求头获取token
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        bearer_token = auth_header.replace("Bearer ", "")
        if bearer_token:
            status, code, decode_data = check_token(bearer_token)
            if status:
                if decode_data["data"]["tokenType"] == "accessToken":
                    user_id = decode_data["data"]["userId"]
                    current_user = await User.filter(id=user_id).first()

    # 方法2: 从查询参数获取token
    if current_user is None and token:
        status, code, decode_data = check_token(token)
        if status:
            if decode_data["data"]["tokenType"] == "accessToken":
                user_id = decode_data["data"]["userId"]
                current_user = await User.filter(id=user_id).first()

    # 如果没有通过任何方式获取到current_user，则认证失败
    if current_user is None:
        raise HTTPException(code="4001", msg="认证失败，需要有效的令牌")

    return await download_strm_files(task_id, current_user)


# === 系统设置相关API端点 ===

# === 服务器管理API端点 ===


@router_strm.post("/server", summary="创建媒体服务器")
async def create_media_server(data: MediaServerBase = Body(...), current_user: User = Depends(get_current_user)):
    """
    创建新的媒体服务器

    - **current_user**: 当前已登录用户
    - **data**: 服务器数据
    """
    server = await server_controller.create_server(data.dict())
    return Success(
        data={
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
    )


@router_strm.put("/server/{server_id}", summary="更新媒体服务器")
async def update_media_server(
    server_id: int = Path(..., description="服务器ID"),
    data: MediaServerBase = Body(...),
    current_user: User = Depends(get_current_user),
):
    """
    更新媒体服务器信息

    - **server_id**: 要更新的服务器ID
    - **data**: 更新的服务器数据
    - **current_user**: 当前已登录用户
    """
    server = await server_controller.update_server(server_id, data.dict())
    return Success(
        data={
            "id": server.id,
            "name": server.name,
            "server_type": server.server_type,
            "base_url": server.base_url,
            "description": server.description,
            "auth_required": server.auth_required,
            "create_time": server.create_time.isoformat(),
            "status": server.status,
        }
    )


@router_strm.delete("/server/{server_id}", summary="删除媒体服务器")
async def delete_media_server(
    server_id: int = Path(..., description="服务器ID"), current_user: User = Depends(get_current_user)
):
    """
    删除媒体服务器

    - **server_id**: 要删除的服务器ID
    - **current_user**: 当前已登录用户
    """
    await server_controller.remove(id=server_id)
    return Success(data={"message": "服务器已成功删除"})


@router_strm.post("/server/{server_id}/test", summary="测试服务器连接")
async def test_server_connection(
    server_id: int = Path(..., description="服务器ID"), current_user: User = Depends(get_current_user)
):
    """
    测试媒体服务器连接

    - **server_id**: 要测试的服务器ID
    - **current_user**: 当前已登录用户
    """
    result = await server_controller.test_connection(server_id)
    return Success(data=result)


@router_strm.post("/server/test", summary="测试未保存的服务器连接")
async def test_server_connection_without_save(
    data: MediaServerBase = Body(...), current_user: User = Depends(get_current_user)
):
    """
    测试未保存的媒体服务器连接

    - **data**: 服务器数据
    - **current_user**: 当前已登录用户
    """
    result = await server_controller.test_connection_without_save(data.dict())
    return Success(data=result)


@router_strm.get("/test-file-type", summary="测试文件类型检测")
async def test_file_type_detection_endpoint(current_user: User = Depends(get_current_user)):
    """
    测试文件类型检测功能，用于验证系统设置是否正确应用于文件类型识别。

    - **current_user**: 当前登录用户，必须拥有管理权限。

    返回不同配置下NFO文件的类型检测结果。
    """
    from app.utils.strm.parser import test_file_type_detection

    # 运行测试并返回结果
    results = await test_file_type_detection()
    return Success(data=results)
