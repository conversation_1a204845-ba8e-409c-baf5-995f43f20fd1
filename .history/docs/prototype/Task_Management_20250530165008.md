# 115STRM管理平台任务管理界面原型

## 任务队列界面设计

![任务队列界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/task-queue.png)

### 布局结构
```
+----------------------------------------------------------+
|                     任务队列                              |
|                                                          |
|  [+ 新建任务]  [运行状态▼]                [搜索框]        |
+----------------------------------------------------------+
|                                                          |
|                      任务列表                             |
|                                                          |
| +------------------------------------------------------+ |
| |  名称  |  类型  |  状态  |  进度  | 创建时间 |   操作   | |
| |--------|--------|--------|--------|---------|----------| |
| | 任务1  | STRM生成| 运行中 | 45%    | 2023-05-20| [操作▼]| |
| | 任务2  | STRM生成| 等待中 | 0%     | 2023-05-20| [操作▼]| |
| | 任务3  | STRM生成| 已完成 | 100%   | 2023-05-19| [操作▼]| |
| | 任务4  | STRM生成| 失败   | 35%    | 2023-05-18| [操作▼]| |
| | ...    | ...    | ...    | ...    | ...     | ...      | |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
| [批量操作▼]                 任务统计: 总计10 运行中1 等待中2 |
+----------------------------------------------------------+
```

### 组件说明

#### 顶部操作栏
- 新建任务按钮 - 打开新建任务表单
- 运行状态下拉过滤器 - 过滤不同状态的任务（全部、运行中、等待中、已完成、失败）
- 搜索框 - 按任务名称或创建者搜索

#### 任务列表表格
- 列：
  - 任务名称
  - 任务类型（STRM生成、文件解析等）
  - 状态（运行中、等待中、已完成、失败、已取消）
  - 进度（进度条和百分比）
  - 创建时间
  - 操作下拉菜单
- 支持排序和过滤
- 支持多选操作
- 任务状态使用不同颜色标识

#### 操作下拉菜单
- 查看详情 - 查看任务详细信息
- 暂停/继续 - 暂停或继续任务
- 取消 - 取消任务
- 重试 - 重试失败的任务
- 删除 - 删除任务（需确认）

#### 底部栏
- 批量操作下拉菜单 - 包含批量取消、批量删除等操作
- 任务统计信息 - 显示任务总数和各状态任务数量

### 交互说明
- 点击任务行可展开显示更多任务信息
- 运行中任务的进度条实时更新
- 任务完成或失败时可以显示通知
- 可以选择多个任务进行批量操作
- 任务列表支持自动刷新（可配置间隔）

## 任务详情界面设计

![任务详情界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/task-detail.png)

### 布局结构
```
+----------------------------------------------------------+
|                     任务详情                              |
+----------------------------------------------------------+
| +----------------------+ +-----------------------------+ |
| |                      | |                             | |
| |     基本信息          | |      任务状态               | |
| |                      | |                             | |
| +----------------------+ +-----------------------------+ |
+----------------------------------------------------------+
|                                                          |
|                      处理文件列表                         |
|                                                          |
| +------------------------------------------------------+ |
| |  文件名  |  路径  |  大小  |  状态  |  处理时间  |     | |
| |----------|--------|--------|--------|-----------|-----| |
| | 文件1.mp4 | /路径1 | 1.2GB  | 成功   | 10秒      |     | |
| | 文件2.mp4 | /路径2 | 2.3GB  | 处理中 | -         |     | |
| | 文件3.mp4 | /路径3 | 1.5GB  | 等待中 | -         |     | |
| | 文件4.mp4 | /路径4 | 0.8GB  | 失败   | 5秒       |     | |
| | ...       | ...    | ...    | ...    | ...       | ... | |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
|                                                          |
|                      日志信息                             |
|                                                          |
| +------------------------------------------------------+ |
| | [时间戳] [INFO] 任务启动                               | |
| | [时间戳] [INFO] 开始处理文件1.mp4                      | |
| | [时间戳] [INFO] 文件1.mp4处理成功                      | |
| | [时间戳] [ERROR] 文件4.mp4处理失败: 权限不足           | |
| | ...                                                    | |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
| [返回列表] [暂停/继续] [取消] [导出日志]                  |
+----------------------------------------------------------+
```

### 组件说明

#### 基本信息卡片
- 任务ID
- 任务名称
- 任务类型
- 创建时间
- 创建者
- 关联服务器
- 任务参数摘要

#### 任务状态卡片
- 当前状态（带状态图标）
- 进度条和百分比
- 开始时间
- 预计完成时间
- 已处理文件数/总文件数
- 成功/失败文件数
- 资源使用情况（CPU、内存等）

#### 处理文件列表表格
- 列：
  - 文件名
  - 文件路径
  - 文件大小
  - 处理状态（成功、处理中、等待中、失败）
  - 处理时间
- 支持排序和过滤
- 支持搜索特定文件
- 文件状态使用不同颜色标识

#### 日志信息面板
- 任务执行日志，包含：
  - 时间戳
  - 日志级别（INFO、WARNING、ERROR等）
  - 日志消息
- 支持日志级别过滤
- 支持自动滚动到最新日志
- 错误日志高亮显示

#### 底部按钮
- 返回列表 - 返回任务队列页面
- 暂停/继续 - 暂停或继续任务
- 取消 - 取消任务
- 导出日志 - 将任务日志导出到文件

### 交互说明
- 任务状态和进度实时更新
- 处理文件列表可以显示当前正在处理的文件
- 日志面板实时追加新日志
- 可以选择要查看的日志详细程度
- 任务完成后显示摘要信息和统计数据

## 定时任务界面设计

![定时任务界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/scheduled-tasks.png)

### 布局结构
```
+----------------------------------------------------------+
|                     定时任务                              |
|                                                          |
|  [+ 新建定时任务]  [状态▼]                [搜索框]        |
+----------------------------------------------------------+
|                                                          |
|                      定时任务列表                         |
|                                                          |
| +------------------------------------------------------+ |
| |  名称  |  类型  | 周期设置 |  状态  | 下次执行 |  操作  | |
| |--------|--------|----------|--------|----------|--------| |
| | 任务1  | STRM生成| 每天8:00 | 启用   | 2023-05-21| [操作▼]| |
| | 任务2  | STRM生成| 每周一   | 禁用   | -        | [操作▼]| |
| | 任务3  | STRM生成| 每月1日  | 启用   | 2023-06-01| [操作▼]| |
| | ...    | ...    | ...      | ...    | ...      | ...    | |
| +------------------------------------------------------+ |
|                                                          |
+----------------------------------------------------------+
| [批量启用] [批量禁用] [导入] [导出]                       |
+----------------------------------------------------------+
```

### 组件说明

#### 顶部操作栏
- 新建定时任务按钮 - 打开新建定时任务表单
- 状态下拉过滤器 - 过滤不同状态的定时任务（全部、启用、禁用）
- 搜索框 - 按任务名称或创建者搜索

#### 定时任务列表表格
- 列：
  - 任务名称
  - 任务类型（STRM生成、文件解析等）
  - 周期设置（每天、每周、每月、自定义cron表达式等）
  - 状态（启用/禁用）
  - 下次执行时间
  - 操作下拉菜单
- 支持排序和过滤
- 支持多选操作

#### 操作下拉菜单
- 编辑 - 打开编辑定时任务表单
- 立即执行 - 立即触发任务执行
- 启用/禁用 - 切换任务状态
- 查看历史 - 查看任务历史执行记录
- 删除 - 删除任务（需确认）

#### 底部按钮
- 批量启用 - 启用选中的任务
- 批量禁用 - 禁用选中的任务
- 导入 - 从文件导入定时任务配置
- 导出 - 将定时任务配置导出到文件

### 交互说明
- 点击任务行可展开显示更多任务信息
- 下次执行时间自动更新
- 任务执行时可以显示通知
- 可以选择多个任务进行批量操作
- 定时任务状态使用不同颜色标识

## 定时任务表单界面设计

![定时任务表单界面](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/scheduled-form.png)

### 布局结构
```
+----------------------------------------------------------+
|                   添加/编辑定时任务                       |
+----------------------------------------------------------+
|                                                          |
|  任务名称: [                                        ]    |
|                                                          |
|  任务类型: [  下拉选择                              ]    |
|                                                          |
|  +------------------------------------------------------+|
|  | 执行计划                                              ||
|  |                                                      ||
|  | 重复模式:                                            ||
|  | ( ) 一次性  ( ) 每天  ( ) 每周  ( ) 每月  ( ) 自定义  ||
|  |                                                      ||
|  | 执行时间: [时:分] [选择日期/星期/Cron表达式]          ||
|  |                                                      ||
|  | 结束条件:                                            ||
|  | ( ) 永不结束  ( ) 执行次数  ( ) 截止日期              ||
|  |                                                      ||
|  +------------------------------------------------------+|
|                                                          |
|  +------------------------------------------------------+|
|  | 任务配置                                              ||
|  |                                                      ||
|  | 服务器: [  下拉选择                              ]    ||
|  |                                                      ||
|  | 源文件: [                                        ]    ||
|  |                                                      ||
|  | 输出目录: [                                      ]    ||
|  |                                                      ||
|  | 使用高级设置: [ ] (勾选则使用高级设置)                ||
|  |                                                      ||
|  | 其他参数... (根据任务类型动态显示)                    ||
|  +------------------------------------------------------+|
|                                                          |
|  +------------------------------------------------------+|
|  | 通知设置                                              ||
|  |                                                      ||
|  | 发送完成通知: [ ] 成功 [ ] 失败                       ||
|  |                                                      ||
|  | 通知方式:                                            ||
|  | [ ] 站内通知  [ ] 邮件  [ ] 其他...                   ||
|  |                                                      ||
|  +------------------------------------------------------+|
|                                                          |
|  任务状态: ( ) 启用  ( ) 禁用                            |
|                                                          |
|  [取消]  [保存]  [保存并立即执行]                         |
+----------------------------------------------------------+
```

### 组件说明

#### 基本表单字段
- 任务名称输入框（必填）
- 任务类型下拉选择（STRM生成、文件解析等）
- 任务状态单选按钮组（启用/禁用）

#### 执行计划卡片
- 重复模式单选按钮组：
  - 一次性（指定日期和时间）
  - 每天（指定时间）
  - 每周（指定星期几和时间）
  - 每月（指定日期和时间）
  - 自定义（Cron表达式）
- 执行时间选择器（根据重复模式动态变化）
- 结束条件单选按钮组：
  - 永不结束
  - 执行指定次数
  - 到达指定日期

#### 任务配置卡片
- 服务器下拉选择
- 源文件选择（文本输入或浏览按钮）
- 输出目录选择（文本输入或浏览按钮）
- 使用高级设置复选框
- 任务特定参数（根据任务类型动态显示）

#### 通知设置卡片
- 发送完成通知复选框组（成功、失败）
- 通知方式复选框组（站内通知、邮件等）
- 通知接收人设置（如适用）

#### 底部按钮
- 取消 - 关闭表单，不保存更改
- 保存 - 保存定时任务
- 保存并立即执行 - 保存任务并立即触发一次执行

### 交互说明
- 重复模式选择会影响执行时间选择器的显示
- 任务类型选择会影响任务配置部分的字段显示
- 自定义Cron表达式提供帮助和验证
- 提供下次执行时间预览
- 保存前验证任务配置的有效性 