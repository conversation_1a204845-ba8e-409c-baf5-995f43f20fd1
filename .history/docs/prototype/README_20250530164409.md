# 115STRM管理平台原型设计导航

本文档提供了115STRM管理平台所有原型设计的导航，便于查看和了解整个平台的用户界面设计。

## 设计规范

- [设计规范文档](./115STRM_Prototype_Spec.md) - 包含UI设计规范、颜色方案和布局规则

## 主要界面原型

### 1. 基础布局

- [主界面布局与仪表盘](./Layout_Dashboard.md) - 系统的基础布局和首页仪表盘设计

### 2. 文件处理

- [文件处理界面](./File_Processing.md) - 包含文件上传、文件解析和文件预览界面

### 3. STRM生成

- [STRM生成界面](./STRM_Generation.md) - 包含基本生成、高级设置和下载管理界面

### 4. 服务器配置

- [服务器配置界面](./Server_Config.md) - 包含服务器管理和路径映射界面

### 5. 规则管理

- [规则管理界面](./Rule_Management.md) - 包含过滤规则和分类规则界面

### 6. 任务管理

- [任务管理界面](./Task_Management.md) - 包含任务队列和定时任务界面

### 7. 统计分析

- [统计分析界面](./Statistics_Analysis.md) - 包含操作日志和数据统计界面

## 原型图预览

下面提供了所有原型图的缩略图预览，点击可查看详情：

| 界面分类       | 原型图预览                                                                                                                                                                                                                                                                                                                                |
| -------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **基础布局**   | ![主界面布局与仪表盘](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/layout-thumb.png)                                                                                                                                                                                                                             |
| **文件处理**   | ![文件上传](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/file-upload-thumb.png) ![文件解析](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/file-parsing-thumb.png) ![文件预览](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/file-preview-thumb.png)              |
| **STRM生成**   | ![STRM基本生成](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/strm-basic-thumb.png) ![STRM高级设置](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/strm-advanced-thumb.png) ![STRM下载管理](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/strm-download-thumb.png) |
| **服务器配置** | ![服务器管理](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/server-management-thumb.png) ![路径映射](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/path-mapping-thumb.png)                                                                                                                |
| **规则管理**   | ![过滤规则](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/filter-rules-thumb.png) ![分类规则](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/category-rules-thumb.png)                                                                                                                     |
| **任务管理**   | ![任务队列](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/task-queue-thumb.png) ![定时任务](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/scheduled-tasks-thumb.png)                                                                                                                      |
| **统计分析**   | ![操作日志](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/operation-logs-thumb.png) ![数据统计](https://raw.githubusercontent.com/yourusername/115strm-prototype/main/statistics-thumb.png)                                                                                                                       |

## 用户流程图

以下是主要用户流程的示意图：

### STRM生成流程

```
文件上传 → 文件解析 → 文件预览 → STRM基本生成 → STRM下载
       ↓                               ↑
   历史文件                         高级设置
```

### 服务器和规则配置流程

```
服务器管理 → 路径映射规则配置
     ↓
过滤规则配置 → 分类规则配置
```

### 任务管理流程

```
创建任务 → 任务队列管理 → 查看任务详情
     ↓            ↑
定时任务设置 ------+
```

## 设计注意事项

1. 所有界面遵循统一的设计规范，包括颜色、字体和间距
2. 界面设计注重响应式布局，适应不同屏幕尺寸
3. 操作流程符合用户习惯，减少学习成本
4. 关键功能区域突出显示，提高可发现性
5. 错误提示清晰明确，帮助用户快速解决问题

## 下一步工作

1. 进行用户测试，收集反馈并优化设计
2. 根据测试结果调整交互流程
3. 完善细节设计，包括动画和过渡效果
4. 准备前端开发所需的切图和标注 