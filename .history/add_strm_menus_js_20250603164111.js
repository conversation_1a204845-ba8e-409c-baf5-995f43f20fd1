// 使用fetch API添加STRM相关菜单
const BASE_URL = "http://127.0.0.1:9999/api/v1";

// 登录并获取token
async function login(username, password) {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username,
        password
      })
    });
    
    const data = await response.json();
    if (data.code === "0000") {
      return data.data.token;
    } else {
      throw new Error(`登录失败: ${data.msg}`);
    }
  } catch (error) {
    console.error('登录错误:', error);
    throw error;
  }
}

// 创建菜单
async function createMenu(token, menuData) {
  try {
    const response = await fetch(`${BASE_URL}/system-manage/menus`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(menuData)
    });
    
    const data = await response.json();
    if (data.code === "0000") {
      return data.data.created_id;
    } else {
      console.error(`创建菜单失败: ${data.msg}`);
      return null;
    }
  } catch (error) {
    console.error('创建菜单错误:', error);
    return null;
  }
}

// 主函数
async function main() {
  // 获取用户凭证
  const username = prompt("请输入用户名:");
  const password = prompt("请输入密码:");
  
  if (!username || !password) {
    console.error('用户名和密码不能为空');
    return;
  }
  
  try {
    // 登录获取token
    const token = await login(username, password);
    console.log("登录成功，获取到token");
    
    // 创建STRM顶级菜单
    const strmMenuData = {
      menuType: "1",  // 目录
      menuName: "STRM管理",
      routeName: "strm",
      routePath: "/strm",
      icon: "mdi:file-upload-outline",
      iconType: "1",  // iconify图标
      i18nKey: "route.strm",
      parentId: 0,
      order: 3,
      statusType: "1",  // 启用
      hideInMenu: false,
      keepAlive: false,
      constant: false,
      query: [],
      byMenuButtons: []
    };
    
    const strmMenuId = await createMenu(token, strmMenuData);
    if (!strmMenuId) {
      console.error("创建STRM顶级菜单失败，程序退出");
      return;
    }
    
    console.log(`STRM顶级菜单创建成功，ID: ${strmMenuId}`);
    
    // 创建文件上传子菜单
    const uploadMenuData = {
      menuType: "2",  // 菜单
      menuName: "文件上传",
      routeName: "strm_upload",
      routePath: "/strm/upload",
      component: "layout.base$view.strm_upload",
      icon: "mdi:upload",
      iconType: "1",  // iconify图标
      i18nKey: "route.strm_upload",
      parentId: strmMenuId,
      order: 1,
      statusType: "1",  // 启用
      hideInMenu: false,
      keepAlive: false,
      constant: false,
      query: [],
      byMenuButtons: []
    };
    
    const uploadMenuId = await createMenu(token, uploadMenuData);
    if (uploadMenuId) {
      console.log(`文件上传菜单创建成功，ID: ${uploadMenuId}`);
    } else {
      console.error("文件上传菜单创建失败");
    }
    
    // 创建任务管理子菜单
    const tasksMenuData = {
      menuType: "2",  // 菜单
      menuName: "任务管理",
      routeName: "strm_tasks",
      routePath: "/strm/tasks",
      component: "layout.base$view.strm_tasks",
      icon: "mdi:playlist-check",
      iconType: "1",  // iconify图标
      i18nKey: "route.strm_tasks",
      parentId: strmMenuId,
      order: 2,
      statusType: "1",  // 启用
      hideInMenu: false,
      keepAlive: false,
      constant: false,
      query: [],
      byMenuButtons: []
    };
    
    const tasksMenuId = await createMenu(token, tasksMenuData);
    if (tasksMenuId) {
      console.log(`任务管理菜单创建成功，ID: ${tasksMenuId}`);
    } else {
      console.error("任务管理菜单创建失败");
    }
    
    console.log("所有菜单创建完成，请刷新页面查看效果");
    
  } catch (error) {
    console.error(`发生错误: ${error.message}`);
  }
}

// 执行主函数
main().catch(console.error); 