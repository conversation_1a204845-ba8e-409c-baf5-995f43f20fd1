<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>CNB谷歌助手</title>
  <link rel="stylesheet" href="styles/sidepanel.css">
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>CNB谷歌助手</h1>
      <button id="resetToken" class="reset-btn" style="display: none;">重置Token</button>
    </div>

    <div id="tokenSection" class="section">
      <div class="input-group">
        <label for="cnbToken">CNB Token:</label>
        <input type="password" id="cnbToken" placeholder="请输入CNB Token">
        <button id="verifyToken" class="primary-btn">验证Token</button>
      </div>
      <div>
        <div>您好，欢迎使用CNB谷歌助手</div>
        <div>
          如果您还没有创建CNBTOKEN
          <div>请点击链接访问 <a href="https://cnb.cool/profile/token" target="_blank">个人中心创建访问令牌</a> 谢谢!</div>
        </div>
      </div>
    </div>

    <div id="mainContent" style="display: none;">
      <!-- 导航菜单 -->
      <div class="nav-menu">
        <button class="nav-btn active" data-target="syncModule">镜像同步</button>
        <button class="nav-btn" data-target="bookmarkModule">书签同步</button>
        <button class="nav-btn" data-target="settingsModule">设置</button>
      </div>

      <!-- 镜像同步模块 -->
      <div id="syncModule" class="module-content">
        <div class="input-group">
          <label for="imageName">镜像名称:</label>
          <input type="text" id="imageName" placeholder="例如: nginx:latest">
        </div>

        <div class="input-group">
          <label for="repository">选择仓库:</label>
          <div class="repository-select-group">
            <select id="repository"></select>
            <button id="setDefaultRepo" class="secondary-btn">设为默认</button>
          </div>
        </div>

        <div class="input-group">
          <label>镜像架构:</label>
          <div class="checkbox-group">
            <label>
              <input type="checkbox" id="amd64" value="amd64" checked>
              amd64
            </label>
            <label>
              <input type="checkbox" id="arm64" value="arm64">
              arm64
            </label>
          </div>
        </div>

        <button id="syncButton" class="primary-btn">开始同步</button>

        <div id="packageSection" class="section">
          <h2>镜像清单<span style="font-size: 10px; color: #666;">仅展示最近20条(镜像默认下载最新上传的)</span></h2>
          <div id="packageList"></div>
        </div>

        <div id="historySection" class="section">
          <h2>构建历史<span style="font-size: 10px; color: #666;">仅展示最近20条(点击对应构建历史可跳转)</span></h2>
          <div id="historyList"></div>
        </div>
      </div>

      <!-- 书签同步模块 -->
      <div id="bookmarkModule" class="module-content" style="display: none;">
        <div class="input-group">
          <label for="organization">选择组织:</label>
          <select id="organization" class="full-width">
            <option value="">请选择组织</option>
          </select>
        </div>

        <!-- <div class="input-group">
          <label for="bookmarkRepo">选择仓库:</label>
          <select id="bookmarkRepo" class="full-width">
            <option value="">请选择仓库</option>
          </select>
        </div> -->

        <div class="input-group">
          <label for="bookmarkPath">生成的文件:</label>
          <input type="text" id="bookmarkPath" value="mybookmarks/data.json" placeholder="默认: 组织/仓库/data.json">
        </div>

        <div class="checkbox-group vertical">
          <label>
            <input type="checkbox" id="rememberSettings" checked>
            记住这些设置
          </label>
          <label>
            <input type="checkbox" id="autoUpload">
            书签变动时自动上传
          </label>
        </div>

        <div class="button-group">
          <button id="uploadBookmarks" class="primary-btn">上传书签</button>
          <button id="syncBookmarks" class="primary-btn">同步书签</button>
        </div>

        <div id="bookmarkStatus" class="status-section" style="display: none;">
          <h3>同步状态</h3>
          <div class="status-content">
            <!-- 这里将显示同步状态和结果 -->
          </div>
        </div>
      </div>

      <!-- 设置模块 -->
      <div id="settingsModule" class="module-content" style="display: none;">
        <div class="settings-group">
          <h3>基本设置</h3>
          <!-- 设置选项将在这里添加 -->
        </div>
      </div>
    </div>

    <div id="messageContainer" class="message-container"></div>
  </div>

  <script src="scripts/sidepanel.js" type="module"></script>
</body>
</html> 