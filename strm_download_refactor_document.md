# STRM下载逻辑重构文档

## 变更概述

本次重构对STRM文件生成和资源下载功能进行了统一处理，主要变更包括：

1. **统一任务管理**：不再区分STRM生成任务和资源下载任务，统一使用`StrmTask`管理所有任务
2. **智能文件类型处理**：根据文件类型自动决定处理方式
   - 视频文件 → 生成STRM文件
   - 音频、图片、字幕、元数据文件 → 下载资源文件
3. **简化API接口**：删除冗余API端点，简化前端调用逻辑
4. **优化数据模型**：删除不必要的模型和字段，添加必要的关联字段

## 变更原因

1. **简化用户操作**：原系统要求用户手动分别创建STRM生成任务和资源下载任务，增加了操作负担
2. **减少代码冗余**：原系统在任务处理逻辑上存在大量重复代码
3. **优化处理流程**：通过统一任务处理，减少数据库操作次数，提高处理效率
4. **提升用户体验**：用户无需区分文件类型，系统自动处理不同类型的文件

## 技术细节

### 数据模型变更

1. **枚举类型修改**：
   - `TaskType` → `ProcessType`（表示处理类型而非任务类型）

2. **StrmTask模型变更**：
   - 移除 `task_type`（不再区分任务类型）
   - 移除 `resource_download_created`（不再需要标记是否创建了资源下载任务）
   - 添加 `download_server`（直接关联下载服务器）
   - 添加 `threads`（下载线程数）

3. **DownloadTask模型变更**：
   - 将 `task_type` 改为 `process_type`（表示处理类型而非任务类型）
   - 移除 `DownloadTaskType` 枚举，使用 `ProcessType`

4. **删除模型**：
   - 删除 `ResourceDownloadTask` 模型（功能并入 `StrmTask`）

### API变更

1. **移除的API端点**：
   - `/strm/resource-download`（POST）- 创建资源下载任务
   - `/strm/resource-tasks`（GET）- 获取资源下载任务列表
   - `/strm/resource-task/{task_id}`（GET）- 获取资源下载任务状态
   - `/strm/resource-task/{task_id}/cancel`（POST）- 取消资源下载任务
   - `/strm/resource-task/{task_id}/logs`（GET）- 获取资源下载任务日志
   - `/strm/resource-task/{task_id}`（DELETE）- 删除资源下载任务

2. **修改的API端点**：
   - `/strm/generate`（POST）- 创建STRM处理任务
     - 移除 `download_resources` 参数
     - 添加 `threads` 参数

3. **请求模型变更**：
   - 更新 `StrmTaskCreate`
   - 删除 `ResourceDownloadTaskCreate`

### 处理逻辑变更

1. **任务创建流程**：
   - 验证媒体服务器和下载服务器
   - 创建单一的 `StrmTask` 记录

2. **任务启动流程**：
   - 解析文件列表
   - 根据文件类型创建 `DownloadTask` 任务项
   - 启动统一处理

3. **文件处理流程**：
   - 视频文件生成STRM
   - 其他文件类型下载资源
   - 并发处理不同类型文件

## 部署指南

### 预备工作

1. **备份数据库**：
   ```bash
   sqlite3 your_database.db .dump > backup.sql
   ```

2. **备份代码**：
   ```bash
   git commit -am "Pre-refactor backup"
   git tag pre-refactor-backup
   ```

### 数据库迁移

需要执行以下SQL语句更新数据库结构：

1. **修改StrmTask表**：
   ```sql
   -- 添加新字段
   ALTER TABLE strm_tasks ADD COLUMN download_server_id INTEGER REFERENCES strm_media_servers(id);
   ALTER TABLE strm_tasks ADD COLUMN threads INTEGER DEFAULT 1;
   
   -- 删除旧字段（SQLite不支持直接删除列，需要创建新表并迁移数据）
   CREATE TABLE strm_tasks_new (
     id INTEGER PRIMARY KEY,
     name TEXT NOT NULL,
     status TEXT NOT NULL,
     server_id INTEGER NOT NULL REFERENCES strm_media_servers(id),
     download_server_id INTEGER REFERENCES strm_media_servers(id),
     source_file TEXT NOT NULL,
     output_dir TEXT NOT NULL,
     total_files INTEGER NOT NULL DEFAULT 0,
     processed_files INTEGER NOT NULL DEFAULT 0,
     success_files INTEGER NOT NULL DEFAULT 0,
     failed_files INTEGER NOT NULL DEFAULT 0,
     start_time TIMESTAMP,
     end_time TIMESTAMP,
     created_by_id INTEGER NOT NULL REFERENCES users(id),
     log_file TEXT,
     log_content TEXT,
     download_duration REAL,
     threads INTEGER DEFAULT 1,
     create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
     update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
   );
   
   INSERT INTO strm_tasks_new SELECT 
     id, name, status, server_id, NULL, source_file, output_dir, 
     total_files, processed_files, success_files, failed_files, 
     start_time, end_time, created_by_id, log_file, log_content, 
     download_duration, 1, create_time, update_time 
   FROM strm_tasks;
   
   DROP TABLE strm_tasks;
   ALTER TABLE strm_tasks_new RENAME TO strm_tasks;
   ```

2. **修改DownloadTask表**：
   ```sql
   -- 修改字段（SQLite不支持直接修改列，需要创建新表并迁移数据）
   CREATE TABLE strm_download_tasks_new (
     id INTEGER PRIMARY KEY,
     task_id INTEGER NOT NULL REFERENCES strm_tasks(id),
     source_path TEXT NOT NULL,
     target_path TEXT,
     file_type TEXT NOT NULL,
     process_type TEXT NOT NULL,
     status TEXT NOT NULL DEFAULT 'pending',
     priority INTEGER NOT NULL DEFAULT 0,
     attempt_count INTEGER NOT NULL DEFAULT 0,
     max_attempts INTEGER NOT NULL DEFAULT 3,
     file_size BIGINT,
     download_started TIMESTAMP,
     download_completed TIMESTAMP,
     download_duration REAL,
     download_speed REAL,
     worker_id TEXT,
     error_message TEXT,
     retry_after TIMESTAMP,
     create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
     update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
   );
   
   -- 将旧数据迁移到新表，将task_type重命名为process_type
   INSERT INTO strm_download_tasks_new SELECT 
     id, task_id, source_path, target_path, file_type, 
     task_type, status, priority, attempt_count, max_attempts, 
     file_size, download_started, download_completed, download_duration, 
     download_speed, worker_id, error_message, retry_after, 
     create_time, update_time 
   FROM strm_download_tasks;
   
   DROP TABLE strm_download_tasks;
   ALTER TABLE strm_download_tasks_new RENAME TO strm_download_tasks;
   ```

3. **创建索引**：
   ```sql
   CREATE INDEX idx_strm_tasks_download_server ON strm_tasks(download_server_id);
   CREATE INDEX idx_download_tasks_process_type ON strm_download_tasks(process_type);
   ```

### 部署步骤

1. **停止服务**：
   ```bash
   systemctl stop your-service-name
   ```

2. **更新代码**：
   ```bash
   git pull
   # 或者 部署新的代码包
   ```

3. **执行数据库迁移**：
   使用上述SQL语句更新数据库结构

4. **更新依赖**：
   ```bash
   pip install -r requirements.txt
   ```

5. **启动服务**：
   ```bash
   systemctl start your-service-name
   ```

6. **监控日志**：
   ```bash
   tail -f /path/to/log/file
   ```

## 前端更新

前端团队需要参考 `frontend_changes_guide.md` 文件更新前端组件。主要变更包括：

1. 更新任务创建表单，移除任务类型选择
2. 更新API调用，使用新的参数格式
3. 合并任务列表展示
4. 优化任务状态展示

## 回滚计划

如果部署后出现严重问题，可按以下步骤回滚：

1. **停止服务**：
   ```bash
   systemctl stop your-service-name
   ```

2. **恢复代码**：
   ```bash
   git checkout pre-refactor-backup
   ```

3. **恢复数据库**：
   ```bash
   sqlite3 your_database.db < backup.sql
   ```

4. **启动服务**：
   ```bash
   systemctl start your-service-name
   ```

## 测试方案

1. **单元测试**：
   - 测试新的任务创建和启动逻辑
   - 测试文件类型判断逻辑

2. **集成测试**：
   - 测试API端点的请求和响应
   - 测试任务状态更新流程

3. **端到端测试**：
   - 测试完整的任务创建和处理流程
   - 验证STRM文件生成和资源下载功能

## 注意事项

1. 此次重构不兼容旧版API，前端必须同步更新
2. 旧的任务数据仍可查看，但新任务将使用新的处理流程
3. 如有旧版任务未完成，建议在部署前完成或取消这些任务
4. 监控系统CPU和内存使用情况，调整线程数以优化性能 