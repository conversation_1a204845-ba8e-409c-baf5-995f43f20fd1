# 失败文件错误信息为空问题修复总结

## 问题描述

在任务管理详情页文件列表中，发现有些失败的文件 `error_message` 字段为空，导致用户无法了解具体的失败原因。

## 问题分析

通过代码分析，发现以下几种情况会导致失败文件的 `error_message` 为空：

### 1. 任务取消时未设置错误信息

**位置**: `app/controllers/strm/task_controller.py` 第990-993行

**问题代码**:
```python
for dl_task in pending_downloads:
    dl_task.status = DownloadTaskStatus.CANCELED
    await dl_task.save()  # 只设置状态，未设置错误信息
```

### 2. 处理器中的取消检查未设置错误信息

**位置**: `app/utils/strm/processor.py` 第418-422行和第895-899行

**问题代码**:
```python
if main_task.status == TaskStatus.CANCELED:
    task.status = DownloadTaskStatus.CANCELED
    await task.save()  # 只设置状态，未设置错误信息
    return False
```

### 3. 重试机制可能导致错误信息丢失

**位置**: `app/utils/strm/processor.py` 第428行

**问题代码**:
```python
task.error_message = None  # 重试时清空错误信息
```

如果重试后仍然失败，但没有正确设置新的错误信息，就会导致 `error_message` 为空。

## 解决方案

### 1. 后端修复

#### 1.1 修复任务取消逻辑

**文件**: `app/controllers/strm/task_controller.py`

```python
# 修改前
for dl_task in pending_downloads:
    dl_task.status = DownloadTaskStatus.CANCELED
    await dl_task.save()

# 修改后
for dl_task in pending_downloads:
    dl_task.status = DownloadTaskStatus.CANCELED
    dl_task.error_message = "任务被用户取消"
    await dl_task.save()
```

#### 1.2 修复处理器中的取消检查

**文件**: `app/utils/strm/processor.py`

```python
# 修改前
if main_task.status == TaskStatus.CANCELED:
    task.status = DownloadTaskStatus.CANCELED
    await task.save()
    return False

# 修改后
if main_task.status == TaskStatus.CANCELED:
    task.status = DownloadTaskStatus.CANCELED
    task.error_message = "主任务已被取消"
    await task.save()
    return False
```

### 2. 前端优化

#### 2.1 增强错误信息显示

**文件**: `web/src/components/strm/TaskFileList.vue`

添加了以下工具函数：

- `shouldShowErrorCard()`: 判断是否应该显示错误卡片
- `getErrorCardTitle()`: 获取错误卡片标题
- `getErrorMessage()`: 获取错误信息，为空时提供默认信息

#### 2.2 错误信息处理逻辑

```javascript
const getErrorMessage = (file: any): string => {
  if (!file) return '未知错误';
  
  const status = file.status || 'unknown';
  const errorMessage = file.error_message;
  
  // 如果有具体的错误信息，直接返回
  if (errorMessage && errorMessage.trim()) {
    return errorMessage;
  }
  
  // 根据状态返回默认信息
  switch (status) {
    case 'failed':
      return '文件处理失败，但未记录具体错误信息';
    case 'canceled':
      return '任务已被取消';
    default:
      return '未知错误';
  }
};
```

### 3. 数据库修复脚本

创建了 `update_empty_error_messages.py` 脚本来修复现有的空错误信息记录：

```bash
python update_empty_error_messages.py
```

该脚本会：
1. 查找所有状态为失败或取消但 `error_message` 为空的记录
2. 为失败记录设置默认错误信息："文件处理失败，但未记录具体错误信息"
3. 为取消记录设置默认错误信息："任务已被取消"
4. 验证更新结果

## 状态说明

修复后，系统支持以下状态和对应的错误信息处理：

| 状态 | 显示文本 | 错误信息处理 |
|------|----------|-------------|
| `completed` | ✅ 成功 | 无错误信息 |
| `failed` | ❌ 失败 | 显示具体错误或默认信息 |
| `canceled` | 🚫 已取消 | 显示取消原因 |
| `downloading` | ⏳ 处理中 | 无错误信息 |
| `pending` | ⏸️ 等待中 | 无错误信息 |

## 预期效果

修复后：
1. 所有失败和取消的文件都会有相应的错误信息
2. 用户可以清楚地了解文件处理失败或取消的原因
3. 提升用户体验和问题排查效率

## 测试建议

1. 创建新任务并取消，检查取消的文件是否有错误信息
2. 模拟文件处理失败，检查失败的文件是否有错误信息
3. 运行数据库修复脚本，检查现有记录是否被正确更新
4. 在前端查看文件详情，确认错误信息显示正常
