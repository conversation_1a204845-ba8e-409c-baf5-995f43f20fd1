# 任务详情页面重新设计

## 设计目标

对任务详情页面进行全面重新设计，使其更加美观、合理，提升用户体验。主要目标包括：

1. 更现代化的视觉设计
2. 更清晰的信息层次结构
3. 更直观的数据展示方式
4. 更好的响应式布局
5. 更统一的设计语言

## 主要改进

### 1. 页面布局重构

- **标题区域**：重新设计了标题区域，添加了图标和更清晰的任务ID展示
- **状态横幅**：添加了醒目的状态横幅，直观展示任务状态和进度
- **卡片布局**：采用现代化的卡片布局，将信息分组展示
- **标签页优化**：优化了标签页设计，添加了图标，提升可识别性

### 2. 统计信息展示

- **统计卡片**：将原来的网格布局改为更直观的统计卡片
- **分组展示**：将统计信息分为"文件处理统计"和"输出文件统计"两组
- **视觉区分**：使用不同颜色和图标区分不同类型的统计数据
- **悬停效果**：添加卡片悬停效果，提升交互体验

### 3. 任务信息展示

- **信息卡片**：将基本信息改为卡片式布局
- **分类展示**：将信息分为"基本信息"和"时间信息"两类
- **布局优化**：采用左右对齐的布局，提升可读性
- **视觉层次**：优化了标题和内容的视觉层次

### 4. 文件列表优化

- **工具栏**：重新设计了文件列表工具栏
- **搜索过滤**：优化了搜索和过滤控件的布局和样式
- **表格样式**：改进了表格视图的样式，添加了行状态颜色
- **空状态**：优化了空状态和加载状态的展示

### 5. 响应式设计

- **移动适配**：优化了在不同屏幕尺寸下的显示效果
- **布局调整**：在小屏幕上自动调整为垂直布局
- **元素大小**：根据屏幕尺寸调整元素大小和间距
- **交互优化**：确保在移动设备上也有良好的交互体验

## 技术实现

### 1. 组件结构优化

```vue
<n-modal v-model:show="showTaskDetailModal" preset="card" :bordered="false" size="huge"
  style="max-width: 1200px; width: 95vw; max-height: 90vh;">
  <template #header>
    <!-- 优化的标题区域 -->
    <div class="task-detail-header">
      <div class="task-header-main">
        <div class="task-title">
          <n-icon size="24" class="task-icon">
            <Icon icon="mdi:cog-play" />
          </n-icon>
          <span class="task-name">{{ currentTask?.name || '任务详情' }}</span>
        </div>
        <div class="task-meta">
          <n-tag size="small" type="info" :bordered="false">
            <template #icon>
              <n-icon><Icon icon="mdi:identifier" /></n-icon>
            </template>
            ID: {{ currentTask?.id }}
          </n-tag>
          <TaskStatusDisplay :status="currentTask?.status" :taskType="currentTask?.task_type" />
        </div>
      </div>
    </div>
  </template>
  
  <!-- 内容区域 -->
  <n-tabs type="line" animated class="task-detail-tabs">
    <n-tab-pane name="overview" tab="📊 任务概览">
      <!-- 状态横幅 -->
      <div class="task-status-banner" :class="`status-${currentTask.status?.toLowerCase()}`">
        <!-- 状态信息 -->
      </div>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <!-- 文件处理统计 -->
        <div class="stat-card-group">
          <!-- 统计卡片 -->
        </div>
        
        <!-- 输出文件统计 -->
        <div class="stat-card-group">
          <!-- 统计卡片 -->
        </div>
      </div>

      <!-- 任务详细信息 -->
      <div class="task-info-section">
        <!-- 信息卡片 -->
      </div>
    </n-tab-pane>

    <n-tab-pane name="files" tab="📁 文件列表">
      <!-- 文件列表工具栏 -->
      <div class="files-toolbar">
        <!-- 搜索和过滤控件 -->
      </div>

      <!-- 文件列表内容 -->
      <div class="files-content">
        <!-- 表格视图 -->
        <!-- 树形视图 -->
      </div>
    </n-tab-pane>
  </n-tabs>
</n-modal>
```

### 2. 辅助函数

添加了多个辅助函数来支持新的设计：

```javascript
// 获取状态图标
const getStatusIcon = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': 'mdi:clock-outline',
    'RUNNING': 'mdi:cog-play',
    'SUCCESS': 'mdi:check-circle',
    'COMPLETED': 'mdi:check-circle',
    'FAILED': 'mdi:close-circle',
    'CANCELLED': 'mdi:cancel'
  };
  return statusMap[status] || 'mdi:help-circle';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '等待中',
    'RUNNING': '处理中',
    'SUCCESS': '已完成',
    'COMPLETED': '已完成',
    'FAILED': '处理失败',
    'CANCELLED': '已取消'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态描述
const getStatusDescription = (status: string) => {
  const statusMap: Record<string, string> = {
    'PENDING': '任务正在等待系统资源分配',
    'RUNNING': '任务正在处理中，请耐心等待',
    'SUCCESS': '所有文件已成功处理完成',
    'COMPLETED': '任务已完成，可以查看结果',
    'FAILED': '任务处理过程中出现错误',
    'CANCELLED': '任务已被用户或系统取消'
  };
  return statusMap[status] || '状态信息未知';
};

// 重置文件过滤器
const resetFileFilters = () => {
  fileSearchText.value = '';
  fileStatusFilter.value = null;
};

// 获取文件行的样式类
const getFileRowClass = (row: any) => {
  if (row.is_success === false) return 'file-row-error';
  if (row.is_success === true) return 'file-row-success';
  return '';
};
```

### 3. CSS样式

添加了大量新的CSS样式来支持新设计：

- 任务头部和状态横幅样式
- 统计卡片和分组样式
- 信息卡片和内容样式
- 文件列表工具栏和内容样式
- 响应式布局样式

## 预期效果

1. **视觉体验**：更现代、更美观的界面设计
2. **信息获取**：用户可以更快速地获取关键信息
3. **操作便捷**：更直观的操作界面，减少用户学习成本
4. **移动友好**：在各种设备上都有良好的使用体验
5. **一致性**：与系统其他部分保持设计语言的一致性

## 后续优化方向

1. 添加任务处理时间线展示
2. 优化文件树形视图的实现
3. 添加文件处理详情展示
4. 增强任务操作功能
5. 添加数据可视化图表
